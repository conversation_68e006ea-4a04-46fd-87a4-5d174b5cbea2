<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:versionCode="1"
    android:versionName="1.0">

    <uses-sdk android:targetSdkVersion="29" />

    <application android:label="@string/NewSoundRecorder_test_app_name">
        <uses-library
            android:name="androidx.test.runner"
            android:required="false" />
    </application>

    <instrumentation
        android:name="androidx.test.runner.AndroidJUnitRunner"
        android:functionalTest="false"
        android:label="@string/test_for_NewSoundRecorder_api"
        android:targetPackage="com.coloros.soundrecorder">
    </instrumentation>

    <instrumentation
        android:name="com.oppo.autotest.olt.testlib.common.OPPORunner"
        android:functionalTest="false"
        android:label="@string/test_for_NewSoundRecorder_api"
        android:targetPackage="com.coloros.soundrecorder">
        <meta-data
            android:name="listener"
            android:value="com.oppo.autotest.olt.testlib.common.TestRunListener" />
    </instrumentation>

</manifest>
