<?xml version="1.0" encoding="utf-8"?><!--
/*********************************************************************************
 ** Copyright (C), 2008-2015, OPLUS Mobile Comm Corp., Ltd
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - coui_checkbox_state.xml
 ** Description:
 **     the states drawable of the checkBox are defined three states
 **
 ** Version: 1.0
 ** Date: 2014-03-26
 ** Author: <EMAIL>
 **
 ** =============================== Revision History: ============================
 ** <author>                        <date>       <version>   <desc>
 ** ==============================================================================
 ** <EMAIL>              2014-03-26   1.0         Create this moudle
 ********************************************************************************/
-->
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:coui="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/selected_normal"
        android:drawable="@drawable/coui_btn_check_on_normal"
        coui:coui_state_allSelect="true"
        coui:coui_state_partSelect="false" />
    <item
        android:id="@+id/unselected_normal"
        android:drawable="@drawable/coui_btn_check_off_normal"
        coui:coui_state_allSelect="false"
        coui:coui_state_partSelect="false" />

    <transition
        android:drawable="@drawable/coui_checkbox_selected_to_unselected"
        android:fromId="@+id/selected_normal"
        android:toId="@+id/unselected_normal" />
    <transition
        android:drawable="@drawable/coui_checkbox_unselected_to_selected"
        android:fromId="@+id/unselected_normal"
        android:toId="@+id/selected_normal" />

</animated-selector>