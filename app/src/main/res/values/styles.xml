<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <style name="ActionBarTabTextStyle" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textColor">@color/coui_textview_default_normal</item>
        <item name="android:textStyle">bold</item>
        <item name="android:ellipsize">end</item>
        <item name="android:singleLine">true</item>
        <item name="android:textSize">@dimen/sp22</item>
    </style>

    <style name="ActionBarStyle" parent="Base.Widget.AppCompat.ActionBar">
        <item name="android:background">@drawable/actionbar_bg</item>
        <item name="android:backgroundStacked">@null</item>
        <item name="android:titleTextStyle">@style/ActionBarTitleStyle</item>
    </style>

    <style name="ActionBarTitleStyle">
        <item name="android:textColor">@color/coui_textview_default_normal</item>
    </style>

    <style name="Widget.OPLUS.ActionMode" parent="android:Widget.ActionBar">
        <item name="android:background">@color/list_bg_white</item>
        <item name="android:backgroundSplit">@null</item>
        <item name="android:height">?android:attr/actionBarSize</item>
        <item name="android:titleTextStyle">@style/ActionModeBarTitleStyle</item>
    </style>

    <style name="ActionModeBarTitleStyle">
        <item name="android:textSize">@dimen/sp15</item>
        <item name="android:textColor">@color/coui_textview_default_normal</item>
    </style>

    <declare-styleable name="VociceWave">
        <attr name="wave_period" format="float" />
        <attr name="wave_min_accelerated_speed" format="float" />
        <attr name="wave_max_accelerated_speed" format="float" />
        <attr name="wave_amplitude" format="dimension" />
        <attr name="wave_length" format="dimension" />
    </declare-styleable>

    <style name="actionModeStyle">
        <item name="background">?attr/couiColorBackground</item>
        <item name="height">44dp</item>
    </style>

    <style name="ActionButtonStyle" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingLeft">12dip</item>
        <item name="android:paddingRight">12dip</item>
        <item name="android:minWidth">42dp</item>
    </style>

    <style name="ColorTabLayoutStyle">
        <item name="android:layout_height">43dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="couiTabMode">fixed</item>
        <item name="couiTabGravity">fill</item>
        <item name="couiTabIndicatorWidthRatio">0</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="couiTabPaddingStart">8dp</item>
        <item name="couiTabPaddingEnd">8dp</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:fadingEdge">horizontal</item>
        <item name="android:requiresFadingEdge">horizontal</item>
        <item name="android:fadingEdgeLength">20dp</item>
        <item name="couiTabIndicatorBackgroundHeight">@dimen/tablayout_selected_indicator_height
        </item>
        <item name="couiTabIndicatorBackgroundColor">@color/tablayout_indicator_background</item>
        <item name="couiTabIndicatorBackgroundPaddingLeft">24dp</item>
        <item name="couiTabIndicatorBackgroundPaddingRight">24dp</item>
        <item name="couiTabIndicatorColor">@color/couiRedTintControlNormal</item>
        <item name="couiTabAutoResize">true</item>
        <item name="couiTabIndicatorHeight">@dimen/tablayout_selected_indicator_height</item>
        <item name="couiTabPaddingBottom">@dimen/tablayout_selected_indicator_height</item>
        <item name="couiTabTextAppearance">@style/COUILargestTabViewStyle</item>
        <item name="couiTabSelectedFontFamily">sans-serif-medium</item>
    </style>

    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <declare-styleable name="BigSpanTextView">
        <attr name="textColor" format="color" />
        <attr name="clickTextColor" format="color" />
        <attr name="clickTextStyle" />
        <attr name="textStyle" />
        <attr name="textSize" format="dimension" />
        <attr name="lineSpacing" format="dimension" />
    </declare-styleable>

    <declare-styleable name="ColorASRProgressBar">
        <attr name="circleProgressBarWidth" format="dimension" />
        <attr name="circleProgressBarHeight" format="dimension" />
        <attr name="circleProgressBarActiveColor" format="reference|color" />
        <attr name="circleProgressBarInactiveColor" format="reference|color" />
        <attr name="circleProgressBarBgCircleActiveColor" format="reference|color" />
        <attr name="circleProgressBarBgCircleInactiveColor" format="reference|color" />
        <attr name="circleProgress" format="integer" />
        <attr name="circleMax" format="integer" />
        <attr name="circleProgressBarType">
            <enum name="small" value="0" />
            <enum name="medium" value="1" />
            <enum name="large" value="2" />
        </attr>
        <attr name="progress_active_text_color" format="color" />
        <attr name="progress_inactive_text_color" format="color" />
        <attr name="progress_text_size" format="dimension" />
        <attr name="show_progress_text" format="boolean" />
    </declare-styleable>

    <style name="boldTextStyle">
        <item name="android:textColor">?attr/couiColorPrimary</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="StatementAndGuideAnimation">
        <item name="android:windowExitAnimation">@anim/coui_fade_out_interpolator</item>
    </style>

    <declare-styleable name="MarqueeTextView">
        <attr name="scroll_interval" format="integer" />
        <attr name="scroll_first_delay" format="integer" />
        <attr name="scroll_mode">
            <enum name="mode_forever" value="100" />
            <enum name="mode_once" value="101" />
        </attr>
    </declare-styleable>

    <style name="roundedCornerStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">4dp</item>
    </style>
</resources>
