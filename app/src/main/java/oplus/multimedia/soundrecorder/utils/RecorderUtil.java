/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package oplus.multimedia.soundrecorder.utils;

import android.content.Context;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.webkit.MimeTypeMap;

import androidx.annotation.RequiresApi;

import com.soundrecorder.base.utils.AddonAdapterCompatUtil;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.constant.Constants;

import java.io.File;
import java.io.FilenameFilter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executors;

import oplus.multimedia.soundrecorder.RecorderApplication;

public class RecorderUtil {

    private static final char EXTENSION_SEPARATOR = '.';
    private static final char UNIX_SEPARATOR = '/';
    private static final char WINDOWS_SEPARATOR = '\\';
    private static final String TAG = "RecorderUtil";

    public static String getMimeTypeByPath(String path) {
        if (path == null) {
            return null;
        }

        String ext = getExtension(path);
        String type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(ext);
        return type;
    }

    public static String getExtension(String filename) {
        if (filename == null) {
            return null;
        }

        int extensionPos = filename.lastIndexOf(EXTENSION_SEPARATOR);
        int lastUnixPos = filename.lastIndexOf(UNIX_SEPARATOR);
        int lastWindowsPos = filename.lastIndexOf(WINDOWS_SEPARATOR);
        int lastSeparator = Math.max(lastUnixPos, lastWindowsPos);
        int index = (lastSeparator > extensionPos) ? -1 : extensionPos;

        if (index == -1) {
            return "";
        } else {
            return filename.substring(index + 1);
        }
    }

    public static Typeface getXTypeFont() {
        Typeface fontface = null;
        try {
            fontface = Typeface.createFromFile("/system/fonts/XType-Bold.otf");
        } catch (Exception e) {
            DebugUtil.d(TAG, "getXTypeFont error, the  e is " + e);
            fontface = Typeface.DEFAULT;
        }
        return fontface;
    }

    public static Typeface getMediumTypeFont() {
        Typeface fontface = null;
        try {
            fontface = Typeface.create("sans-serif-medium", Typeface.NORMAL);
        } catch (Exception e) {
            DebugUtil.d(TAG, "getMediumTypeFont error, the  e is " + e);
            fontface = Typeface.DEFAULT;
        }
        return fontface;
    }

    public static void enableBackgroundService(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            try {
                List<String> targetPkgList = new ArrayList<String>();
                String packageName = context.getPackageName();
                targetPkgList.add(packageName);
                AddonAdapterCompatUtil.addBackgroundRestrictedInfo(packageName, targetPkgList);
            } catch (Throwable e) {
                DebugUtil.e(TAG, "enable background service error", e);
            }
        }
    }

    public static void deleteLogs() {
        Executors.newSingleThreadExecutor().submit(new Runnable() {
            @Override
            public void run() {
                deleteAppLogMonitorFiles();
                deleteFeedbackLog();
            }
        });
    }

    private static void deleteAppLogMonitorFiles() {
        if (BaseUtil.isAndroidROrLater()) {
            deleteDownloadAppLogMonitorFilesOnR();
        } else {
            deleteColorOsAppLogMonitorFilesOnQ();
            deleteDownloadAppLogMonitorFilesOnQ();
            deleteClockAppLogMonitorFilesOnQ();
        }
    }


    @RequiresApi(api = Build.VERSION_CODES.Q)
    public static void deleteDownloadAppLogMonitorFilesOnR() {
        String packageName = RecorderApplication.getAppContext().getPackageName();
        for (String volumn : MediaStore.getExternalVolumeNames(RecorderApplication.getAppContext())) {
            String where = MediaStore.Files.FileColumns.RELATIVE_PATH + " COLLATE NOCASE = ?";
            String[] whereArgs = new String[]{Environment.DIRECTORY_DOWNLOADS + File.separator + "AppMonitorSDKLogs" + File.separator + packageName + File.separator + "normal" + File.separator};
            Uri contentUri = MediaStore.Files.getContentUri(volumn);
            int deleteCnt = -1;
            try {
                deleteCnt = RecorderApplication.getAppContext().getContentResolver().delete(contentUri, where, whereArgs);
                DebugUtil.i(TAG, "deleteDownloadAppLogMonitorFilesOnR: where: " + where + ", whereArgs: " + Arrays.toString(whereArgs) + ", volumn: " + volumn + ", uri: " + contentUri + ", deleteCnt: " + deleteCnt);
            } catch (Exception e) {
                DebugUtil.e(TAG, "deleteDownloadAppLogMonitorFilesOnR error: where: " + where + ", whereArgs: " + Arrays.toString(whereArgs) + ", volumn: " + volumn + ", uri: " + contentUri + ", deleteCnt: " + deleteCnt, e);
            }
        }
    }

    public static void deleteColorOsAppLogMonitorFilesOnQ() {
        deleteAppLogMonitorFilesOnQ(Constants.COLOROS_DIR, null);
    }

    public static void deleteDownloadAppLogMonitorFilesOnQ() {
        deleteAppLogMonitorFilesOnQ(Environment.DIRECTORY_DOWNLOADS, null);
    }

    public static void deleteClockAppLogMonitorFilesOnQ() {
        deleteAppLogMonitorFilesOnQ(Constants.COLOROS_DIR, Constants.CLOCK_PACKAGE_NAME);
        deleteAppLogMonitorFilesOnQ(Environment.DIRECTORY_DOWNLOADS, Constants.COLORFUL_ENGINE_PACKAGE_NAME);
        deleteAppLogMonitorFilesOnQ(Environment.DIRECTORY_DOWNLOADS, Constants.ASSISTANSCREEN_PACKAGE_NAME);
        deleteAppLogMonitorFilesOnQ(Environment.DIRECTORY_DOWNLOADS, Constants.ROAMING_PACKAGE_NAME);
    }


    private static void deleteAppLogMonitorFilesOnQ(String middeDirectoryName, String inputPackageName) {
        if (TextUtils.isEmpty(middeDirectoryName)) {
            DebugUtil.e(TAG, "input middeDirecotryName is null, return");
            return;
        }
        try {
            String packageName = inputPackageName;
            if (TextUtils.isEmpty(packageName)) {
                packageName = RecorderApplication.getAppContext().getPackageName();
            }
            File file = new File(AddonAdapterCompatUtil.getExternalStorageDirectory(), middeDirectoryName);
            if (file.exists()) {
                File appLogFile = new File(file, "AppMonitorSDKLogs");
                if (appLogFile.exists()) {
                    File soundrecordLogFile = new File(appLogFile, packageName);
                    if (soundrecordLogFile.exists()) {
                        File nornalDir = new File(soundrecordLogFile, "normal");
                        if (nornalDir.exists()) {
                            File[] files = nornalDir.listFiles(new FilenameFilter() {
                                @Override
                                public boolean accept(File dir, String name) {
                                    if (TextUtils.isEmpty(name)) {
                                        return false;
                                    }
                                    if (name.startsWith("trace") && name.endsWith(".txt")) {
                                        return true;
                                    }
                                    return false;
                                }
                            });
                            if (files != null) {
                                for (File logFile : files) {
                                    if ((logFile != null) && logFile.exists()) {
                                        boolean deleteResult = logFile.delete();
                                        DebugUtil.i(TAG, "deleteAppLogMonitorFilesOnQ result: " + deleteResult + ", file.name: " + logFile.getName());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "deleteColorOsAppLogMonitorFilesOnQ failed", e);
        }
    }


    private static void deleteFeedbackLog() {
        try {
            File documentsFile = RecorderApplication.getAppContext().getExternalFilesDir("Documents");
            if ((documentsFile != null) && (documentsFile.exists())) {
                File[] files = documentsFile.listFiles();
                if (files != null) {
                    for (File file : files) {
                        FileUtils.deleteDirOrFile(RecorderApplication.getAppContext(), file);
                    }
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "deleteColorOsAppLogMonitorFilesOnQ failed", e);
        }
    }
}
