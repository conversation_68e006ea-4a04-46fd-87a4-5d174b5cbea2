<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <queries>
        <!-- required to export note-->
        <package
            android:name="com.coloros.note"
            tools:node="remove" />
        <package android:name="com.oplus.note" />
        <!-- required to need hide call recordings-->
        <package android:name="com.android.incallui" />
        <!-- required to need wechat share-->
        <package android:name="com.tencent.mm" />
        <!-- required to need bootreg-->
        <!--oppo开机向导包名，一加外销OS13融合后也是使用的这个-->
        <package android:name="com.coloros.bootreg" />
        <!--老一加开机向导包名，仅在OS13下为这个-->
        <package android:name="com.oplus.opusermanual" />
        <!-- required to wps export-->
        <package android:name="cn.wps.moffice_eng" />
        <!-- required to getOpenId to bind stdId app -->
        <package android:name="com.oplus.stdid" />
        <package android:name="com.oplus.dmp" />
        <!-- 图片标记新增查询包名-->
        <package
            android:name="com.oppo.camera"
            tools:node="remove" />
        <package android:name="com.oplus.camera" />
        <package android:name="com.oneplus.camera" />
        <package
            android:name="com.coloros.gallery3d"
            tools:node="remove" />
        <!--一加相册包名-->
        <package android:name="com.oneplus.gallery" />
        <!--悬停空间-->
        <package android:name="com.oplus.bracketspace"/>
        <!--电池-->
        <package
            android:name="com.coloros.oppoguardelf"
            tools:node="remove" />
        <package android:name="com.oplus.battery"/>
        <!--手机管家-->
        <package android:name="com.coloros.phonemanager"/>
    </queries>

    <!-- for AlarmManager API，such as: setAlarmClock、setExact、setExactAndAllowWhileIdle-->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <!-- set recorder mode -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- need check -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <!-- BlueSpeaker-->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />

    <!-- CTS -->
    <uses-permission
        android:name="oppo.permission.OPPO_COMPONENT_SAFE"
        tools:node="remove" />
    <uses-permission android:name="oplus.permission.OPLUS_COMPONENT_SAFE" />
    <uses-permission
        android:name="oppo.permission.cloud.ACCESS_CLOUD"
        tools:node="remove" />

    <!--SAU-->
    <uses-permission
        android:name="com.oppo.permission.safe.SAU"
        tools:node="remove" />
    <uses-permission android:name="com.oplus.permission.safe.SAU" />
    <!--for encyption-->
    <uses-permission
        android:name="com.oppo.permission.safe.PRIVATE"
        tools:node="remove" />
    <uses-permission android:name="com.oplus.permission.safe.PRIVATE" />
    <!--云同步sdk底层获取guid-->
    <uses-permission android:name="com.oplus.permission.safe.SECURITY" />
    <!--for center dmp search-->
    <uses-permission android:name="com.oplus.dmp.IndexProvider.PERMISSION" />
    <uses-permission android:name="com.oplus.dmp.SearchProvider.PERMISSION" />

    <!-- Android T -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"/>
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Android T -->
    <!--悬停空间所需permission-->
    <uses-permission android:name="oplus.bracketspace.permission.INSERT_PERMISSION" />
    <!--虚拟化从设备需要的权限-->
    <uses-permission android:name="com.oplus.permission.safe.PHONE" />
    <uses-permission android:name="android.permission.TURN_SCREEN_ON"/>

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk"/>

    <application
        android:name="oplus.multimedia.soundrecorder.RecorderApplication"
        android:allowBackup="false"
        android:directBootAware="true"
        android:extractNativeLibs="true"
        android:icon="@drawable/ic_launcher_recorder"
        android:label="@string/app_name_main"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:replace="android:label,android:allowBackup,android:theme,android:name,android:icon">
        <meta-data
            android:name="color.support.options"
            android:value="@string/color_support_value" />
        <meta-data
            android:name="versionCommit"
            android:value="${versionCommit}" />
        <meta-data
            android:name="versionDate"
            android:value="${versionDate}" />
        <meta-data
            android:name="AppCode"
            android:value="20007" />
        <meta-data
            android:name="upgrade_product_code"
            android:value="20199" />

        <meta-data
            android:name="application_do_not_disable"
            android:value="true" />

        <meta-data
            android:name="OppoPermissionKey"
            tools:node="remove" />

        <meta-data
            android:name="OplusPermissionKey"
            android:value="@string/permission_key_1plus"
            tools:replace="android:value" />

        <meta-data
            android:name="AppPlatformKey"
            android:value="@string/appPlatform_permission_key_1plus"
            tools:replace="android:value" />

        <meta-data
            android:name="color_material_enable"
            android:value="true" />

        <meta-data
            android:name="OplusAppCompatibilityToken"
            android:value="12,22" />

        <meta-data
            android:name="support_speech_assist"
            android:value="true" />

        <meta-data
            android:name="support_mp3_callrecordings"
            android:value="true" />

        <!--搬家配置，由于资源读取不到，放到这里来-->
        <meta-data
            android:name="backup_name_resId"
            android:value="@string/app_name_main"></meta-data>

        <meta-data
            android:name="backup_icon_resId"
            android:value="@drawable/ic_launcher_recorder"></meta-data>
        <!--搬家配置结束-->
        <!--配置当前版本支持悬停空间-->
        <meta-data
            android:name="BracketSpaceSupport"
            android:value="true" />

        <activity
            android:name="oplus.multimedia.soundrecorder.slidebar.TransparentActivity"
            android:configChanges="locale|orientation|keyboardHidden|mcc|mnc|density"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/TransparentActivityTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <!--新增action启动快捷录音，侧边栏未修改，仍以className调起的，此处增加为了后续兼容-->
                <action android:name="com.oplus.soundrecorder.open_quick_recording" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 小布助手启动-->
                <action android:name="oplus.intent.action.START_RECORD_FROM_BREENO" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 负一屏卡片启动-->
                <action android:name="oplus.intent.action.provider.start_transparent_from_smallcard" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.soundrecorder.dragonfly.startRecordActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 火烈鸟miniApp接续-->
                <action android:name="com.soundrecorder.MINI_APP_CONTINUE_LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 泛在状态栏胶囊点击跳转录制页面逻辑-->
                <action android:name="oplus.intent.action.com.soundrecorder.SEEDLING_CARD" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 1*2小卡跳转-->
                <action android:name="oplus.intent.action.com.soundrecorder.SMALL_SEEDLING_CARD" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 魔方按钮-->
                <action android:name="oplus.intent.action.START_RECORD_FROM_CUBE_BUTTON" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 录音摘要卡-->
                <action android:name="oplus.intent.action.com.soundrecorder.SUMMARY_CARD" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 锁屏-->
                <action android:name="com.oplus.soundrecorder.LOCK_SCREEN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <!--增加侧边栏启动activity重定向，侧边栏目前是通过硬编码路径跳转的-->
        <activity-alias
            android:name="com.coloros.soundrecorder.TransparentActivity"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:targetActivity="oplus.multimedia.soundrecorder.slidebar.TransparentActivity"
            tools:node="remove"></activity-alias>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/transfer_paths" />
        </provider>

        <receiver
            android:name="com.soundrecorder.common.sync.encryptbox.EncryptBoxDataChangeReceiver"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            tools:replace="android:permission">
            <intent-filter>
                <action android:name="com.coloros.encryption.action.AUDIO_DATA_CHANGED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.oplus.encryption.action.AUDIO_DATA_CHANGED" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.recorder.cloudkit.sync.ui.SettingRecordSyncActivity"
            android:configChanges="locale|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustNothing"
            tools:replace="android:permission">
            <intent-filter>
                <!--云服务定义action-->
                <action android:name="oplus.intent.action.RECORD_CLOUD_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.soundrecorder.editrecord.EditRecordActivity"
            android:configChanges="keyboardHidden|layoutDirection|navigation"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/AppBaseTheme.NoActionBar.ActionMode.LocalDirection"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:replace="android:permission" />


        <activity
            android:name="com.soundrecorder.playback.PlaybackActivity"
            android:configChanges="keyboardHidden|layoutDirection|navigation"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/AppBaseTheme.NoActionBar.ActionMode.LocalDirection"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustNothing"
            tools:replace="android:permission" />

        <activity
            android:name="com.soundrecorder.playback.newconvert.BridgeActivity"
            android:configChanges="locale|orientation|keyboardHidden|mcc|mnc|density"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:resizeableActivity="false"
            android:screenOrientation="behind"
            android:theme="@style/TransparentActivityTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustResize"
            tools:replace="android:permission">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:host="openbridgebctivity"
                    android:scheme="privacy" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.soundrecorder.record.RecorderActivity"
            android:configChanges="locale|orientation|keyboardHidden|screenSize|screenLayout|layoutDirection|uiMode|smallestScreenSize|navigation|fontScale|keyboard|density"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/RecorderActivityTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustResize"
            tools:replace="android:permission">
            <intent-filter>
                <action
                    android:name="com.oppo.soundrecorder.open_recorder"
                    tools:node="remove" />
                <action android:name="com.oplus.soundrecorder.open_recorder" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="color.support.UI_OPTIONS"
                android:value="splitActionBarWhenNarrow" />
        </activity>

        <provider
            android:name="oplus.multimedia.soundrecorder.RecorderStateProvider"
            android:authorities="com.multimedia.record.state.provider"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
    </application>
</manifest>
