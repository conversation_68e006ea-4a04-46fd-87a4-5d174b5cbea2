
<template>
  <div
    class="widgetui-container"
    style="{{ containerStyle }}"
    forcedark="false"
    desc-restyling="true"
    onresize="resizeChange"
  >
    <slot name="default"></slot>
  </div>
</template>

<script>
/**
 * @file 卡片容器组件
 */
export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    backgroundColor: {
      type: String,
      default: '#f2f3f4',
    },
  },

  computed: {
    containerStyle() {
      return {
        backgroundColor: this.isDark ? '#333333' : this.backgroundColor,
      }
    },
  },

  resizeChange(event) {
    this.$emit('emitResize', {
      width: event.offsetWidth,
      height: event.offsetHeight,
    })
  },
}
</script>

<style lang="less">
.widgetui-container {
  background-size: contain;
  width: 100%;
  height: 100%;
  background: linear-gradient(315deg, #DFE3E8 -60.34%, #EDEFF3 136.92%);
}
</style>
