<template>
  <div class="title-wrapper">
    <image class="title-logo" src="{{ icon }}" forcedark="false"></image>
    <text class="title-txt" style="color:{{ color }}">{{ title }}</text>
  </div>
</template>

<!-- 注意：卡片运行时不会加载app.ux，请不要在app.ux中添加卡片相关逻辑 -->
<script>
export default {
  props: {
    title: {
      type: String,
      default: '卡片标题'
    },
    icon: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss">
@import '../../common/css/common.scss';

.title-wrapper {
  align-items: center;
  flex-shrink: 0;
  width: 100%;
  height: 36 * $size-factor;
  padding-top: 16 * $size-factor;
  padding-left: 16 * $size-factor;

  .title-logo {
    width: 18 * $size-factor;
    height: 18 * $size-factor;
    border-radius: 4 * $size-factor;
    object-fit: contain;
  }

  .title-txt {
    flex: 1;
    line-height: 20 * $size-factor;
    font-size: 14 * $size-factor;
    margin-left: 6 * $size-factor;
    lines: 1;
    text-overflow: ellipsis;
  }
}
</style>
