{"v": "5.9.6", "fr": 30, "ip": 0, "op": 76, "w": 216, "h": 126, "nm": "大状态栏音频波形Lottie", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "矩形 白色 2", "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [185.625, 63, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [157.627, 63, 0], "t": 7, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [183.621, 63, 0], "t": 8, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [179.625, 63, 0], "t": 9, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [163.628, 63, 0], "t": 13, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [159.625, 63, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [185.625, 63, 0], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [157.627, 63, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [183.621, 63, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [179.625, 63, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [163.628, 63, 0], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [159.625, 63, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [185.625, 63, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [157.627, 63, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [183.621, 63, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [179.625, 63, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [163.628, 63, 0], "t": 43, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [159.625, 63, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [185.625, 63, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [157.627, 63, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [183.621, 63, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [179.625, 63, 0], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [163.628, 63, 0], "t": 58, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [159.625, 63, 0], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [185.625, 63, 0], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [157.627, 63, 0], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [183.621, 63, 0], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [179.625, 63, 0], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [163.628, 63, 0], "t": 73, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [159.625, 63, 0], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [185.625, 63, 0], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "矩形 白色 3", "sr": 1, "ks": {"o": {"k": [{"s": [0], "t": 4, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 5, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 6, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 7, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 8, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 11, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 12, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 13, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 14, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 15, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 19, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 20, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 21, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 22, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 23, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 26, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 27, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 28, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 29, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 30, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 34, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 35, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 36, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 37, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 38, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 41, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 42, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 43, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 44, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 45, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 49, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 50, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 51, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 52, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 53, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 56, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 57, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 58, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 59, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 60, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 64, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 65, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 66, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 67, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 68, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 71, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 72, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 73, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 74, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 75, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [215.625, 63, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [187.627, 63, 0], "t": 7, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [213.621, 63, 0], "t": 8, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [209.625, 63, 0], "t": 9, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [193.628, 63, 0], "t": 13, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [189.625, 63, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [215.625, 63, 0], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [187.627, 63, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [213.621, 63, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [209.625, 63, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [193.628, 63, 0], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [189.625, 63, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [215.625, 63, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [187.627, 63, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [213.621, 63, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [209.625, 63, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [193.628, 63, 0], "t": 43, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [189.625, 63, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [215.625, 63, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [187.627, 63, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [213.621, 63, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [209.625, 63, 0], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [193.628, 63, 0], "t": 58, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [189.625, 63, 0], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [215.625, 63, 0], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [187.627, 63, 0], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [213.621, 63, 0], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [209.625, 63, 0], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [193.628, 63, 0], "t": 73, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [189.625, 63, 0], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [215.625, 63, 0], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "矩形 白色 4", "sr": 1, "ks": {"o": {"k": [{"s": [30], "t": 0, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 1, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 2, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 3, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 7, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [27.755], "t": 8, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [2.245], "t": 10, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 11, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 14, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 15, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 16, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 17, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 18, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 22, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [27.755], "t": 23, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [2.245], "t": 25, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 26, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 29, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 30, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 31, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 32, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 33, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 37, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [27.755], "t": 38, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [2.245], "t": 40, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 41, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 44, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 45, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 46, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 47, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 48, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 52, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [27.755], "t": 53, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [2.245], "t": 55, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 56, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 59, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 60, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.193], "t": 61, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.807], "t": 62, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 63, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 67, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [27.755], "t": 68, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [2.245], "t": 70, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 71, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 74, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 75, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [155.625, 63, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [149.222, 63, 0], "t": 2, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [133.228, 63, 0], "t": 7, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [154.023, 63, 0], "t": 8, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [134.825, 63, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [155.625, 63, 0], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [149.222, 63, 0], "t": 17, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [133.228, 63, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [154.023, 63, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [134.825, 63, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [155.625, 63, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [149.222, 63, 0], "t": 32, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [133.228, 63, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [154.023, 63, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [134.825, 63, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [155.625, 63, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [149.222, 63, 0], "t": 47, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [133.228, 63, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [154.023, 63, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [134.825, 63, 0], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [155.625, 63, 0], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [149.222, 63, 0], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [133.228, 63, 0], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [154.023, 63, 0], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [134.825, 63, 0], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [155.625, 63, 0], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "番茄红", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [125.625, 63, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 84], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992156863213, 0.23137255013, 0.23137255013, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 番茄红", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 3, "nm": "位置变化", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [106.625, 63, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 75, "s": [-133.375, 63, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "矩形 白色 11112", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 69.5, "s": [0]}, {"t": 71, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [279, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 12], "t": 69, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 23], "t": 70, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 45], "t": 71, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 68.35], "t": 72, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 68.048], "t": 73, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 61.595], "t": 74, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 55.304], "t": 75, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "矩形 白色 111", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 60, "s": [0]}, {"t": 61.5, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [249, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 12], "t": 60, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 20], "t": 61, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 29.983], "t": 62, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 32.978], "t": 63, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 31.284], "t": 64, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 28.824], "t": 65, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 26.841], "t": 66, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 25.533], "t": 67, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.764], "t": 68, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.35], "t": 69, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.144], "t": 70, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.05], "t": 71, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.011], "t": 72, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "矩形 白色 110", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 50.5, "s": [0]}, {"t": 52, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [219, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 12], "t": 50, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 21.667], "t": 51, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 41], "t": 52, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 61.52], "t": 53, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 61.254], "t": 54, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 55.583], "t": 55, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 50.055], "t": 56, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 46.094], "t": 57, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 43.641], "t": 58, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 42.264], "t": 59, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 41.552], "t": 60, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 41.211], "t": 61, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 41.063], "t": 62, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 41.007], "t": 63, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "矩形 白色 13", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 41.5, "s": [0]}, {"t": 43, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [189, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 12], "t": 0, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 12], "t": 75, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "矩形 白色 12", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 32, "s": [0]}, {"i": {"x": [0.684], "y": [1]}, "o": {"x": [0.329], "y": [0]}, "t": 33.5, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 63.5, "s": [100]}, {"t": 65.509765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [159, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 12], "t": 0, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 12], "t": 75, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "矩形 白色 11", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 22.5, "s": [0]}, {"i": {"x": [0.684], "y": [1]}, "o": {"x": [0.329], "y": [0]}, "t": 24, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 54.5, "s": [100]}, {"t": 56.509765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [129, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 12], "t": 22, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 16], "t": 23, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24], "t": 24, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 32.491], "t": 25, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 32.381], "t": 26, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 30.034], "t": 27, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 27.747], "t": 28, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 26.108], "t": 29, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 25.093], "t": 30, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.523], "t": 31, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.228], "t": 32, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.087], "t": 33, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.026], "t": 34, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.003], "t": 35, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24], "t": 54, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 22.129], "t": 55, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 13.937], "t": 56, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.741], "t": 57, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.085], "t": 58, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.442], "t": 59, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.967], "t": 60, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.391], "t": 61, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.671], "t": 62, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.836], "t": 63, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.925], "t": 64, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.969], "t": 65, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.989], "t": 66, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "矩形 白色 10", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.684], "y": [1]}, "o": {"x": [0.329], "y": [0]}, "t": 14.5, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 45, "s": [100]}, {"t": 47.009765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [99, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 12], "t": 13, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 35.333], "t": 14, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 64.451], "t": 15, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 73.185], "t": 16, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 68.246], "t": 17, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 61.069], "t": 18, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 55.286], "t": 19, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 51.471], "t": 20, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 49.228], "t": 21, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 48.021], "t": 22, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 47.421], "t": 23, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 47.146], "t": 24, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 47.033], "t": 25, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 46.994], "t": 26, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 47], "t": 45, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 29.627], "t": 46, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 12.003], "t": 47, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 6.736], "t": 48, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 6.777], "t": 49, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 8.232], "t": 50, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 9.658], "t": 51, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.681], "t": 52, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.316], "t": 53, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.672], "t": 54, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.857], "t": 55, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.945], "t": 56, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.984], "t": 57, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.998], "t": 58, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "矩形 白色 9", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 4, "s": [0]}, {"i": {"x": [0.684], "y": [1]}, "o": {"x": [0.329], "y": [0]}, "t": 5.5, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 35.5, "s": [100]}, {"t": 37.509765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [69, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 12], "t": 4, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 20], "t": 5, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 29.983], "t": 6, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 32.978], "t": 7, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 31.284], "t": 8, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 28.824], "t": 9, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 26.841], "t": 10, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 25.533], "t": 11, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.764], "t": 12, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.35], "t": 13, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.144], "t": 14, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.05], "t": 15, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24.011], "t": 16, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 24], "t": 35, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 22.129], "t": 36, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 13.937], "t": 37, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.741], "t": 38, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.085], "t": 39, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.442], "t": 40, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.967], "t": 41, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.391], "t": 42, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.671], "t": 43, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.836], "t": 44, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.925], "t": 45, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.969], "t": 46, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.989], "t": 47, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "矩形 白色 5", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": -5.5, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": -4, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 26.5, "s": [100]}, {"t": 28.509765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [39, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [12, 55.304], "t": 0, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 50.797], "t": 1, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 48.006], "t": 2, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 46.439], "t": 3, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 45.628], "t": 4, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 45.24], "t": 5, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 45.072], "t": 6, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 45.008], "t": 7, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 45], "t": 26, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 34.492], "t": 27, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 15.884], "t": 28, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 9.282], "t": 29, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 7.866], "t": 30, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 8.636], "t": 31, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 9.77], "t": 32, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 10.685], "t": 33, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.29], "t": 34, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.646], "t": 35, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.838], "t": 36, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.933], "t": 37, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.977], "t": 38, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.995], "t": 39, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": -25, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "矩形 白色 6", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 17, "s": [100]}, {"t": 19, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [9, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 17, "s": [12, 24]}, {"t": 19, "s": [12, 12]}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "矩形 白色 7", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 7.5, "s": [100]}, {"t": 9.5, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-21, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 7.5, "s": [12, 41]}, {"t": 9.5, "s": [12, 12]}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "矩形 白色 8", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 0, "s": [100]}, {"t": 0.5, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-51, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "ct": 1, "bm": 0}], "markers": [{"tm": 26.5, "cm": "2", "dr": 0}]}