package com.oplus.recorderlogx

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import com.oplus.recorderlog.log.ILogProcess
import com.oplus.recorderlog.log.RecorderLogger
import com.oplus.recorderlog.log.dbprint.DbPrinterImpl
import com.oplus.recorderlog.log.IDbPrinter
import com.oplus.recorderlog.util.BaseUtil
import com.oplus.recorderlog.util.VersionUtil
import com.soundrecorder.base.utils.OpenIdUtils
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig
import java.io.File

class RecorderXLog : ILogProcess {

    companion object {
        const val TAG = "RecorderXLog"

        const val MANUAL_REASON = "manule_report_from_user"
        //业务方push配置目标匹配包名
        const val SOUNDRECORDER_LOG_PUSH_PKG = "com.coloros.soundrecorder"


        //录音NewSoundRecorder在MDP中申请的业务日志回传定义“recordlog_retrieve”
        private const val MDP_BUSINESS = "recordlog_retrieve"
        //录音NewSoundRecorder在MDP中申请的业务下的子业务，暂时未定义，传空
        private const val MDP_SUBTYPE = ""
        //Recorder申请的MDP，主动上报到Recorder的日志后台空间
        private const val MDP_APP_KEY = "1725"
        //Recorder申请的，主动上报到Recorder的日志后台空间
        private const val MDP_APP_SEC = "0KbCK4v2M1DMNS3cZSTjU0t6bELblz5w"


        private const val PARENT_DIR_NAME = "NewSoundRecorder"
        private const val LOG_DIR_NAME = "Log"
    }


    @SuppressLint("StaticFieldLeak")
    private var mLogger: com.oplus.log.Logger? = null
    private var simpleLog: com.oplus.log.ISimpleLog? = null
    private var iDbPrinter: IDbPrinter = DbPrinterImpl(this)



    override fun initLog(context: Context) {
        val businessPkg = context.packageName
        val processName = businessPkg
        var cache = ""
        val externalCacheDir = context.externalCacheDir
        cache = if (externalCacheDir != null) {
            externalCacheDir.absolutePath
        } else {
            context.cacheDir.absolutePath
        }
        val logPath = cache + File.separator + PARENT_DIR_NAME + File.separator + LOG_DIR_NAME
        i(
            TAG,
            "init processName:$processName " +
                    "businessType:$MDP_BUSINESS, " +
                    "bizSubType:$MDP_SUBTYPE, " +
                    "businessPkg:$businessPkg, " +
                    "logPath:$logPath"
        )
        mLogger = com.oplus.log.Logger.newBuilder()
            .withHttpDelegate(RecordLogHttpDelegate()) // 网络请求代理
            .logFilePath(logPath) //文件输出路径
            .fileLogLevel(com.oplus.log.consts.LogLevel.INFO.toInt()) //文件日志级别
            .consoleLogLevel(com.oplus.log.consts.LogLevel.NONE.toInt()) //控制台日志级别, NONE表示不输出
            .fileExpireDays(15) //文件过期天数, 过期文件会被删除
            .setTracePkg(businessPkg) //自定义包名,不传时会自动获取(只是作为远端配置的一个key值而已)
            .setProcessName(processName ?: "")
            .setOpenIdProvider(object : com.oplus.log.Settings.IOpenIdProvider {
                //可传一个或多个,传多个时,服务端会依次匹配
                override fun getGuid(): String? {
                    return null
                }

                override fun getOuid(): String? {
                    return null
                }

                override fun getDuid(): String? {
                    return OpenIdUtils.INSTANCE.duid
                }
            })
            .mmapCacheDir(logPath) //这里设置mmapCache的路径，用于区分cloudkit和录音分别打印的日志
            .create(context.applicationContext)
        if (mLogger != null) { //获取路径失败时，logger 可能为空，任何使用到的地方都需要判空
            simpleLog = mLogger?.simpleLog //日志写入操作类
        } else {
            Log.e(TAG, "init logger = null")
        }
    }


    override fun flushLog(isSync: Boolean) {
        if (mLogger != null) {
            RecorderLogger.i(TAG, "flushLog $isSync", false)
            try {
                mLogger?.flush(isSync)
            } catch (_: Exception) {
            }
        }
    }

    override fun d(tag: String?, message: String?) {
        simpleLog?.d(tag, message)
    }

    override fun v(tag: String?, message: String?) {
        simpleLog?.v(tag, message)
    }

    override fun i(tag: String?, message: String?) {
        simpleLog?.i(tag, message)
    }

    override fun w(tag: String?, message: String?) {
        simpleLog?.w(tag, message)
    }

    override fun e(tag: String?, message: String?) {
        simpleLog?.e(tag, message)
    }

    override fun e(tag: String?, message: String?, e: Throwable?) {
        simpleLog?.e(tag, message)
    }

    /**
     * 收到日志打捞Push消息时的相关处理
     */

    override fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig) {
        upload(context, createUserTraceConfigDto(cloudLogConfigMsg))
    }

    /**
     * 收到push消息，转换为相关字段转为相关日志调用
     */
    private fun createUserTraceConfigDto(recordLogConfigMsg: RecordLogXConfig): com.usertrace.cdo.usertrace.domain.dto.UserTraceConfigDto? {
        val userTraceConfigDto = com.usertrace.cdo.usertrace.domain.dto.UserTraceConfigDto()
        try {
            userTraceConfigDto.traceId = recordLogConfigMsg.getTraceId()?.toLong() ?: -1
        } catch (exception: Exception) {
            e(
                TAG,
                "createUserTraceConfigDto setTraceId exception " + exception.message
            )
        }
        userTraceConfigDto.imei = recordLogConfigMsg.getImei()
        try {
            userTraceConfigDto.beginTime = recordLogConfigMsg.getBeginTime()?.toLong() ?: 0
        } catch (exception: Exception) {
            e(
                TAG,
                "createUserTraceConfigDto setBeginTime exception " + exception.message
            )
        }
        try {
            userTraceConfigDto.endTime = recordLogConfigMsg.getEndTime()?.toLong() ?: 0
        } catch (exception: Exception) {
            e(
                TAG,
                "createUserTraceConfigDto setEndTime exception " + exception.message
            )
        }
        try {
            userTraceConfigDto.force = recordLogConfigMsg.getForce()?.toInt() ?: 1
        } catch (exception: Exception) {
            e(
                TAG,
                "createUserTraceConfigDto setEndTime exception " + exception.message
            )
        }
        userTraceConfigDto.tracePkg = recordLogConfigMsg.getTracePkg()
        userTraceConfigDto.openId = recordLogConfigMsg.getOpenId()
        userTraceConfigDto.registrationId = recordLogConfigMsg.getRegistrationId()
        return userTraceConfigDto
    }


    /**
     *  上传log的时候打印当前App和当前手机的一些基本信息，写入日志中
     */
    fun printVersionInfo(context: Context?) {
        val startTs = System.currentTimeMillis()
        val versionInfoBuilder = StringBuilder()
        val cloudKitVersionName: String = VersionUtil.getAppVersionName(context)
            .toString()
        versionInfoBuilder.append(" RecorderAppVersionName:")
        versionInfoBuilder.append(cloudKitVersionName) //cloudkit版本
        val appVersionCode: String = VersionUtil.getAppVersionCode(context)
            .toString()
        versionInfoBuilder.append(" appVersionCode:")
        versionInfoBuilder.append(appVersionCode) //业务方版本
        val osRomVersion: String = VersionUtil.romVersion
        versionInfoBuilder.append(" osRomVersion:")
        versionInfoBuilder.append(osRomVersion)
        val osOtaVersion: String = VersionUtil.otaVersion
        versionInfoBuilder.append(" osOtaVersion:")
        versionInfoBuilder.append(osOtaVersion)
        val deviceName: String = VersionUtil.colorOSVersion
        versionInfoBuilder.append(" colorOSVersion:")
        versionInfoBuilder.append(deviceName)
        val deviceModel: String = VersionUtil.androidVersion
        versionInfoBuilder.append(" androidVersion:")
        versionInfoBuilder.append(deviceModel)
        val regionMark: String = VersionUtil.uRegion
        versionInfoBuilder.append(" uRegion:")
        versionInfoBuilder.append(regionMark)
        val cost = System.currentTimeMillis() - startTs
        w(TAG, versionInfoBuilder.toString())
        w(TAG, "printVersionInfo end cost:$cost")
    }


    override fun processDBPrint(context: Context?) {
        printDb(context)
    }

    fun printDb(context: Context?) {
        iDbPrinter.printMediaDb(context)
        waitForSec(5)
        flushLog(true)
        waitForSec(5)
        iDbPrinter.printRecordDb(context)
        waitForSec(5)
        flushLog(true)
        waitForSec(5)
    }


    private fun upload(context: Context?, userTraceConfigDto: com.usertrace.cdo.usertrace.domain.dto.UserTraceConfigDto?) {
        d(TAG, "upload")
        val logger = mLogger
        if (mLogger == null) {
            e(TAG, "upload logService or LogMessageBean is null please init")
            return
        }
        if (userTraceConfigDto == null) {
            e(TAG, "upload userTraceConfigDto is null")
            return
        }

        printVersionInfo(context)
        printDb(context)

        logger?.setUploaderListener(object : com.oplus.log.uploader.UploadManager.UploaderListener {
            override fun onUploaderSuccess() {
                i(TAG, "upload log success")
            }

            override fun onUploaderFailed(message: String) {
                e(TAG, "upload log fail $message")
            }
        })
        val useWifi = userTraceConfigDto.force == 1
        val beginTime = userTraceConfigDto.beginTime
        val endTime = userTraceConfigDto.endTime

        val formatBeginTime = BaseUtil.getFormateTime(beginTime)
        val formatEndTime = BaseUtil.getFormateTime(endTime)

        //subType为业务子类型，一个业务有多个产品时，用于区分产品，没有则传空字符串
        logger?.upload(
            MDP_BUSINESS,
            userTraceConfigDto.traceId.toString(),
            beginTime,
            endTime,
            useWifi,
            MDP_SUBTYPE
        )
        i(
            TAG,
            "upload log businessType:" + MDP_BUSINESS
                    + ", userTraceConfigDto.getTraceId()=" + userTraceConfigDto.traceId
                    + ", beginTime=" + beginTime
                    + ", beginFormatTime=" + formatBeginTime
                    + ", endTime = " + endTime
                    + ", endFormatTime = " + formatEndTime
                    + " useWifi=" + useWifi
        )
        //ToastManager.showLongToast("上传日志")
    }

    fun checkUpload(context: Context?) {
        val logger = mLogger
        if (logger == null) {
            Log.e(TAG, "checkUpload logger = null")
            return
        }
        i(TAG, "checkUpload BUSINESS_TYPE:$MDP_BUSINESS, SUB_TYPE:$MDP_SUBTYPE")
        //subType为业务子类型，一个业务有多个产品时，用于区分产品，没有则传空字符串
        logger.checkUpload(
            MDP_BUSINESS,
            MDP_SUBTYPE,
            object : com.oplus.log.uploader.UploadManager.UploadCheckerListener {
                override fun onNeedUpload(userTraceConfigDto: com.usertrace.cdo.usertrace.domain.dto.UserTraceConfigDto) {
                    i(TAG, "checkUpload onNeedUpload")
                    upload(context, userTraceConfigDto)
                }

                override fun onDontNeedUpload(msg: String) {
                    w(TAG, "checkUpload onDontNeedUpload msg:$msg")
                }
            })
    }


    private fun getDefaultLogDayTime(): Long {
        return 1000 * 60 * 60 * 24 * 7L // 7天
    }


    override fun processManualReportLog() {
        val timeInMiliSec =  getDefaultLogDayTime()
        val reportReason = MANUAL_REASON
        val useWifi = false
        reportUpload(reportReason, timeInMiliSec, useWifi)
    }



    /**
     * 主动反馈上报，默认上传最新一个小时日志
     * @param reportReason 不能存在空格
     */
    private fun reportUpload(reportReason: String, hour: Long, useWifi: Boolean) {
        val logger = mLogger
        if (logger == null) {
            e(TAG, "reportUpload logger = null")
            return
        }
        flushLog(true)
        i(
            TAG,
            "reportUpload reportReason:$reportReason, hour$hour, useWifi:$useWifi, businessType:$MDP_BUSINESS"
        )
        // 设置report接口监听
        logger.setReporterListener(object :
            com.oplus.log.uploader.UploadManager.ReportUploaderListener {
            override fun onReporterSuccess(response: com.oplus.log.uploader.ResponseWrapper) {
                // reportUoload上传成功后，可通过response获取reportId
                d(
                    TAG, "onReporterSuccess code:" + response.statusCode + ", msg:" + response.message
                )
            }

            override fun onReporterFailed(errorMsg: String, reportBody: com.oplus.log.uploader.UploadManager.ReportBody) {
                e(TAG, "onReporterFailed：$errorMsg")
            }
        })

        // 上传一个小时以内的日志文件
        val ts = System.currentTimeMillis()

        // business 业务名称（必传）
        // specificId 特征id（不传或传空字符串）
        // startTime 日志开始时间（必传）
        // endTime  日志结束时间（必传）
        // useWifi  是否在WiFi条件下上传（必传）
        // subType  业务的子业务（若无该参数可不传或传空字符串）
        // appKey   mdp上应用组件的appkey（必传）4.0.2以上版本此参数名称变更为program
        // reportReason  上报原因（必传）
        // appSecret  mdp上应用组件的appSecret（必传）
        logger.reportUpload(
            MDP_BUSINESS,
            "",
            ts - hour,
            ts,
            useWifi,
            MDP_SUBTYPE,
            MDP_APP_KEY,
            reportReason,
            MDP_APP_SEC
        )
    }


    private fun waitForSec(sec: Int) {
        try {
            Thread.sleep(sec * 1000L)
        } catch (e: InterruptedException) {
            e(TAG, "waitForSec interrupted", e)
        }
    }
}