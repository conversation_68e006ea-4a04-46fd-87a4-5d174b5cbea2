/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ShareStatisticsUtil.kt
 ** Description: ShareStatisticsUtil.
 ** Version: 1.0
 ** Date : 2025/4/3
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/4/3    1.0    create
 ****************************************************************/
package com.soundrecorder.common.buryingpoint

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction.addNewCommonUserAction

object ShareStatisticsUtil {
    private const val TAG = "ShareStatisticsUtil"
    private const val EVENT_GROUP_SHARE = "share"

    /*点击的分享类型*/
    private const val EVENT_ID_SHARE = "event_share"
    private const val KEY_SHARE_TYPE = "type"
    const val VALUE_LINK_SHARE = 0
    const val VALUE_AUDIO_SHARE = 1
    const val VALUE_TEXT_SHARE = 2
    const val VALUE_AUDIO_AND_TEXT_SHARE = 3

    /*链接分享生成链接时间*/
    private const val EVENT_ID_LINK_SHARE = "event_link_share"
    private const val KEY_LINK_GENERATION_TIME = "link_time" // 毫秒
    private const val KEY_LINK_GENERATION_RESULT = "result"
    const val VALUE_LINK_GENERATION_SUCCESS = 0

    /*链接分享上传录音文件时间*/
    private const val EVENT_ID_FILE_UPLOAD_TIME = "event_file_upload_time"
    private const val KEY_FILE_UPLOAD_TIME = "time"

    @JvmStatic
    fun addClickShareTypeEvent(shareType: Int) {
        val eventInfo: MutableMap<String, Int> = HashMap()
        eventInfo[KEY_SHARE_TYPE] = shareType
        addNewCommonUserAction(BaseApplication.getAppContext(), EVENT_GROUP_SHARE, EVENT_ID_SHARE, eventInfo, false)
    }

    @JvmStatic
    fun addGenerationLinkTimeEvent(generationLinkTime: Long, errorCode: Int) {
        DebugUtil.d(TAG, "addGenerationLinkTimeEvent generationLinkTime=$generationLinkTime")
        val eventInfo: MutableMap<String, Any> = HashMap()
        eventInfo[KEY_LINK_GENERATION_TIME] = generationLinkTime
        eventInfo[KEY_LINK_GENERATION_RESULT] = errorCode
        addNewCommonUserAction(BaseApplication.getAppContext(), EVENT_GROUP_SHARE, EVENT_ID_LINK_SHARE, eventInfo, false)
    }

    @JvmStatic
    fun addLinkShareFileUploadTimeEvent(fileUploadTime: Long) {
        DebugUtil.d(TAG, "addLinkShareFileUploadTimeEvent fileUploadTime=$fileUploadTime")
        val eventInfo: MutableMap<String, Long> = HashMap()
        eventInfo[KEY_FILE_UPLOAD_TIME] = fileUploadTime
        addNewCommonUserAction(BaseApplication.getAppContext(), EVENT_GROUP_SHARE, EVENT_ID_FILE_UPLOAD_TIME, eventInfo, false)
    }
}