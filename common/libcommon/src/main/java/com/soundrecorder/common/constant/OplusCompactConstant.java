package com.soundrecorder.common.constant;

public class OplusCompactConstant {

    //SETTING ACTION ACTIVITY SEND
    public static final String SETTING_RINGTONE_ACTION_BEFOR = "com.oppo.music.set_ringtone";
    public static final String SETTING_RINGTONE_ACTION_AFTER = "oplus.intent.action.settings.RINGTONE_SETTINGS";

    //STATEMENT PAGE ACTION ACTIVITY SEND
    public static final String STATEMENT_ACTION_BEFOR = "com.coloros.bootreg.activity.statementpage";
    public static final String STATEMENT_ACTION_AFTER = "com.oplus.bootreg.activity.statementpage";
    public static final String PACKAGE_BOOTREG_BEFORE = "com.coloros.bootreg";
    public static final String FLAG_STATEMENT_INTENT = "statement_intent_flag";
    public static final int FLAG_STATEMENT_INTENT_VALUE = 2;

    //oneplus-exp STATEMENT PAGE ACTION ACTIVITY SEND
    public static final String STATEMENT_ACTION_ONEPLUS = "android.oem.intent.action.OP_LEGAL";
    public static final String PACKAGE_BOOTREG_ONEPLUS = "com.oplus.opusermanual";
    public static final String FLAG_STATEMENT_INTENT_ONEPLUS = "op_legal_notices_type";
    public static final int FLAG_STATEMENT_INTENT_VALUE_ONEPLUS = 3;

    //START BROWSE FILE ACTIVITY RECEIVED
    public static final String START_BROWSE_ACTION_BEFOR = "oppo.intent.action.BROWSE_FILE";
    public static final String START_BROWSE_ACTION_AFTER = "oplus.intent.action.BROWSE_FILE";

    public static final String START_BROWSE_ACTION_THREAD_RECORD = "oplus.intent.action.THREAD_RECORD";

    //STATUS BAR ACTION ACTIVITY RECEIVED
    public static final String STATUS_BAR_OPEN_RECORDER_ACTION_BEFOR = "com.oppo.soundrecorder.open_recorder";
    public static final String STATUS_BAR_OPEN_RECORDER_ACTION_AFTER = "com.oplus.soundrecorder.open_recorder";

    //STATUS BAR ACTION BROADCAST SEND
    public static final String STATUS_BAR_UPDATE_ACTION_BEFOR = "com.oppo.recorder";
    public static final String STATUS_BAR_UPDATE_ACTION_AFTER = "com.oplus.recorder";

    //STOP RECORDER ACTION BROADCAST SEND by camera
    public static final String STOP_RECORDER_AFTER = "oplus.multimedia.soundrecorder.stopRecroderNormal";
    public static final String STOP_RECORDER_NORMAL_BEFOR = "oppo.multimedia.soundrecorder.stopRecroderNormal";

    //LOW STORAGE ACTION BROADCAST RECIEVED
    public static final String LOWMEMORY_BROADCAST_ACTION_BEFOR = "oppo.intent.action.TASK_TERMINATION_FOR_LOW_STORAGE";
    public static final String LOWMEMORY_BROADCAST_ACTION_AFTER = "oplus.intent.action.TASK_TERMINATION_FOR_LOW_STORAGE";

    public static final String PROVIDER_START_BROWSE_FILE_FROM_SMALLCARD = "oplus.intent.action.provider.start_browse_file_from_smallcard";
}
