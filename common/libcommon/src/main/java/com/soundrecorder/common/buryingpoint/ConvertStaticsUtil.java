/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.soundrecorder.common.buryingpoint;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.DebugUtil;

import java.util.HashMap;

import static com.soundrecorder.common.buryingpoint.RecorderUserAction.DEFAULT_VALUE;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CLICK_SHARE_TEXT;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_CANCEL;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_CANCEL_DIALOG;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_CANCEL_DURATION;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_CONTINUE_DIALOG;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_FAIL_DURATION;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_FILE_DURATION;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_START;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_STOP;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_SUCCESS_DURATION;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_CONVERT_TEXT;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_EXPORT_CANCEL;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_EXPORT_CLICK_EXPORT;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_EXPORT_COPY;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_EXPORT_DOC;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_EXPORT_DOC_NUM;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_EXPORT_TO_NOTE;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_EXPORT_TXT;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_LOOK_TEXT;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_NOT_INSTALL_WPS;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_SHARE_TXT_SETTING_SWITCH;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_SHOW_SPEAKERS;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_SPEAKER_CLICK_TIPS;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_SPEAKER_RENAME_ALL;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_SPEAKER_RENAME_BY_HISTORY;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.KEY_SPEAKER_RENAME_CURRENT;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_CONVERT_CANCEL;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_CONVERT_START;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_CONVERT_STOP;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_EXPORT_CANCEL;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_EXPORT_COPY;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_EXPORT_TO_NOTE;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_EXPORT_TXT;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_SHOW_SPEAKERS_SHOWING;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_SHOW_SPEAKERS_UNSHOWING;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_SPEAKER_CLICK_TIPS;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_SPEAKER_RENAME_ALL;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_SPEAKER_RENAME_BY_HISTORY;
import static com.soundrecorder.common.buryingpoint.RecorderUserAction.VALUE_SPEAKER_RENAME_CURRENT;

public class ConvertStaticsUtil {

    public static final String TAG = "ConvertStaticsUtil";
    public static final String EVENT_CONVERT_FAIL_MESSAGE = "event_convert_fail_message";
    public static final String KEY_CONVERT_FAIL_MESSAGE = "message";
    public static final String KEY_CONVERT_UPLOAD_CODE = "upload_code";
    public static final String KEY_CONVERT_CONVERT_CODE = "convert_code";

    public static void addConvertStartEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_CONVERT_START, VALUE_CONVERT_START);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addConvertStartEvent");
    }

    public static void addConvertStopEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_CONVERT_STOP, VALUE_CONVERT_STOP);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addConvertStopEvent");
    }

    public static void addConvertCancelEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_CONVERT_CANCEL, VALUE_CONVERT_CANCEL);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addConvertCancelEvent");
    }


    public static void addExportCopyEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_EXPORT_COPY, VALUE_EXPORT_COPY);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addExportCopyEvent");
    }


    public static void addExportTxtEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_EXPORT_TXT, VALUE_EXPORT_TXT);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addExportTxtEvent");
    }


    public static void addExportCancelEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_EXPORT_CANCEL, VALUE_EXPORT_CANCEL);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addExportCancelEvent");
    }

    //==================convert rename speaker=====================

    /**
     * add point when click speaker tip
     */
    public static void addSpeakerClickSpeakerTipEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_SPEAKER_CLICK_TIPS, VALUE_SPEAKER_CLICK_TIPS);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addSpeakerClickSpeakerTipEvent");
    }

    /**
     * add point when rename speaker by use history
     */
    public static void addSpeakerRenameByHistoryEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_SPEAKER_RENAME_BY_HISTORY, VALUE_SPEAKER_RENAME_BY_HISTORY);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addSpeakerRenameByHistoryEvent");
    }

    /**
     * add point when rename all speaker
     */
    public static void addSpeakerRenameAllEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_SPEAKER_RENAME_ALL, VALUE_SPEAKER_RENAME_ALL);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addSpeakerRenameAllEvent");
    }

    /**
     * add point when rename current speaker
     */
    public static void addSpeakerRenameCurrentEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_SPEAKER_RENAME_CURRENT, VALUE_SPEAKER_RENAME_CURRENT);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addSpeakerRenameCurrentEvent");
    }

    /**
     * add point when click show speaker switch
     * 点击讲话人单选按钮  转文本页面
     */
    public static void addClickSpeakerSwitchEventOnConvert(Boolean isShow) {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        if (isShow) {
            eventInfo.put(KEY_SHOW_SPEAKERS, VALUE_SHOW_SPEAKERS_SHOWING);
        } else {
            eventInfo.put(KEY_SHOW_SPEAKERS, VALUE_SHOW_SPEAKERS_UNSHOWING);
        }
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
        DebugUtil.i(TAG, "addClickSpeakerSwitchEventOnConvert");
    }

    /**
     * add point when click export to Doc format
     */
    public static void addExportDocEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_EXPORT_DOC_NUM, DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_EXPORT,
                RecorderUserAction.EVENT_CONVERT_EXPORT_DOC,
                eventInfo, false);
        DebugUtil.i(TAG, "addExportDocEvent");
    }

    /**
     * add point when not installed wps then show notice dialog
     */
    public static void addShowInstallWpsDialogEvent() {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_NOT_INSTALL_WPS, DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_EXPORT,
                RecorderUserAction.EVENT_CONVERT_EXPORT_NOTICE_INSTALL_WPS,
                eventInfo, false);
        DebugUtil.i(TAG, "addShowInstallWpsDialogEvent");
    }

    /**
     * add point when click show segmented switch
     * 点击关闭分段单选按钮
     */
    public static void addClickSegmentedSwitchEventOnShare(String value) {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_SHARE_TXT_SETTING_SWITCH, value);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_EXPORT,
                RecorderUserAction.EVENT_CONVERT_EXPORT_SWITCH_SEGMENTED,
                eventInfo, false);
        DebugUtil.i(TAG, "addClickSegmentedSwitchEvent");
    }

    /**
     * add point when click show date switch
     * 点击关闭时间单选按钮
     */
    public static void addClickDateSwitchEventOnShare(String value) {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_SHARE_TXT_SETTING_SWITCH, value);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_EXPORT,
                RecorderUserAction.EVENT_CONVERT_EXPORT_SWITCH_TIME,
                eventInfo, false);
        DebugUtil.i(TAG, "addClickDateSwitchEvent");
    }

    /**
     * add point when click show speaker switch
     * 点击关闭讲话人单选按钮
     */
    public static void addClickSpeakerSwitchEventOnShare(String value) {
        HashMap<String, String> eventInfo = new HashMap<String, String>();
        eventInfo.put(KEY_SHARE_TXT_SETTING_SWITCH, value);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_EXPORT,
                RecorderUserAction.EVENT_CONVERT_EXPORT_SWITCH_SPEAKER,
                eventInfo, false);
        DebugUtil.i(TAG, "addClickSpeakerSwitchEvent");
    }

    /**
     * 点击文本页搜索按钮,统计进入搜索页面
     */
    public static void addInConvertSearchEvent(int count) {
        HashMap<String, Integer> eventInfo = new HashMap<String, Integer>();
        eventInfo.put(RecorderUserAction.IN_SEARCH, 1); //是否进入搜索页面
        eventInfo.put(RecorderUserAction.COUNT, count); //进入的次数
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT_SEARCH,
                eventInfo, false);
        DebugUtil.i(TAG, "addInConvertSearchEvent count:" + count);
    }

    /**
     * 点击关键词标签的次数
     *
     * @param count 点击的次数
     */
    public static void clickKeyWordChipEvent(int count) {
        HashMap<String, Integer> eventInfo = new HashMap<String, Integer>();
        eventInfo.put(RecorderUserAction.COUNT, count); // 点击次数
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_KEY_WORD,
                eventInfo, false);
        DebugUtil.i(TAG, "clickKeyWordChipEvent count:" + count);
    }

    /**
     * 转文本失败错误信息
     * @param uploadCode 上传错误码
     * @param convertCode 转文本错误码
     * @param msg 错误信息
     */
    public static void addConvertFailedMessage(int uploadCode, int convertCode, String msg) {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CONVERT_FAIL_MESSAGE, msg);
        eventInfo.put(KEY_CONVERT_UPLOAD_CODE, String.valueOf(uploadCode));
        eventInfo.put(KEY_CONVERT_CONVERT_CODE, String.valueOf(convertCode));
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                EVENT_CONVERT_FAIL_MESSAGE,
                eventInfo, false);
        DebugUtil.i(TAG, "convertFailedMessage msg:" + msg);
    }

    /**
     * 点击音频tab-转文本
     */
    public static void addConvertText() {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CONVERT_TEXT, DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
    }

    /**
     * 点击音频tab-查看文本
     */
    public static void addLookText() {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_LOOK_TEXT, DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
    }

    /**
     * 录音文件-文本tab-点击“取消转写”按钮-取消转写弹框-点击弹框中“取消转写”按钮
     */
    public static void addConvertCancelDialog() {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CONVERT_CANCEL_DIALOG, DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
    }

    /**
     * 录音文件-文本tab-点击“取消转写”按钮-取消转写弹框-点击弹框中“继续转写”按钮
     */
    public static void addConvertContinueDialog() {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CONVERT_CONTINUE_DIALOG, DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
    }

    /**
     * 点击文本tab-分享文本按钮
     */
    public static void addShareText() {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CLICK_SHARE_TEXT, DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, false);
    }

    /**
     * 录音文件-文本-点击分享文本-分享文本弹框-分享文本弹框弹出次数
     */
    public static void addClickExport(String complete) {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_EXPORT_CLICK_EXPORT, complete);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, true);
    }

    /**
     * 录音文件-文本tab-分享文本-分享文本弹框-以doc格式分享
     */
    public static void addExportDoc() {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_EXPORT_DOC, DEFAULT_VALUE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, true);
    }

    public static void addSendToNote() {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_EXPORT_TO_NOTE, VALUE_EXPORT_TO_NOTE);
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT,
                eventInfo, true);
    }

    /**
     * 点被转写的文件的时长
     */
    public static void addConvertFileDuration(Long duration) {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CONVERT_FILE_DURATION, duration.toString());
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT_DURATION,
                eventInfo, false);
    }

    /**
     * 从开始转写到转写成功，耗费的时间
     */
    public static void addConvertSuccessDuration(Long duration) {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CONVERT_SUCCESS_DURATION, duration.toString());
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT_DURATION,
                eventInfo, false);
    }

    /**
     * 从开始转写到取消转写耗费的时间
     */
    public static void addConvertCancelDuration(Long duration) {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CONVERT_CANCEL_DURATION, duration.toString());
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT_DURATION, eventInfo, false);
    }

    /**
     * 从开始转写到转写失败，耗费的总时长
     */
    public static void addConvertFailDuration(Long duration) {
        HashMap<String, String> eventInfo = new HashMap();
        eventInfo.put(KEY_CONVERT_FAIL_DURATION, duration.toString());
        RecorderUserAction.addNewCommonUserAction(BaseApplication.getAppContext(),
                RecorderUserAction.USER_ACTION_CONVERT,
                RecorderUserAction.EVENT_CONVERT_DURATION,
                eventInfo, false);
    }
}
