/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ConvertDeleteUtil.java
 * * Description: ConvertDeleteUtil.java
 * *              class to process delete trans files and db
 * * Version: 1.0
 * * Date : 2019/9/25
 * * Author: huangyuanwang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * huangyuanwang  2019/9/25      1.0    build this module
 ****************************************************************/

package com.soundrecorder.common.db;

import android.content.Context;
import android.text.TextUtils;

import java.io.File;

import com.soundrecorder.base.ext.ExtKt;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.utils.ConvertDbUtil;
import com.soundrecorder.common.utils.FunctionOption;

public class ConvertDeleteUtil {

    private static final String TAG = "ConvertDeleteUtil";

    public static boolean deleteConvertData(Context context, long recordId) {
        DebugUtil.d(TAG, "deleteConvertData recordId:" + recordId);
        boolean textFileDeleted = false;
        boolean convertDbRecordDeleted = false;
        boolean mediumFilesDeleted = false;
        ConvertRecord convertRecord = ConvertDbUtil.selectByRecordId(recordId);
        if (convertRecord != null) {
            // delete key word db
            deleteKeyWords(recordId);

            //delete Text Files
            String textFilePath = convertRecord.getConvertTextfilePath();
            if (!TextUtils.isEmpty(textFilePath)) {
                File file = new File(textFilePath);
                if (file.exists()) {
                    textFileDeleted = file.delete();
                    DebugUtil.i(TAG, "text file " + file.getName() + ", deleted: " + textFileDeleted);
                } else {
                    textFileDeleted = true;
                }
            }
            int deleteCount = ConvertDbUtil.deleteByRecordId(recordId);
            DebugUtil.i(TAG, "convertRecord recordId " + recordId + ", deleteCount: " + deleteCount);
            convertDbRecordDeleted = deleteCount > 0;
        } else {
            textFileDeleted = true;
            convertDbRecordDeleted = true;
        }

        // delete medium files
        File opusDir = new File(context.getFilesDir(), String.valueOf(recordId));
        if (opusDir.exists()) {
            mediumFilesDeleted = deleteDir(opusDir);
            DebugUtil.i(TAG, "opus dir " + opusDir.getName() + ", deleted: " + mediumFilesDeleted);
        } else {
            mediumFilesDeleted = true;
        }
        return textFileDeleted && convertDbRecordDeleted && mediumFilesDeleted;
    }

    /**
     *
     * @param context
     * @param mediaPath /storage/emulated/0/Music/Recordings/Standard Recordings/标准录音 2.mp3
     * @return
     */
    public static boolean deleteConvertData(Context context, String mediaPath) {
        DebugUtil.d(TAG, "deleteConvertData fileName:" + ExtKt.displayName(mediaPath));
        if (TextUtils.isEmpty(mediaPath)) {
            return false;
        }
        boolean textFileDeleted = false;
        boolean convertDbRecordDeleted = false;
        boolean mediumFilesDeleted = false;
        ConvertRecord convertRecord = ConvertDbUtil.selectByMediaPath(mediaPath);
        if (convertRecord != null) {
            // delete key word db
            deleteKeyWords(convertRecord.getRecordId());

            //delete Text Files
            String textFilePath = convertRecord.getConvertTextfilePath();
            if (!TextUtils.isEmpty(textFilePath)) {
                File file = new File(textFilePath);
                if (file.exists()) {
                    textFileDeleted = file.delete();
                    DebugUtil.i(TAG, "text file " + file.getName() + ", deleted: " + textFileDeleted);
                } else {
                    textFileDeleted = true;
                }
            }
            int deleteCount = ConvertDbUtil.deleteByRecordId(convertRecord.getRecordId());
            DebugUtil.i(TAG, "convertRecord recordId " + convertRecord.getRecordId() + ", deleteCount: " + deleteCount);
            convertDbRecordDeleted = deleteCount > 0;

            // delete medium files
            File opusDir = new File(context.getFilesDir(), String.valueOf(convertRecord.getRecordId()));
            if (opusDir.exists()) {
                mediumFilesDeleted = deleteDir(opusDir);
                DebugUtil.i(TAG, "opus dir " + opusDir.getName() + ", deleted: " + mediumFilesDeleted);
            } else {
                mediumFilesDeleted = true;
            }
        } else {
            textFileDeleted = true;
            convertDbRecordDeleted = true;
            mediumFilesDeleted = true;
        }
        return textFileDeleted && convertDbRecordDeleted && mediumFilesDeleted;
    }

    /**
     * 删除录音文件对应的关键词
     *
     * @param recordId
     * @return
     */
    private static void deleteKeyWords(long recordId) {
        if (!FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) {
            DebugUtil.e(TAG, "deleteKeyWords not support key words, so ignore delete " + recordId);
            return;
        }
        KeyWordDbUtils.INSTANCE.deleteKeyWords(recordId);
    }


    private static boolean deleteDir(File file) {
        boolean deleteSuc = false;
        if (file.isFile()) {
            deleteSuc = file.delete();
        } else {
            File[] files = file.listFiles();
            if (files == null) {
                deleteSuc = file.delete();
            } else {
                for (int i = 0; i < files.length; i++) {
                    deleteDir(files[i]);
                }
                deleteSuc = file.delete();
            }
        }
        return deleteSuc;
    }



}
