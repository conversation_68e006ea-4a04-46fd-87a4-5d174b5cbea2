/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NoteDbUtils.kt
 * * Description : 回收站DB工具类
 * * Version     : 1.0
 * * Date        : 2024/10/28
 * * Author      : 80266877
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.db

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.net.Uri
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.NoteDbUtils.COLUMN_NAME_MEDIA_ID
import com.soundrecorder.common.db.NoteDbUtils.COLUMN_NAME_NOTE_ID
import com.soundrecorder.common.db.NoteDbUtils.COLUMN_NAME_RECORD_UUID
import com.soundrecorder.common.db.NoteDbUtils.DATABASE_VERSION_NOTE
import com.soundrecorder.common.db.NoteDbUtils.TABLE_NOTE_NAME
import com.soundrecorder.common.db.NoteDbUtils.TYPE_INSERT_FAILED
import com.soundrecorder.common.db.NoteDbUtils.TYPE_INSERT_SUCCESS
import com.soundrecorder.common.db.NoteDbUtils.TYPE_UPDATE_FAILED
import com.soundrecorder.common.db.NoteDbUtils.TYPE_UPDATE_SUCCESS
import java.io.File

/**
 * 回收站的DB工具类
 */
object RecycleBinDbUtils {

    private const val TAG = "RecycleBinDbUtils"

    const val TFIDF_VALUE = "tfidfValue"
    const val FILE_DIRECTORY_NAME = ".SoundRecordRecycler"

    val recycleBinUri: Uri = DatabaseConstant.getContentUri(DatabaseConstant.TABLE_NAME_RECYCLE_BIN)

    /**
     * 创建表
     */
    @JvmStatic
    fun createRecycleBinTable(db: SQLiteDatabase) {
        DebugUtil.e(TAG, "crateNoteTable")
        db.execSQL(
            "CREATE TABLE IF NOT EXISTS " + DatabaseConstant.TABLE_NAME_RECYCLE_BIN + " ("
                    + DatabaseConstant.RecycleBinColumn.ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                    + DatabaseConstant.RecycleBinColumn.DISPLAY_NAME + " TEXT, "
                    + DatabaseConstant.RecycleBinColumn.NAME_MD5 + " TEXT,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_FILE_ID + " TEXT,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_OLD_RECORD_ID + " INTEGER DEFAULT 0,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_OLD_MEDIA_ID + " INTEGER DEFAULT 0,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_GLOBAL_ID + " TEXT,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_RECORD_TYPE + " INTEGER DEFAULT 0,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_FILE_RECYCLE_PATH + " TEXT,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_DELETE_TIME + "  BIGINT DEFAULT 0,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_FILE_CREATE_TIME + "  BIGINT DEFAULT 0,"
                    + DatabaseConstant.RecycleBinColumn.COLUMN_NAME_DIRTY + " INTEGER DEFAULT 0"
                    + ");"
        )
    }


    /**
     * 录音文件移动到回收站目录以后， 把对应信息插入数据库
     */
    @JvmStatic
    fun insertRecycleRecord(context: Context, file: File, record: Record) {
        val values = ContentValues().apply {
            put(DatabaseConstant.RecycleBinColumn.DISPLAY_NAME, file.name)
            put(DatabaseConstant.RecycleBinColumn.NAME_MD5, record.mD5)
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_FILE_ID, record.fileId)
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_OLD_RECORD_ID, record.id)
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_OLD_MEDIA_ID, record.id)
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_GLOBAL_ID, record.globalId)
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_RECORD_TYPE, record.recordType)
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_FILE_RECYCLE_PATH, file.absolutePath)
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_DELETE_TIME, System.currentTimeMillis())
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_FILE_CREATE_TIME, record.dateCreated)
            put(DatabaseConstant.RecycleBinColumn.COLUMN_NAME_DIRTY, 0)
        }
        context.contentResolver.insert(recycleBinUri, values)
    }

    /**
     * 录音文件移动到回收站目录以后， 把对应信息插入数据库
     */
    @JvmStatic
    fun queryRecycleRecord(context: Context, file: File, mediaId: Long, record: Record): Cursor? {
        return context.contentResolver.query(recycleBinUri, null, null, null, null)
    }

    /**
     * 升级note表 （summary表）
     * @param db
     * @param fromVersion 老版本
     * @param toVersion 新版本
     */
    @JvmStatic
    fun upgradeNoteTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        DebugUtil.e(TAG, "upgradeNoteTable from:$fromVersion to:$toVersion")
        if (fromVersion >= toVersion) { // 升级时，from < to ,如果大于等于，则说明传入的值错误
            DebugUtil.e(TAG, "upgradeNoteTable version error")
            return
        }
    }

    /**
     * 降级数据库表note
     * @param db
     * @param fromVersion
     * @param toVersion
     */
    @JvmStatic
    fun downgradeNoteTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        DebugUtil.e(TAG, "downgradeNoteTable from:$fromVersion to:$toVersion")
        if (fromVersion <= toVersion) { // 降级时，from > to，如果小于等于，则说明版本错误
            DebugUtil.e(TAG, "downgradeNoteTable version error")
            return
        }

        for (version in (fromVersion downTo toVersion + 1)) {
            when (version) { //只处理 version >= 11 的情况
                DATABASE_VERSION_NOTE -> dropNoteTable(db)
            }
        }
    }

    /**
     * 删除 note表
     */
    @JvmStatic
    fun dropNoteTable(db: SQLiteDatabase) {
        DebugUtil.e(TAG, "dropNoteTable")
        db.execSQL("DROP TABLE IF EXISTS $TABLE_NOTE_NAME;")
    }

    @JvmStatic
    private fun getContentResolver() = BaseApplication.getAppContext().contentResolver

    /**
     * 添加摘要
     * @param noteData 摘要信息
     * @return true 添加成功； false 失败
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun addNote(noteData: NoteData): Boolean {
        try {
            val contentValues = ContentValues()
            val contentResolver = getContentResolver()
            contentResolver.insert(recycleBinUri, contentValues)
            DebugUtil.d(TAG, "addNote noteData:$noteData")
            return true
        } catch (e: Exception) {
            DebugUtil.e(TAG, "addNote error", e)
        }
        return false
    }


    /**
     * 根据noteId删除对应的摘要
     * @param noteId 摘要noteId
     */
    @JvmStatic
    fun deleteNoteByNoteId(noteId: String): Int {
        return deleteNote(COLUMN_NAME_NOTE_ID, noteId)
    }

    /**
     * 根据noteId批量删除对应的摘要
     * @param noteIdList 摘要noteId列表
     */
    @JvmStatic
    fun deleteNotesByNoteId(noteIdList: List<String>): Int {
        if (noteIdList.isEmpty()) {
            return 0
        }
        return deleteNotes(COLUMN_NAME_NOTE_ID, noteIdList)
    }

    /**
     * 根据mediaId删除对应的摘要
     * @param mediaId 音频文件媒体库ID
     */
    @JvmStatic
    fun deleteNoteByMediaId(mediaId: String): Int {
        return deleteNote(COLUMN_NAME_MEDIA_ID, mediaId)
    }


    @JvmStatic
    fun clearAllNoteData() {
        val where = " $COLUMN_NAME_RECORD_UUID not null"
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(recycleBinUri, where, null)
            DebugUtil.d(TAG, "clearAllNoteData deleteCount:$count")
        }.onFailure {
            DebugUtil.e(TAG, "clearAllNoteData error ", it)
        }
    }

    @JvmStatic
    private fun deleteNote(column: String, columnValue: String): Int {
        val where = " $column = ? "
        val args = arrayOf(columnValue)
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(recycleBinUri, where, args)
            DebugUtil.d(TAG, "deleteNote column:$column columnValue:$columnValue deleteCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "deleteNote error column:$column columnValue:$columnValue", it)
        }
        return 0
    }

    @JvmStatic
    private fun deleteNotes(column: String, columnValues: List<String>): Int {
        val where = MediaDBUtils.getWhereForInKeyword(columnValues.size, column).toString()
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(recycleBinUri, where, columnValues.toTypedArray())
            DebugUtil.d(TAG, "deleteNote column:$column size:${columnValues.size} deleteCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "deleteNote error column:$column size:${columnValues.size}", it)
        }
        return 0
    }

    /**
     * 更新摘要
     * @param noteDate 新的摘要信息
     * @return 成功更新数据的个数
     */
    @JvmStatic
    fun updateNoteByMediaId(noteDate: NoteData): Int {
        val where = " $COLUMN_NAME_MEDIA_ID = ? "
        val args = arrayOf(noteDate.mediaId)

        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val contentValues = ContentValues()
            val count = contentResolver.update(recycleBinUri, contentValues, where, args)
            DebugUtil.d(TAG, "updateNoteByMediaId noteDate:$noteDate, updateCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "updateNoteByMediaId noteDate:$noteDate, $it")
        }
        return 0
    }

    /**
     * 插入或更新已有的摘要，该方法是通过uuid来判断是否有重复的
     * @param noteDate 摘要信息
     * @return TYPE_INSERT_SUCCESS:插入成功；TYPE_UPDATE_SUCCESS：更新成功；
     */
    @JvmStatic
    fun updateOrInsertNote(noteDate: NoteData): Int {
        val queryResult = -1
        val opResult = if (queryResult != null) {
            val num = updateNoteByMediaId(noteDate)
            if (num > 0) {
                TYPE_UPDATE_SUCCESS
            } else {
                TYPE_UPDATE_FAILED
            }
        } else {
            val success = addNote(noteDate)
            if (success) {
                TYPE_INSERT_SUCCESS
            } else {
                TYPE_INSERT_FAILED
            }
        }
        DebugUtil.d(TAG, "updateOrInsertNote noteDate:$noteDate, queryResult:$queryResult opResult=$opResult")
        return opResult
    }
}