/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDi<PERSON>orCommon
 ** Description : AutoDi<PERSON><PERSON><PERSON>ommon
 ** Version     : 1.0
 ** Date        : 2025/06/06
 ** Author      : renjiahao
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  renjiahao       2025/06/06       1.0      create
 ***********************************************************************/
package com.soundrecorder.common.di

import com.soundrecorder.common.api.CommonApi
import com.soundrecorder.modulerouter.CommonAction
import org.koin.dsl.module

object AutoDiForCommon {
    val commonModule = module {
        single<CommonAction>(createdAtStart = true) {
            CommonApi
        }
    }
}