/*
Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
File: - ShareSummaryAndTextPreference
Description: 录音分享摘要分享原文底部弹窗button
Version: 1.0
Date : 2025/6/1
Author: W9067780
--------------------- Revision History: ---------------------
<author>	<data> 	  <version >	   <desc>
W9067780  2025/6/1     1.0      create this file
*/
package com.soundrecorder.common.flexible

import android.content.Context
import android.util.AttributeSet
import android.widget.Button
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.preference.COUIJumpPreference
import com.soundrecorder.common.R

class ShareSummaryAndTextPreference : COUIJumpPreference {

    private var onBindOrClickListener: OnBindOrClickListener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun onBindViewHolder(holder: PreferenceViewHolder?) {
        super.onBindViewHolder(holder)
        val button = holder?.itemView?.findViewById<Button>(R.id.dialog_share_button)
        button?.let {
            it.setOnClickListener {
                onBindOrClickListener?.onClick()
            }
        }
    }

    fun setListener(onBindOrClickListener: OnBindOrClickListener) {
        this.onBindOrClickListener = onBindOrClickListener
    }

    interface OnBindOrClickListener {
        fun onClick()
    }
}