/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FollowDialogRestoreUtils
 * Description:跟手弹窗重建
 * Version: 1.0
 * Date: 2023/04/25
 * Author: W9010241(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9010241 2023/04/25 1.0 create
 */

package com.soundrecorder.common.flexible

import android.view.View
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ScreenUtil
import kotlinx.coroutines.Runnable

object FollowDialogRestoreUtils {
    private const val TAG = "FollowDialogRestoreUtils"
    private const val TIME_INTERVAL_500 = 500L
    private var mFollowDialogRunnable: Runnable? = null

    @JvmStatic
    fun followDialogRestore(decorView: View?, supportFollow: Boolean, callBack: FollowRestoreCallBack?) {
        mFollowDialogRunnable = Runnable {
            callBack?.restoreCallBack()
            DebugUtil.d(TAG, "restoreCallBack")
            releaseFollowDialogRunnable(decorView)
        }
        decorView?.removeCallbacks(mFollowDialogRunnable)
        if (supportFollow && !ScreenUtil.isSmallScreen(BaseApplication.getAppContext())) {
            DebugUtil.d(TAG, "post interval 500L")
            decorView?.postDelayed(mFollowDialogRunnable, TIME_INTERVAL_500)
        } else {
            DebugUtil.d(TAG, "post interval 0")
            decorView?.post(mFollowDialogRunnable)
        }
    }

    @JvmStatic
    fun releaseFollowDialogRunnable(decorView: View?) {
        decorView?.removeCallbacks(mFollowDialogRunnable)
        mFollowDialogRunnable = null
    }
}