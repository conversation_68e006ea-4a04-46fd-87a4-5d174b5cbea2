/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NoteDbUtils.kt
 * * Description : 摘要的DB工具类
 * * Version     : 1.0
 * * Date        : 2024/2/28
 * * Author      : W9041435
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.db

import android.content.ContentValues
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.databean.SummaryState
import com.soundrecorder.common.db.NoteDbUtils.SummaryType.SUMMARY_TYPE_CALL_RECORD

/**
 * 摘要的DB工具类
 */
object NoteDbUtils {
    object SummaryType {
        /**
         * 这个值是同便签字段保持一致的
         * 以下是存储在“通话摘要”笔记本
         * 0：通话摘要
         * 1：录音-通话录音摘要
         * 2：录音-QQ通话录音摘要
         * 3：录音-微信通话录音摘要
         * 4：语音转文字-QQ通话录音摘要
         * 5：语音转文字-微信通话录音摘要
         *
         * 以下是存储在“语音摘要”笔记本
         * 6：录音-非通话录音摘要
         * 7：语音转文字-会议软件
         * 8：语音转文字-视频软件
         * 9：语音转文字-其它应用
         *
         * 以下是存储在“文章摘要”笔记本
         * 10：文章摘要
         * 11：全文摘要
         */
        const val SUMMARY_TYPE_COMMON_RECORD = 6
        const val SUMMARY_TYPE_CALL_RECORD = 1
    }

    private const val TAG = "NoteDbUtils"

    const val PRIMARY_ID = "_id"

    /**
     * 摘要的表名
     */
    const val TABLE_NOTE_NAME = "summary"

    const val NEW_TABLE_NOTE_NAME = "summary_cache_data"
    /**
     * 数据库版本
     * 新增summary表对应数据库版本号
     */
    const val DATABASE_VERSION_NOTE = 11

    /**
     * 更新summary表主键ID
     */
    const val DATABASE_VERSION_NOTE_UPDATE_PRIMARY_KEY = 12

    /**
     * 所有录音的uuid
     * 注意：callId可能有相同重复数据，不能作为唯一标识
     */
    const val COLUMN_NAME_RECORD_UUID = "record_uuid"

    /**
     * 录音文件的类型(summary_type)
     */
    const val COLUMN_NAME_RECORD_TYPE = "record_type"

    /**
     * 便签生成摘要数据id，跳转到便签需要使用
     */
    const val COLUMN_NAME_NOTE_ID = "note_id"

    /**
     * 摘要具体内容
     */
    const val COLUMN_NAME_NOTE_CONTENT = "note_content"

    /**
     * 音频文件媒体库ID
     */
    const val COLUMN_NAME_MEDIA_ID = "media_id"

    /**
     * 音频文件路径
     */
    const val COLUMN_NAME_MEDIA_PATH = "media_path"

    /**
     * 音频文件校验标记，预留
     */
    const val COLUMN_NAME_CHECK_FLAG = "check_flag"

    /**
     * 摘要状态
     */
    const val COLUMN_NAME_NOTE_STATE = "note_state"
    /*电话本标识*/
    const val COLUMN_NAME_CONTACT_FLAG = "contact_flag"
    const val SUMMARY_CACHE_DATA_COLUMN_PATH = "file_path"
    const val SUMMARY_CACHE_DATA_COLUMN_ID = "_id"
    const val SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP = "time_stamp"
    const val SUMMARY_CACHE_DATA_COLUMN_RECORD_TYPE = "record_type"
    const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_CONTENT = "summary_content"
    const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_TRACE = "summary_trace"
    const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_STYLE = "summaryStyle"
    const val SUMMARY_CACHE_DATA_COLUMN_MIND_MAP_CONTENT = "mind_map_content"
    const val SUMMARY_CACHE_DATA_COLUMN_MIND_MAP_TRACE = "mind_map_trace"
    const val SUMMARY_CACHE_DATA_COLUMN_CHOOSE_STATE = "choose_state"

    const val PROVIDER_NOTE_TYPE = "vnd.android.cursor.dir/note"

    const val TYPE_INIT: Int = 0
    const val TYPE_INSERT_SUCCESS: Int = 10
    const val TYPE_INSERT_FAILED: Int = 11
    const val TYPE_UPDATE_SUCCESS: Int = 20
    const val TYPE_UPDATE_FAILED: Int = 21

    /* 查询的关键词的参数列表长度 */
    const val SUMMARY_QUERY_ARG_NUMBER: Int = 1
    private val noteUri = DatabaseConstant.getContentUri(TABLE_NOTE_NAME)

    private val newSummaryUri = DatabaseConstant.getContentUri(NEW_TABLE_NOTE_NAME)

    /**
     * 创建note表
     */
    @JvmStatic
    fun createNoteTable(db: SQLiteDatabase) {
        DebugUtil.e(TAG, "crateNoteTable")
        db.execSQL(
            "CREATE TABLE IF NOT EXISTS " + TABLE_NOTE_NAME + " ("
                    + PRIMARY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
                    + COLUMN_NAME_RECORD_UUID + " TEXT NOT NULL,"
                    + COLUMN_NAME_RECORD_TYPE + " INTEGER NOT NULL,"
                    + COLUMN_NAME_NOTE_ID + " TEXT,"
                    + COLUMN_NAME_NOTE_CONTENT + " TEXT,"
                    + COLUMN_NAME_NOTE_STATE + " INTEGER,"
                    + COLUMN_NAME_MEDIA_ID + " TEXT NOT NULL,"
                    + COLUMN_NAME_MEDIA_PATH + " TEXT NOT NULL,"
                    + COLUMN_NAME_CHECK_FLAG + " TEXT,"
                    + COLUMN_NAME_CONTACT_FLAG + " INTEGER"
                    + ");"
        )
    }

    /**
     * 升级note表 （summary表）
     * @param db
     * @param fromVersion 老版本
     * @param toVersion 新版本
     */
    @JvmStatic
    fun upgradeNoteTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        DebugUtil.e(TAG, "upgradeNoteTable from:$fromVersion to:$toVersion")
        if (fromVersion >= toVersion) { // 升级时，from < to ,如果大于等于，则说明传入的值错误
            DebugUtil.e(TAG, "upgradeNoteTable version error")
            return
        }
        for (version in (fromVersion + 1..toVersion)) {
            when (version) { //只处理 version >= 11 的情况
                DATABASE_VERSION_NOTE -> createNoteTable(db)
                DATABASE_VERSION_NOTE_UPDATE_PRIMARY_KEY -> updatePrimaryKey(db)
            }
        }
    }

    @JvmStatic
    fun updatePrimaryKey(db: SQLiteDatabase) {
        kotlin.runCatching {
            val tempSummaryName = "temp_summary"
            val renameOldTable = "ALTER TABLE $TABLE_NOTE_NAME RENAME TO $tempSummaryName"
            db.execSQL(renameOldTable)
            createNoteTable(db)
            val oldColumn = "$COLUMN_NAME_RECORD_UUID, $COLUMN_NAME_RECORD_TYPE, $COLUMN_NAME_NOTE_ID,$COLUMN_NAME_NOTE_CONTENT, " +
                    "$COLUMN_NAME_MEDIA_ID, $COLUMN_NAME_MEDIA_PATH, $COLUMN_NAME_CHECK_FLAG, $COLUMN_NAME_NOTE_STATE, $COLUMN_NAME_CONTACT_FLAG"
            db.execSQL("INSERT INTO $TABLE_NOTE_NAME ($oldColumn) SELECT $oldColumn FROM $tempSummaryName")
            db.execSQL("DROP TABLE IF EXISTS $tempSummaryName")
        }.onFailure {
            DebugUtil.e(TAG, "updatePrimaryKey error $it")
        }
    }

    /**
     * 降级数据库表note
     * @param db
     * @param fromVersion
     * @param toVersion
     */
    @JvmStatic
    fun downgradeNoteTable(db: SQLiteDatabase, fromVersion: Int, toVersion: Int) {
        DebugUtil.e(TAG, "downgradeNoteTable from:$fromVersion to:$toVersion")
        if (fromVersion <= toVersion) { // 降级时，from > to，如果小于等于，则说明版本错误
            DebugUtil.e(TAG, "downgradeNoteTable version error")
            return
        }

        for (version in (fromVersion downTo toVersion + 1)) {
            when (version) { //只处理 version >= 11 的情况
                DATABASE_VERSION_NOTE -> dropNoteTable(db)
            }
        }
    }

    /**
     * 删除 note表
     */
    @JvmStatic
    fun dropNoteTable(db: SQLiteDatabase) {
        DebugUtil.e(TAG, "dropNoteTable")
        db.execSQL("DROP TABLE IF EXISTS $TABLE_NOTE_NAME;")
    }

    @JvmStatic
    private fun getContentResolver() = BaseApplication.getAppContext().contentResolver

    /**
     * 添加摘要
     * @param noteData 摘要信息
     * @return true 添加成功； false 失败
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun addNote(noteData: NoteData): Boolean {
        try {
            val contentValues = getContentValues(noteData)
            val contentResolver = getContentResolver()
            contentResolver.insert(noteUri, contentValues)
            DebugUtil.d(TAG, "addNote noteData:$noteData")
            return true
        } catch (e: Exception) {
            DebugUtil.e(TAG, "addNote error", e)
        }
        return false
    }

    /**
     * 获取ContentValues
     */
    @JvmStatic
    private fun getContentValues(noteData: NoteData): ContentValues {
        val contentValues = ContentValues().apply {
            if (noteData.recordUUID.isNotEmpty()) {
                put(COLUMN_NAME_RECORD_UUID, noteData.recordUUID)
            }
            put(COLUMN_NAME_RECORD_TYPE, noteData.recordType)
            noteData.noteId?.let {
                put(COLUMN_NAME_NOTE_ID, it)
            }
            noteData.noteContent?.let {
                put(COLUMN_NAME_NOTE_CONTENT, it)
            }
            put(COLUMN_NAME_MEDIA_ID, noteData.mediaId)
            put(COLUMN_NAME_MEDIA_PATH, noteData.mediaPath)
            put(COLUMN_NAME_CONTACT_FLAG, if (noteData.contactFlag == true) 1 else 0)
            noteData.noteState?.let {
                put(COLUMN_NAME_NOTE_STATE, it)
            }
            noteData.checkFlag?.let {
                put(COLUMN_NAME_CHECK_FLAG, it)
            }
        }
        return contentValues
    }

    /**
     * 根据noteId删除对应的摘要
     * @param noteId 摘要noteId
     */
    @JvmStatic
    fun deleteNoteByNoteId(noteId: String): Int {
        return deleteNote(COLUMN_NAME_NOTE_ID, noteId)
    }

    /**
     * 根据noteId批量删除对应的摘要
     * @param noteIdList 摘要noteId列表
     */
    @JvmStatic
    fun deleteNotesByNoteId(noteIdList: List<String>): Int {
        if (noteIdList.isEmpty()) {
            return 0
        }
        return deleteNotes(COLUMN_NAME_NOTE_ID, noteIdList)
    }

    /**
     * 根据mediaId删除对应的摘要
     * @param mediaId 音频文件媒体库ID
     */
    @JvmStatic
    fun deleteNoteByMediaId(mediaId: String): Int {
        return deleteNote(COLUMN_NAME_MEDIA_ID, mediaId)
    }


    @JvmStatic
    fun clearAllNoteData() {
        val where = " $COLUMN_NAME_RECORD_UUID not null"
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(noteUri, where, null)
            DebugUtil.d(TAG, "clearAllNoteData deleteCount:$count")
        }.onFailure {
            DebugUtil.e(TAG, "clearAllNoteData error ", it)
        }
    }

    @JvmStatic
    private fun deleteNote(column: String, columnValue: String): Int {
        val where = " $column = ? "
        val args = arrayOf(columnValue)
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(noteUri, where, args)
            DebugUtil.d(TAG, "deleteNote column:$column columnValue:$columnValue deleteCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "deleteNote error column:$column columnValue:$columnValue", it)
        }
        return 0
    }

    @JvmStatic
    private fun deleteNotes(column: String, columnValues: List<String>): Int {
        val where = MediaDBUtils.getWhereForInKeyword(columnValues.size, column).toString()
        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val count = contentResolver.delete(noteUri, where, columnValues.toTypedArray())
            DebugUtil.d(TAG, "deleteNote column:$column size:${columnValues.size} deleteCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "deleteNote error column:$column size:${columnValues.size}", it)
        }
        return 0
    }

    /**
     * 根据录音文件uuid，查询对应的摘要数据
     * @param recordUUID 录音文件uuid
     * @return 若uuid存在，则返回NoteData，否则返回null
     */
    @JvmStatic
    fun queryNoteByUUID(recordUUID: String): NoteData? {
        return queryNote(COLUMN_NAME_RECORD_UUID, recordUUID)
    }

    @JvmStatic
    fun queryNoteByNoteId(noteId: String): NoteData? {
        return queryNote(COLUMN_NAME_NOTE_ID, noteId)
    }

    /**
     * 根据音频文件媒体库ID，查询对应的摘要数据
     * @param mediaId 音频文件媒体库ID
     * @return 若存在，则返回NoteData，否则返回null
     */
    @JvmStatic
    fun queryNoteByMediaId(mediaId: String): NoteData? {
        return queryNote(COLUMN_NAME_MEDIA_ID, mediaId)
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    private fun queryNote(column: String, columnValue: String): NoteData? {
        val where = " $column = ? "
        val args = arrayOf(columnValue)
        var cursor: Cursor? = null
        try {
            val contentResolver = getContentResolver()
            cursor = contentResolver.query(noteUri, null, where, args, null)
            cursor?.let {
                DebugUtil.d(TAG, "queryNote column:$column columnValue:$columnValue --> count=${it.count}")
                if (it.moveToFirst()) {
                    val recordUUIDIndex = it.getColumnIndexOrThrow(COLUMN_NAME_RECORD_UUID)
                    val recordTypeIndex = it.getColumnIndexOrThrow(COLUMN_NAME_RECORD_TYPE)
                    val noteIdIndex = it.getColumnIndexOrThrow(COLUMN_NAME_NOTE_ID)
                    val noteContentIndex = it.getColumnIndexOrThrow(COLUMN_NAME_NOTE_CONTENT)
                    val mediaIdIndex = it.getColumnIndexOrThrow(COLUMN_NAME_MEDIA_ID)
                    val mediaPathIndex = it.getColumnIndexOrThrow(COLUMN_NAME_MEDIA_PATH)
                    val noteStateIndex = it.getColumnIndexOrThrow(COLUMN_NAME_NOTE_STATE)
                    val checkFlagIndex = it.getColumnIndexOrThrow(COLUMN_NAME_CHECK_FLAG)
                    val contactFlagIndex = it.getColumnIndexOrThrow(COLUMN_NAME_CONTACT_FLAG)

                    val recordUUID = it.getString(recordUUIDIndex)
                    val recordType = it.getInt(recordTypeIndex)
                    val noteId = it.getString(noteIdIndex)
                    val noteContent = it.getString(noteContentIndex)
                    val mediaId = it.getString(mediaIdIndex)
                    val mediaPath = it.getString(mediaPathIndex)
                    val noteState = it.getInt(noteStateIndex)
                    val checkFlag = it.getString(checkFlagIndex)

                    val noteData = NoteData(recordUUID, recordType, noteId, noteContent,
                        mediaId, mediaPath, checkFlag, noteState).apply {
                        contactFlag = it.getInt(contactFlagIndex) == 1
                    }
                    DebugUtil.d(TAG, "queryNote result -->  NoteData:$noteData")
                    return noteData
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryNote error column:$column columnValue:$columnValue", e)
        } finally {
            cursor?.close()
        }

        return null
    }

    /**
     * 查询所有的摘要
     * @return 摘要列表
     */
    @JvmStatic
    fun queryAllNotes(): List<NoteData> = queryNotes()

    @JvmStatic
    fun queryCallNotes(): List<NoteData> =
        queryNotes(selection = "$COLUMN_NAME_RECORD_TYPE = ? ", selectionArgs = arrayOf(SUMMARY_TYPE_CALL_RECORD.toString()))

    @JvmStatic
    fun queryNotesByKeyWord(keyWord: String?): List<NoteData> {
        // 关键词为null直接返回
        if (keyWord.isNullOrEmpty()) {
            return emptyList()
        }

        val projection = arrayOf(PRIMARY_ID, COLUMN_NAME_RECORD_UUID, COLUMN_NAME_RECORD_TYPE,
            COLUMN_NAME_NOTE_ID, COLUMN_NAME_NOTE_CONTENT, COLUMN_NAME_NOTE_STATE, COLUMN_NAME_MEDIA_ID,
            COLUMN_NAME_MEDIA_PATH, COLUMN_NAME_CHECK_FLAG)

        val selection = "$COLUMN_NAME_NOTE_CONTENT GLOB ?"
        val selectionArgs = Array(SUMMARY_QUERY_ARG_NUMBER) { "\'*$keyWord*\'" }
        return queryNotes(projection, selection, selectionArgs, null)
    }

    @JvmStatic
    private fun queryNotes(
        projection: Array<String>? = null,
        selection: String? = null,
        selectionArgs: Array<String>? = null,
        sortOrder: String? = null
    ): List<NoteData> {
        try {
            getContentResolver().query(noteUri, projection, selection, selectionArgs, sortOrder)?.use {
                if (it.moveToFirst()) {
                    val recordUUIDIndex = it.getColumnIndexOrThrow(COLUMN_NAME_RECORD_UUID)
                    val recordTypeIndex = it.getColumnIndexOrThrow(COLUMN_NAME_RECORD_TYPE)
                    val noteIdIndex = it.getColumnIndexOrThrow(COLUMN_NAME_NOTE_ID)
                    val noteContentIndex = it.getColumnIndexOrThrow(COLUMN_NAME_NOTE_CONTENT)
                    val mediaIdIndex = it.getColumnIndexOrThrow(COLUMN_NAME_MEDIA_ID)
                    val mediaPathIndex = it.getColumnIndexOrThrow(COLUMN_NAME_MEDIA_PATH)
                    val noteStateIndex = it.getColumnIndexOrThrow(COLUMN_NAME_NOTE_STATE)
                    val checkFlagIndex = it.getColumnIndexOrThrow(COLUMN_NAME_CHECK_FLAG)
                    val contactFlagIndex = it.getColumnIndexOrThrow(COLUMN_NAME_CONTACT_FLAG)

                    val list = mutableListOf<NoteData>()
                    do {
                        val recordUUID = it.getString(recordUUIDIndex)
                        val recordType = it.getInt(recordTypeIndex)
                        val noteId = it.getString(noteIdIndex)
                        val noteContent = it.getString(noteContentIndex)
                        val mediaId = it.getString(mediaIdIndex)
                        val mediaPath = it.getString(mediaPathIndex)
                        val noteState = it.getInt(noteStateIndex)
                        val checkFlag = it.getString(checkFlagIndex)

                        list.add(
                            NoteData(
                                recordUUID, recordType, noteId, noteContent, mediaId, mediaPath, checkFlag, noteState).apply {
                                    contactFlag = it.getInt(contactFlagIndex) == 1
                            })
                    } while (it.moveToNext())

                    DebugUtil.d(TAG, "queryNotes list:${list.size},selection=$selection,selectionArgs=$selectionArgs")
                    return list
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "queryNotes error", e)
        }
        return emptyList()
    }

    /**
     * 更新摘要
     * @param noteDate 新的摘要信息
     * @return 成功更新数据的个数
     */
    @JvmStatic
    fun updateNoteByMediaId(noteDate: NoteData): Int {
        val where = " $COLUMN_NAME_MEDIA_ID = ? "
        val args = arrayOf(noteDate.mediaId)

        kotlin.runCatching {
            val contentResolver = getContentResolver()
            val contentValues = getContentValues(noteDate)
            val count = contentResolver.update(noteUri, contentValues, where, args)
            DebugUtil.d(TAG, "updateNoteByMediaId noteDate:$noteDate, updateCount:$count")
            return count
        }.onFailure {
            DebugUtil.e(TAG, "updateNoteByMediaId noteDate:$noteDate, $it")
        }
        return 0
    }

    /**
     * 插入或更新已有的摘要，该方法是通过uuid来判断是否有重复的
     * @param noteDate 摘要信息
     * @return TYPE_INSERT_SUCCESS:插入成功；TYPE_UPDATE_SUCCESS：更新成功；
     */
    @JvmStatic
    fun updateOrInsertNote(noteDate: NoteData): Int {
        val queryResult = queryNoteByMediaId(noteDate.mediaId)
        val opResult = if (queryResult != null) {
            val num = updateNoteByMediaId(noteDate)
            if (num > 0) {
                TYPE_UPDATE_SUCCESS
            } else {
                TYPE_UPDATE_FAILED
            }
        } else {
            val success = addNote(noteDate)
            if (success) {
                TYPE_INSERT_SUCCESS
            } else {
                TYPE_INSERT_FAILED
            }
        }
        DebugUtil.d(TAG, "updateOrInsertNote noteDate:$noteDate, queryResult:$queryResult opResult=$opResult")
        return opResult
    }
}