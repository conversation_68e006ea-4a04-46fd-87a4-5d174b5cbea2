/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  GroupStateBuryingPointManager
 * * Description: GroupStateBuryingPointManager
 * * Version: 1.0
 * * Date : 2025/5/23
 * * Author: W9021607
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9021607    2025/5/23   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.content.SharedPreferences
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.GroupInfoDbUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 分组状态埋点管理器
 * 负责收集分组信息并执行埋点上报，使用全局协程确保埋点操作完成
 */
object GroupStateBuryingPointManager {
    private const val TAG = "GroupStateBuryingPoint"
    private const val ONE_WEEK_MS = 7 * 24 * 60 * 60 * 1000
    private const val LAST_EXEC_TIME = "last_exec_time"
    private const val FLAG_COUNT = 5
    private const val WEEKLY_PREFS_NAME = "WeeklyCoroutinePrefs"

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    private val prefs: SharedPreferences by lazy {
        BaseApplication.getAppContext().getSharedPreferences(WEEKLY_PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 分组状态埋点，一周上报一次
     * 通过存储上次执行时间到SharedPreferences，判断是否已经过了一周
     */
    fun checkAndReportGroupState() {
        /* 获取当前时间 */
        val currentTime = System.currentTimeMillis()

        /* 获取上次执行时间 */
        val lastExecTime = prefs.getLong(LAST_EXEC_TIME, 0)

        /* 检查是否已经过了一周 */
        if (currentTime - lastExecTime >= ONE_WEEK_MS) {
            // 使用应用级协程在IO线程中执行数据库操作
            applicationScope.launch(Dispatchers.IO) {
                /* 收集分组信息 */
                val myList = GroupInfoDbUtil.getAllGroupInfoList(BaseApplication.getAppContext())
                val group = myList.mapNotNull { info ->
                    info.takeIf {
                        it.mGroupName.isNotBlank() && it.mGroupCount >= 0
                    }?.let { validInfo ->
                        "${validInfo.mGroupName}:${validInfo.mGroupCount}"
                    }
                }.joinToString(separator = ",")
                val names = myList.mapNotNull { info ->
                    info.takeIf {
                        it.mGroupName.isNotBlank() && it.mId >= FLAG_COUNT
                    }?.let { validInfo ->
                        validInfo.mGroupName
                    }
                }.joinToString(separator = ",")

                /* 执行埋点 */
                BuryingPoint.addRecordGroupState(group, names)

                /* 更新上次执行时间 */
                prefs.edit().putLong(LAST_EXEC_TIME, currentTime).apply()
                DebugUtil.d(TAG, "checkAndReportGroupState: buryPointGroupState: addRecordGroupState，update time=$currentTime")
            }
        } else if (currentTime - lastExecTime < 0) {
            /* 异常场景，将sp值重新复制当前时间 */
            prefs.edit().putLong(LAST_EXEC_TIME, currentTime).apply()
            DebugUtil.d(TAG, "checkAndReportGroupState: time Exception: lastExecTime=$lastExecTime, currentTime=$currentTime")
        }
    }
}
