/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: BuryingPointRealTimeSubtitle.kt
 * Description: BuryingPointRealTimeSubtitle.kt
 * Version: 1.0
 * Date: 2025/6/14
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/6/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.common.buryingpoint

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.recorder.IBuryingPointRealTimeSubtitle

class BuryingPointRealTimeSubtitle : IBuryingPointRealTimeSubtitle {
    companion object {
        private const val TAG = "BuryingPointRealTimeSubtitle"
    }

    var uniFileId: String = ""
    var state = mutableMapOf(0 to 0, 1 to 0)
    var fMs: Long = 0
    var returnResult = mutableMapOf(0 to 0, 1 to 0)
    var failReason = mutableMapOf<Int, Int>()

    /*是否上报埋点*/
    private var isValid = false

    private var realTimeSwitchOpen = false

    /**
     * 开关开启关闭次数
     */
    override fun addState(status: Boolean) {
        if (status) {
            state[1] = state[1]?.plus(1) ?: 1
            realTimeSwitchOpen = true
        } else {
            state[0] = state[0]?.plus(1) ?: 1
        }
    }

    /**
     * 转写结果成功
     */
    override fun addReturnSuccess() {
        returnResult[1] = 1
    }

    /**
     * 转写结果失败次数+1
     * @param reason 失败原因
     */
    override fun addReturnFail(reason: Int) {
        returnResult[0]?.let { returnResult[0] = it + 1 }
        failReason[reason] = failReason[reason]?.plus(1) ?: 1
    }

    /**
     * 设置首字上屏耗时
     */
    override fun setFirstWordTime(time: Long) {
        this.fMs = time
    }

    /**
     * 设置是否上报埋点
     */
    override fun setBuryingPointValid(valid: Boolean) {
        this.isValid = valid
    }

    /**
     * 转写开关是否开启过
     */
    override fun isSwitchEverTurnedOn(): Boolean {
        return realTimeSwitchOpen
    }

    /**
     * 上报埋点
     */
    override fun reportBuryingPoint() {
        DebugUtil.i(TAG, "reportBuryingPoint, isValid = $isValid")
        if (isValid) {
            BuryingPoint.addRealTimeSubtitle(this)
        }
    }
}
