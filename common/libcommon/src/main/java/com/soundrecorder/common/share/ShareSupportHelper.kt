/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ShareUtil
 * * Description: ShareUtil
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/6   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.share

import android.app.Activity
import android.content.ComponentName
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.PackageManager.NameNotFoundException
import android.net.Uri
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil.isSuperSoundRecorderEpicEffective
import com.soundrecorder.base.view.DetachableOnClickListener
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.flexible.FollowCOUIAlertDialog
import com.soundrecorder.common.flexible.ShareRecordCOUIDialog
import com.soundrecorder.common.flexible.ShareSummaryAndTextCOUIDialog
import com.soundrecorder.common.flexible.setFollowNegativeButton
import com.soundrecorder.common.utils.EnableAppUtil
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ShareSupportHelper {
    companion object {
        private const val TAG = "ShareSupportHelper"
        private const val PACKAGE_NAME_WPS = "cn.wps.moffice_eng"
        private const val ACTION_WPS_EXPORT = "com.oppo.voice.action.convertwps"

        //便签OPPO及一加内销
        private const val METADATA_NOTE = "com.oplus.note.activity.insert_note"
        private const val PACKAGE_NAME_NOTE = "com.coloros.note"
        private const val PACKAGE_NAME_NOTE_OPLUS = "com.oplus.note"
        private const val NOTE_PROVIDER = "com.nearme.note.db.NotesProvider"
        private const val NOTE_PROVIDER_META_DATA = "com.oplus.note.db.insert_pic_text_note"

        //分享弹窗选择分享类型
        const val SHARE_TYPE_LINK: Int = 1 //链接分享
        const val SHARE_TYPE_AUDIO: Int = 2 //音频分享
        const val SHARE_TYPE_TEXT: Int = 3 //文本分享选项
        const val SHARE_TYPE_AUDIO_AND_TEXT: Int = 4 //音频与文本分享
        const val SHARE_TYPE_ONLY_TEXT: Int = 5 //文本文件分享
        const val SHARE_TYPE_COPY_TEXT: Int = 6 //复制文本
        const val SHARE_TYPE_TEXT_TO_NOTE: Int = 7 //分享文本到便签
        const val SHARE_TYPE_TEXT_TO_DOC: Int = 8 //分享文本到文档应用
        const val SHARE_TYPE_SUMMARY: Int = 9 //分享摘要
        const val SHARE_TYPE_TEXT_TO_PDF: Int = 10 //分享文本到文档应用
    }

    // private var shareDialog: FollowCOUIAlertDialog? = null
    private var shareRecordDialog: ShareRecordCOUIDialog? = null
    private var shareSummaryAndroidTextDialog: ShareSummaryAndTextCOUIDialog? = null
    private var shareTextDialog: FollowCOUIAlertDialog? = null
    private var docOnlySupportTextDialog: AlertDialog? = null
    private var installWpsGuideDialog: AlertDialog? = null
    private var appDisableDialog: AlertDialog? = null
    private var noteNotSupportImgDialog: AlertDialog? = null
    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    /**
     * 显示分享方式选项弹窗
     */
    fun showShareDialog(
        activity: FragmentActivity?,
        anchor: View?,
        dismissListener: DialogInterface.OnDismissListener?,
        childFragmentManager: FragmentManager? = null,
        callBack: (type: Int) -> Unit
    ) {

        if (shareRecordDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "shareDialog is showing")
            return
        }
        if (activity == null) {
            DebugUtil.e(TAG, "activity is null")
            return
        }
        shareRecordDialog = ShareRecordCOUIDialog(childFragmentManager)
        shareRecordDialog!!.showShareRecordDialog { type ->
            if (ClickUtils.isQuickClick()) {
                DebugUtil.i(TAG, "shareRecordDialog is showing")
            }
            when (type) {
                SHARE_TYPE_LINK -> callBack.invoke(SHARE_TYPE_LINK)
                SHARE_TYPE_AUDIO -> callBack.invoke(SHARE_TYPE_AUDIO)
                SHARE_TYPE_TEXT -> callBack.invoke(SHARE_TYPE_TEXT)
                SHARE_TYPE_SUMMARY -> callBack.invoke(SHARE_TYPE_SUMMARY)
            }
            dismissShareDialog()
        }
    }

    fun dismissShareDialog() {
        if (shareRecordDialog?.isShowing() == true) {
            shareRecordDialog?.dismiss()
        }
        shareRecordDialog = null
    }

    fun getExportDialogIsShowing(): Boolean {
        return shareRecordDialog?.isShowing() == true
    }

    fun getExportDialogSupportFollow(): Boolean {
        DebugUtil.e(TAG, "getExportDialogSupportFollow .. ")
        return true
    }

    /**
     * 显示分享文本方式选项弹窗
     */
    fun showShareTextDialog(
        activity: FragmentActivity?,
        anchor: View?,
        callBack: (type: Int) -> Unit
    ) {
        if (activity == null) return
        val itemsNameIdList: ArrayList<Int> = ArrayList()
        if (playbackApi?.isSupportWpsExport() == true && isSuperSoundRecorderEpicEffective()) {
            itemsNameIdList.add(com.soundrecorder.common.R.string.share_as_word_document)
        }
        itemsNameIdList.add(com.soundrecorder.common.R.string.share_as_text_document)
        itemsNameIdList.add(com.soundrecorder.common.R.string.copy_full_text)
        val install = isNoteInstalled()
        if (install) {
            itemsNameIdList.add(com.soundrecorder.common.R.string.export_to_note_new)
        }
        val shareListener =
            DetachableOnClickListener.wrap(DialogInterface.OnClickListener { dialogInterface, which ->
                if (ClickUtils.isQuickClick()) {
                    return@OnClickListener
                }
                when (itemsNameIdList[which]) {
                    com.soundrecorder.common.R.string.share_as_text_document ->
                        callBack.invoke(SHARE_TYPE_ONLY_TEXT)

                    com.soundrecorder.common.R.string.copy_full_text ->
                        callBack.invoke(SHARE_TYPE_COPY_TEXT)

                    com.soundrecorder.common.R.string.export_to_note_new ->
                        callBack.invoke(SHARE_TYPE_TEXT_TO_NOTE)

                    com.soundrecorder.common.R.string.share_as_word_document ->
                        callBack.invoke(SHARE_TYPE_TEXT_TO_DOC)
                }
                dialogInterface.dismiss()
            })
        val exportCancelClickListener =
            DetachableOnClickListener.wrap { dialog: DialogInterface?, _ ->
                ConvertStaticsUtil.addExportCancelEvent()
                dialog?.dismiss()
            }
        val items = itemsNameIdList.map { activity.getString(it) }.toTypedArray()
        val supportFollow = anchor != null
        shareTextDialog = FollowCOUIAlertDialog(activity)
        shareTextDialog?.setAnchorView(anchor)
        shareTextDialog?.initBuilder()?.setItems(items, shareListener)
            ?.setTitle(com.soundrecorder.common.R.string.share_convert)
            ?.setFollowNegativeButton(
                supportFollow,
                com.soundrecorder.common.R.string.cancel,
                exportCancelClickListener
            )
        shareTextDialog?.showDialogDisableFollow()
        val dialog = shareTextDialog?.getAlertDialog()
        dialog?.let {
            shareListener.clearOnDetach(it)
            exportCancelClickListener.clearOnDetach(it)
        }
    }

    fun showShareSummaryDialog(
        isShowPdf: Int,
        activity: FragmentActivity?,
        childFragmentManager: FragmentManager? = null,
        callBack: (type: Int) -> Unit
    ) {
        if (shareSummaryAndroidTextDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "shareDialog is showing")
            return
        }
        if (activity == null) {
            return
        }
        shareSummaryAndroidTextDialog = ShareSummaryAndTextCOUIDialog(childFragmentManager)
        shareSummaryAndroidTextDialog!!.showShareSummaryAndTextDialog(isShowPdf) { type ->
            if (ClickUtils.isQuickClick()) {
                DebugUtil.i(TAG, "shareRecordDialog is showing")
            }
            when (type) {
                SHARE_TYPE_ONLY_TEXT -> callBack.invoke(SHARE_TYPE_ONLY_TEXT)
                SHARE_TYPE_TEXT_TO_DOC -> callBack.invoke(SHARE_TYPE_TEXT_TO_DOC)
                SHARE_TYPE_TEXT_TO_PDF -> callBack.invoke(SHARE_TYPE_TEXT_TO_PDF)
            }
            dismissShareTextDialog()
        }
    }
    fun getExportTextDialogIsShowing(): Boolean {
        if (shareSummaryAndroidTextDialog?.isShowing() == true) {
            return true
        }
        return false
    }

    fun getExportTextDialogSupportFollow(): Boolean {
        DebugUtil.e(TAG, "getExportTextDialogSupportFollow .. ")
        if (true) {
            return true
        }
        return false
    }

    fun dismissShareTextDialog() {
        if (shareSummaryAndroidTextDialog?.isShowing() == true) {
            shareSummaryAndroidTextDialog?.dismiss()
        }
        shareSummaryAndroidTextDialog = null
    }

    fun isSupportWPS(): Boolean = AppUtil.isAppInstalled(PACKAGE_NAME_WPS) &&
            AppUtil.isActionSupport(PACKAGE_NAME_WPS, ACTION_WPS_EXPORT)

    fun showInstallWpsGuideDialog(activity: Activity, callBack: (isAgree: Boolean) -> Unit) {
        val message: String =
            activity.getString(com.soundrecorder.common.R.string.need_install_wps_v2)
        val titleString: String =
            activity.getString(com.soundrecorder.common.R.string.need_install_wps_titile)
        val builder = COUIAlertDialogBuilder(activity)
        builder.setTitle(titleString)
        builder.setMessage(message)
        builder.setPositiveButton(com.soundrecorder.common.R.string.install) { dialog, which ->
            val url = "market://details?id=$PACKAGE_NAME_WPS&style=0"
            val uri = Uri.parse(url)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            val packageManager: PackageManager = activity.packageManager
            kotlin.runCatching {
                val pkgInfo = packageManager.getPackageInfo("com.oppo.market", 0)
                if (pkgInfo?.applicationInfo != null && pkgInfo.applicationInfo?.enabled == true) {
                    intent.setPackage("com.oppo.market")
                }
            }.onFailure {
                DebugUtil.e(TAG, "showInstallWpsGuideDialog $it")
                kotlin.runCatching {
                    val pkgInfo = packageManager.getPackageInfo("com.heytap.market", 0)
                    if (pkgInfo?.applicationInfo != null && pkgInfo.applicationInfo?.enabled == true) {
                        intent.setPackage("com.heytap.market")
                    }
                }.onFailure { e2 ->
                    DebugUtil.e(TAG, "showInstallWpsGuideDialog $e2")
                }
            }
            callBack.invoke(true)
            activity.startActivityForResult(intent, 1)
        }
        builder.setNegativeButton(com.soundrecorder.common.R.string.cancel) { dialog, which ->
            callBack.invoke(false)
            dialog.dismiss()
        }
        installWpsGuideDialog = builder.create()
        installWpsGuideDialog?.setOnCancelListener {
            callBack.invoke(false)
        }
        installWpsGuideDialog?.setCanceledOnTouchOutside(false)
        installWpsGuideDialog?.show()
        ViewUtils.updateWindowLayoutParams(installWpsGuideDialog?.window)
    }

    private fun dismissInstallWpsGuideDialog() {
        if (installWpsGuideDialog?.isShowing == true) {
            installWpsGuideDialog?.dismiss()
        }
        installWpsGuideDialog = null
    }

    fun showDocOnlySupportTextDialog(
        activity: FragmentActivity,
        callBack: (isAgree: Boolean) -> Unit
    ) {
        docOnlySupportTextDialog = COUIAlertDialogBuilder(activity)
            .setTitle(com.soundrecorder.common.R.string.doc_export_not_suppport_image_title)
            .setMessage(com.soundrecorder.common.R.string.save_note_tip_message)
            .setPositiveButton(com.soundrecorder.common.R.string.save_continue) { _, _ ->
                activity.lifecycleScope.launch(Dispatchers.IO) {
                    callBack.invoke(true)
                }
            }
            .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
            .create()
        docOnlySupportTextDialog?.show()
        ViewUtils.updateWindowLayoutParams(docOnlySupportTextDialog?.window)
    }

    private fun dismissDocOnlySupportTextDialog() {
        if (docOnlySupportTextDialog?.isShowing == true) {
            docOnlySupportTextDialog?.dismiss()
        }
        docOnlySupportTextDialog = null
    }

    fun isNoteInstalled(): Boolean {
        /*metadata 已判断未安装场景，此处不重复判断*/
        val hasNoteOplus = AppUtil.metaDataEquals(PACKAGE_NAME_NOTE_OPLUS, METADATA_NOTE)
        if (hasNoteOplus) {
            return hasNoteOplus
        }
        return AppUtil.metaDataEquals(PACKAGE_NAME_NOTE, METADATA_NOTE)
    }

    fun isNoteEnabled(activity: Activity): Boolean {
        if (!EnableAppUtil.isAppEnabled(activity, AppUtil.getNotesPackageName())) {
            appDisableDialog.dismissWhenShowing()
            appDisableDialog = EnableAppUtil.showEnableDialog(
                activity,
                AppUtil.getNotesPackageName(),
                com.soundrecorder.common.R.string.enable_request_title,
                com.soundrecorder.common.R.string.convert_notes_disable_content
            )
            return false
        }
        return true
    }

    private fun dismissNoteEnabledDialog() {
        if (appDisableDialog?.isShowing == true) {
            appDisableDialog?.dismiss()
        }
        appDisableDialog = null
    }

    suspend fun isNoteSupportImg(activity: Activity): Boolean {
        return withContext(Dispatchers.IO) {
            var isSupportImg = false
            try {
                val component = ComponentName(
                    PACKAGE_NAME_NOTE,
                    NOTE_PROVIDER
                )
                val providerInfo =
                    activity.packageManager.getProviderInfo(component, PackageManager.GET_META_DATA)
                val metadata = providerInfo.metaData ?: return@withContext false
                isSupportImg = metadata.getBoolean(NOTE_PROVIDER_META_DATA)
                DebugUtil.i(TAG, "Note support image: $isSupportImg")
            } catch (e: NameNotFoundException) {
                DebugUtil.e(TAG, "get Note metadata error", e)
            }
            isSupportImg
        }
    }

    fun showNoteNotSupportImgDialog(
        activity: AppCompatActivity,
        callBack: (isAgree: Boolean) -> Unit
    ) {
        noteNotSupportImgDialog = COUIAlertDialogBuilder(activity)
            .setTitle(com.soundrecorder.common.R.string.save_note_tip_title)
            .setMessage(com.soundrecorder.common.R.string.save_note_tip_message)
            .setPositiveButton(com.soundrecorder.common.R.string.save_continue) { _, _ ->
                callBack.invoke(true)
            }
            .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
            .create()
        noteNotSupportImgDialog?.show()
    }

    private fun dismissNoteNotSupportImgDialog() {
        if (noteNotSupportImgDialog?.isShowing == true) {
            noteNotSupportImgDialog?.dismiss()
        }
        noteNotSupportImgDialog = null
    }

    fun release() {
        dismissShareDialog()
        dismissShareTextDialog()
        dismissDocOnlySupportTextDialog()
        dismissInstallWpsGuideDialog()
        dismissNoteEnabledDialog()
        dismissNoteNotSupportImgDialog()
    }
}