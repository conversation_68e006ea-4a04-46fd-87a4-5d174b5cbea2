/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: Packet
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/
package com.soundrecorder.common.sync.data;

import java.util.Set;

public interface Packet<T> {
    void putNumber(String key, Number v);

    void putBoolean(String key, Boolean v);

    void putString(String key, String v);

    void putKV(String key, Packet<T> v);

    Number getNumber(String key);

    String getString(String key);

    Boolean getBoolean(String key);

    Packet<T> getKV(String key);

    void putKVAsArray(String key, PacketArray<T> v);

    PacketArray<T> getKVAsArray(String key);

    Set<String> keySet();

    T toT();

    Packet<T> parse(T t);

    boolean isAllValueEmpty();
}
