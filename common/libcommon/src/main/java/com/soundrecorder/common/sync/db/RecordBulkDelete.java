/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: RecordBulkDelete
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, mapengfei, create
 ****************************************************************/
package com.soundrecorder.common.sync.db;

import android.content.ContentResolver;
import android.net.Uri;
import android.os.RemoteException;

import java.util.ArrayList;

class RecordBulkDelete {

    String mColume;
    private static final int CLAUSE_SIZE_MAX = 50;
    private StringBuilder mWhereClause = new StringBuilder();
    private ArrayList<String> mWhereArgs = new ArrayList<String>(50);
    private ContentResolver mProvider;
    private Uri mBaseUri;

    public RecordBulkDelete(ContentResolver provider, Uri baseUri, String colume) {
        mProvider = provider;
        mBaseUri = baseUri;
        mColume = colume;
    }

    public void delete(String data) throws RemoteException {
        if (mWhereClause.length() != 0) {
            mWhereClause.append(",");
        }

        mWhereClause.append("?");
        mWhereArgs.add(data);

        if (mWhereArgs.size() >= CLAUSE_SIZE_MAX) {
            flush();
        }
    }

    public void flush() throws RemoteException {
        int size = mWhereArgs.size();

        if (size > 0) {
            String[] paths = new String[size];
            paths = mWhereArgs.toArray(paths);
            mProvider.delete(mBaseUri, mColume + " IN (" + mWhereClause.toString() + ")", paths);
            mWhereClause.setLength(0);
            mWhereArgs.clear();
        }
    }
}
