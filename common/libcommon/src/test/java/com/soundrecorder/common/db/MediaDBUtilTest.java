/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : MediaDBUtilTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/9/4
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/9/4, LI Kun, create
 ************************************************************/

package com.soundrecorder.common.db;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ContentProviderController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.List;

import com.soundrecorder.base.BaseApplication;

import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.shadows.ShadowBaseUtils;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.db.RecorderProvider;

import com.soundrecorder.common.utils.AudioNameUtils;

import static com.soundrecorder.common.constant.Constants.RECORDINGS;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.robolectric.Shadows.shadowOf;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowBaseUtils.class, ShadowFeatureOption.class})
public class MediaDBUtilTest {
    private static final String EMPTY_STRING = "";
    private static final String TEST = "test";
    private static final String STRING_NULL_IN = "null IN ( ?,?)";
    private static final String STRING_TEST_IN = "test IN ( ?,?,?)";
    private static final String FILE_PATH = "storage" + File.separator + "emulated" + File.separator
            + "0" + File.separator + "Recordings" + File.separator + "test.mp3";
    private static final String FILE_NAME = "test.mp3";
    private Context mContext;
    private ContentProviderController<RecorderProvider> mController;
    private MockedStatic<BaseApplication> mMockBaseAppliation;
    private MockedStatic<TextUtils> mTextUtilsMockedStatic;

    public static final String STANDARD_RECORDINGS = "Standard Recordings";
    public static final String INTERVIEW_RECORDINGS = "Interview Recordings";
    public static final String MEETING_RECORDINGS = "Meeting Recordings";
    public static final String CALL_RECORDINGS = "Call Recordings";

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = Mockito.spy(ApplicationProvider.getApplicationContext());

        mMockBaseAppliation = mockStatic(BaseApplication.class);
        mMockBaseAppliation.when(() -> BaseApplication.getAppContext())
                .thenReturn(mContext);

        when(mContext.getApplicationContext()).thenReturn(mContext);

        mTextUtilsMockedStatic = mockStatic(TextUtils.class);
        mTextUtilsMockedStatic.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
            CharSequence a = invocation.getArgument(0);
            if (a == null || a.length() == 0) {
                return true;
            }
            return false;
        });
    }

    @After
    public void release() {
        if (mMockBaseAppliation != null) {
            mMockBaseAppliation.close();
            mMockBaseAppliation = null;
        }
        mContext = null;
        mTextUtilsMockedStatic.close();
        mTextUtilsMockedStatic = null;
    }

    @Test
    public void should_sendBroadcast_when_addToMediaDBForRecoveryCopy_with_fileNotExistsInDB() {
        Context spyContext = mContext;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        Cursor mockCursor = mock(Cursor.class);
        when(spyContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.getCount()).thenReturn(0);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockContentResolver.insert((Uri) any(), (ContentValues) any())).thenReturn(mock(Uri.class));
        when(mockContentResolver.update((Uri) any(), (ContentValues) any(), anyString(), (String[]) any())).thenReturn(1);
        File mockFile = Mockito.mock(File.class);
        when(mockFile.getName()).thenReturn(FILE_NAME);
        when(mockFile.getAbsolutePath()).thenReturn(FILE_PATH);
        when(mockFile.getPath()).thenReturn(FILE_PATH);
        Uri result = MediaDBUtils.addToMediaDBForRecoveryCopy(spyContext, mockFile, RecordModeConstant.STORAGE_RECORD_ABOVE_Q, true);
        Assert.assertNotNull(result);
    }

    @Test
    public void should_null_when_addToMediaDBForRecoveryCopy_with_fileExistsInDB() {
        Context spyContext = mContext;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        Cursor mockCursor = mock(Cursor.class);
        when(spyContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockContentResolver.insert((Uri) any(), (ContentValues) any())).thenReturn(mock(Uri.class));
        when(mockContentResolver.update((Uri) any(), (ContentValues) any(), anyString(), (String[]) any())).thenReturn(1);
        File mockFile = Mockito.mock(File.class);
        when(mockFile.getName()).thenReturn(FILE_NAME);
        when(mockFile.getAbsolutePath()).thenReturn(FILE_PATH);
        when(mockFile.getPath()).thenReturn(FILE_PATH);
        Uri result = MediaDBUtils.addToMediaDBForRecoveryCopy(spyContext, mockFile, RecordModeConstant.STORAGE_RECORD_ABOVE_Q, true);
        Assert.assertNull(result);
        List<Intent> broadcastIntents = shadowOf(RuntimeEnvironment.application).getBroadcastIntents();
        Assert.assertTrue(broadcastIntents.isEmpty());
    }

    @Test
    public void should_fileName_when_getTitleByName() {
        String filePath = "Recording" + File.separator + "Standard Recording" + File.separator + "test.mp3";
        String filePath2 = "test.mp3";
        String fileName = "test";
        String title = MediaDBUtils.getTitleByName(filePath);
        String title2 = MediaDBUtils.getTitleByName(filePath2);
        Assert.assertEquals(fileName, title);
        Assert.assertEquals(fileName, title2);

        String title3 = MediaDBUtils.getTitleByName(null);
        Assert.assertEquals("", title3);
    }

    @Test
    public void should_false_when_updateRealSizeAndDuration_with_input_null() {
        boolean updateSuc = MediaDBUtils.updateRealSizeAndDuration(null);
        Assert.assertEquals(updateSuc, false);
    }

    @Test
    public void should_0_when_getDurationFromUri_with_input_null() throws FileNotFoundException {
        Context spyContext = mContext;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(spyContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.openInputStream((Uri) any())).thenThrow(new FileNotFoundException());
        when(mockContentResolver.openFileDescriptor((Uri) any(), (String) any())).thenThrow(new FileNotFoundException());
        long result = MediaDBUtils.getDurationFromUri(null);
        Assert.assertEquals(0, result);
    }


    @Test
    public void should_when_getFileSize_with_input_null() throws FileNotFoundException {
        Context spyContext = mContext;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(spyContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.openInputStream((Uri) any())).thenThrow(new FileNotFoundException());
        long result = FileUtils.getFileSize(null);
        Assert.assertEquals(-1, result);
    }

    @Test
    public void should_false_when_updateRealSizeAndDuration_with_input_uri_not_found() throws Exception {
        Context spyContext = mContext;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(spyContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.openInputStream((Uri) any())).thenThrow(new FileNotFoundException());
        when(mockContentResolver.openFileDescriptor((Uri) any(), (String) any())).thenThrow(new FileNotFoundException());
        Cursor mockCursor = mock(Cursor.class);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockContentResolver.insert((Uri) any(), (ContentValues) any())).thenReturn(mock(Uri.class));
        when(mockContentResolver.update((Uri) any(), (ContentValues) any(), anyString(), (String[]) any())).thenReturn(0);
        Uri inputUri = Uri.parse("content://mediaStore");
        boolean updateSuc = MediaDBUtils.updateRealSizeAndDuration(inputUri);
        Assert.assertEquals(updateSuc, false);
    }


    @Test
    public void should_when_getMusicRelativePathByPath() throws Exception {
        Context spyContext = Mockito.spy(mContext);
        String standardRecordPath = "xmvnxcvdv" + File.separator + RECORDINGS + File.separator + STANDARD_RECORDINGS + File.separator + "1.mp3";
        String resultStand = Environment.DIRECTORY_MUSIC + File.separator + RECORDINGS + File.separator + STANDARD_RECORDINGS + File.separator;
        String meetingRecordPath = "xmvnxcvdv" + File.separator + RECORDINGS + File.separator + MEETING_RECORDINGS + File.separator + "2.mp3";
        String resultMeet = Environment.DIRECTORY_MUSIC + File.separator + RECORDINGS + File.separator + MEETING_RECORDINGS + File.separator;
        String interviewRecordPath = "xmvnxcvdv" + File.separator + RECORDINGS + File.separator + INTERVIEW_RECORDINGS + File.separator + "3.mp3";
        String resultInterview = Environment.DIRECTORY_MUSIC + File.separator + RECORDINGS + File.separator + INTERVIEW_RECORDINGS + File.separator;
        String callRecordPath = "xmvnxcvdv" + File.separator + RECORDINGS + File.separator + CALL_RECORDINGS + File.separator + "3.mp3";
        String resultCall = Environment.DIRECTORY_MUSIC + File.separator + RECORDINGS + File.separator + CALL_RECORDINGS + File.separator;
        String illegalInput = "dxlvncxz";
        String result1 = MediaDBUtils.getMusicRelativePathByPath(standardRecordPath);
        Assert.assertEquals(result1, resultStand);
        String result2 = MediaDBUtils.getMusicRelativePathByPath(meetingRecordPath);
        Assert.assertEquals(result2, resultMeet);
        String result3 = MediaDBUtils.getMusicRelativePathByPath(interviewRecordPath);
        Assert.assertEquals(result3, resultInterview);
        String result4 = MediaDBUtils.getMusicRelativePathByPath(callRecordPath);
        Assert.assertEquals(result4, resultCall);
        String result5 = MediaDBUtils.getMusicRelativePathByPath(illegalInput);
        Assert.assertEquals(result5, "");
    }

    @Test
    public void should_false_when_moveRecordFileByCursor() throws Exception {
        Context spyContext = mContext;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mockContentResolver.openInputStream((Uri) any())).thenThrow(new FileNotFoundException());
        when(mockContentResolver.openFileDescriptor((Uri) any(), (String) any())).thenThrow(new FileNotFoundException());
        Cursor mockCursor = mock(Cursor.class);
        when(spyContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.moveToFirst()).thenReturn(true);
        String standardRecordPath = "/storage/emulated/0" + File.separator + RECORDINGS + File.separator + STANDARD_RECORDINGS + File.separator + "1.mp3";
        when(mockCursor.getString(mockCursor.getColumnIndex(MediaStore.Audio.Media.DATA))).thenReturn(standardRecordPath);
        when(mockCursor.getInt(mockCursor.getColumnIndex(MediaStore.Audio.Media._ID))).thenReturn(1);
        when(mockContentResolver.update((Uri) any(), (ContentValues) any(), anyString(), (String[]) any())).thenReturn(0);
        int updateSuc = MediaDBUtils.moveRecordFileByCursor(mockCursor);
        Assert.assertEquals(updateSuc > 0, false);
    }

    @Test
    public void should_when_getTitleName() throws Exception {
        Context spyContext = mContext;
        String inputPath1 = File.separator + RECORDINGS + File.separator + STANDARD_RECORDINGS + File.separator + "stand.mp3";
        String name1 = MediaDBUtils.getTitleByName(inputPath1);
        Assert.assertEquals(name1, "stand");
        String inputPath2 = File.separator + RECORDINGS + File.separator + STANDARD_RECORDINGS + File.separator + "stand";
        String name2 = MediaDBUtils.getTitleByName(inputPath2);
        Assert.assertEquals(name2, "stand");
    }

    @Test
    public void should_return_null_when_getMediaUriForAbsolutePath_not_record_found() throws Exception {
        Context spyContext = mContext;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mockContentResolver.openInputStream((Uri) any())).thenThrow(new FileNotFoundException());
        when(mockContentResolver.openFileDescriptor((Uri) any(), (String) any())).thenThrow(new FileNotFoundException());
        Cursor mockCursor = mock(Cursor.class);
        when(spyContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.getCount()).thenReturn(0);
        when(mockCursor.moveToFirst()).thenReturn(true);
        String standardRecordPath = "/storage/emulated/0" + File.separator + RECORDINGS + File.separator + STANDARD_RECORDINGS + File.separator + "1.mp3";
        Uri uriRst = MediaDBUtils.getMediaUriForAbsolutePath(standardRecordPath);
        Assert.assertNull(uriRst);
    }

    @Test
    public void should_some_record_when_getNeedMoveRecordFileCount() throws Exception {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mockContentResolver.openInputStream((Uri) any())).thenThrow(new FileNotFoundException());
        when(mockContentResolver.openFileDescriptor((Uri) any(), (String) any())).thenThrow(new FileNotFoundException());
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.moveToFirst()).thenReturn(true);
        int count = MediaDBUtils.getNeedMoveRecordFileCount();
        Assert.assertTrue(count > 0);
    }

    @Test
    public void should_not_null_when_getNeedMoveRecordFileWhere() {
        String where = MediaDBUtils.getNeedMoveRecordFileWhere();
        Assert.assertNotNull(where);
    }

    @Test
    public void should_id_when_queryRowIdByRelativePathAndDisplayName() throws Exception {
        String relativePath = "music/Standard Recordings";
        String displayName = "x.mp3";
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mockContentResolver.openInputStream((Uri) any())).thenThrow(new FileNotFoundException());
        when(mockContentResolver.openFileDescriptor((Uri) any(), (String) any())).thenThrow(new FileNotFoundException());
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockCursor.getLong(mockCursor.getColumnIndex(MediaStore.Audio.Media._ID))).thenReturn(111L);
        long id = MediaDBUtils.queryRowIdByRelativePathAndDisplayName(relativePath, displayName);
        Assert.assertTrue(id > 0);
    }

    @Test
    public void should_uri_when_queryRecordById() {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.moveToFirst()).thenReturn(true);
        Record record = MediaDBUtils.queryRecordById(111);
        Assert.assertNotNull(record);
    }

    @Test
    public void should_not_null_when_queryRecordByUri_with_input_uri() {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.moveToFirst()).thenReturn(true);
        Uri inputUri = Uri.parse("content://mediaStore/test");
        Record record = MediaDBUtils.queryRecordByUri(inputUri);
        Assert.assertNotNull(record);
    }

    @Test
    public void should_true_when_updateRealSizeAndDurationAndName_with_not_uri() throws Exception {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.openInputStream((Uri) any())).thenThrow(new FileNotFoundException());
        when(mockContentResolver.openFileDescriptor((Uri) any(), (String) any())).thenThrow(new FileNotFoundException());
        when(mockContentResolver.update((Uri) any(), (ContentValues) any(), anyString(), (String[]) any())).thenReturn(0);
        Uri inputUri = Uri.parse("content://mediaStore");
        boolean updateSuc = MediaDBUtils.updateRealSizeAndDurationAndName(inputUri, FILE_PATH, true);
        Assert.assertFalse(updateSuc);
    }

    @Test
    public void should_media_id_when_queryIdByData_with_data() {
        long testId = 99L;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        Cursor mockCursor = mock(Cursor.class);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockCursor.getLong(mockCursor.getColumnIndex(MediaStore.Audio.Media._ID))).thenReturn(testId);
        long mediaId = MediaDBUtils.queryIdByData(FILE_PATH);
        Assert.assertTrue(mediaId > -1);
    }

    @Test
    public void should_uri_when_getMediaUriForRelativePathAndDisplayName_with_input_relative_path_and_name() {
        long testId = 99L;
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.getLong(mockCursor.getColumnIndex(MediaStore.Audio.Media._ID))).thenReturn(testId);
        Uri mediaUri = MediaDBUtils.getMediaUriForRelativePathAndDisplayName("test_path", FILE_NAME);
        Assert.assertEquals(mediaUri, MediaDBUtils.genUri(testId));
    }

    @Test
    public void should_returnFalse_when_updateRealSizeAndDuration() {
        File file = Mockito.mock(File.class);
        doReturn(true).when(file).exists();
        doReturn("/test").when(file).getAbsolutePath();
        boolean mediaUri = MediaDBUtils.updateRealSizeAndDuration(mContext, file);
        Assert.assertFalse(mediaUri);
    }

    @Test
    public void should_renameResult_when_rename() {
        Record record = Mockito.mock(Record.class);
        doReturn("test").when(record).getDisplayName();
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.update((Uri) any(), (ContentValues) any(), (String) any(), (String[]) any())).thenReturn(2);
        when(mockCursor.moveToFirst()).thenReturn(true);

        int renameResult = MediaDBUtils.rename(Uri.parse("/test"), record);
        Assert.assertNotEquals(renameResult, 1);
    }

    @Test
    public void should_returnNotnull_when_getCloudContentValues() {
        Record record = Mockito.mock(Record.class);
        doReturn("test").when(record).getDisplayName();
        Assert.assertNotNull(MediaDBUtils.getCloudContentValues(record, "test"));
    }

    @Test
    public void should_returnFalse_when_hasOwnerPackageName() {
        boolean isMp3 = MediaDBUtils.hasOwnerPackageName(FILE_PATH);
        Assert.assertFalse(isMp3);
    }

    @Test
    public void should_mDuration_when_getDurationFromMediaId() {
        long mDuration = MediaDBUtils.getDurationFromMediaId(11L);
        Assert.assertEquals(mDuration, 0);
    }

    @Test
    public void should_fileFormat_when_getFileFormatFromUri() {
        String fileFormat = MediaDBUtils.getFileFormatFromUri(Uri.parse("test"));
        Assert.assertEquals(fileFormat, "");
    }

    @Test
    public void should_returnNull_when_getMediaUriForAbsolutePath() {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockCursor.getLong(mockCursor.getColumnIndex(MediaStore.Audio.Media._ID))).thenReturn(1L);
        Uri uri = MediaDBUtils.getMediaUriForAbsolutePath("test");
        Assert.assertNotNull(uri);
    }

    @Test
    public void should_returnNull_when_getMediaUriForRecord() {
        Record record = Mockito.mock(Record.class);
        doReturn("test").when(record).getDisplayName();
        doReturn("test").when(record).getRelativePath();
        doReturn("test").when(record).getData();

        ContentResolver mockContentResolver = mock(ContentResolver.class);
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.moveToFirst()).thenReturn(true);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.getLong(mockCursor.getColumnIndex(MediaStore.Audio.Media._ID))).thenReturn(1L);

        Uri uri = MediaDBUtils.getMediaUriForRecord(record);
        Assert.assertNotNull(uri);
    }

    @Test
    public void should_numeric_when_isNumeric() {
        boolean numeric = AudioNameUtils.isNumeric(FILE_NAME);
        Assert.assertFalse(numeric);

        numeric = AudioNameUtils.isNumeric("1");
        Assert.assertTrue(numeric);

        numeric = AudioNameUtils.isNumeric("1哈哈");
        Assert.assertFalse(numeric);

        numeric = AudioNameUtils.isNumeric("1哈哈1");
        Assert.assertFalse(numeric);

        numeric = AudioNameUtils.isNumeric("1哈哈_");
        Assert.assertFalse(numeric);

        numeric = AudioNameUtils.isNumeric("2哈哈_2");
        Assert.assertFalse(numeric);

        numeric = AudioNameUtils.isNumeric("1哈哈 hh1_2");
        Assert.assertFalse(numeric);

        numeric = AudioNameUtils.isNumeric("1_2");
        Assert.assertFalse(numeric);

        numeric = AudioNameUtils.isNumeric("1234");
        Assert.assertTrue(numeric);

        numeric = AudioNameUtils.isNumeric("1234 ");
        Assert.assertFalse(numeric);
    }

    @Test
    public void should_str_when_genDefaultFileTitle() {
        String str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, FILE_NAME);
        Assert.assertEquals(str, "test_1");

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_2.mp3");
        Assert.assertEquals("test_1", str);

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_1哈哈.mp3");
        Assert.assertEquals(str, "test_1哈哈_1");

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_1哈哈1.mp3");
        Assert.assertEquals(str, "test_1哈哈1_1");

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_1哈哈_.mp3");
        Assert.assertEquals(str, "test_1哈哈__1");

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_2哈哈_2.mp3");
        Assert.assertEquals(str, "test_2哈哈_1");

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_1哈哈 hh1.mp3");
        Assert.assertEquals(str, "test_1哈哈 hh1_1");

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_1哈哈 hh1_2.mp3");
        Assert.assertEquals(str, "test_1哈哈 hh1_1");

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_1_2.mp3");
        Assert.assertEquals(str, "test_1_1");

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_1234.mp3");
        Assert.assertEquals("test_1", str);

        str = AudioNameUtils.genDefaultFileTitle(FILE_PATH, "test_1234 .mp3");
        Assert.assertEquals(str, "test_1234 _1");
    }

    @Test
    public void should_str_when_genSaveFileName() {
        String str = AudioNameUtils.genSaveFileName(RecordModeConstant.RECORD_TYPE_STANDARD, "test", true);
        Assert.assertEquals(str, "Standard recording 1test");
        String str2 = AudioNameUtils.genSaveFileName(RecordModeConstant.RECORD_TYPE_INTERVIEW, "test", true);
        Assert.assertEquals(str2, "Interview recording 1test");
        String str3 = AudioNameUtils.genSaveFileName(RecordModeConstant.RECORD_TYPE_CONFERENCE, "test", true);
        Assert.assertEquals(str3, "Meeting recording 1test");
    }

    @Test
    public void should_mDuration_when_queryRowIdByRelativePathAndDisplayName() {
        long l = MediaDBUtils.queryRowIdByRelativePathAndDisplayName(FILE_PATH, FILE_NAME);
        Assert.assertEquals(l, 0);
    }

    @Test
    public void should_record_when_queryRecordByRelativePathAndDisplayName() {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        Cursor mockCursor = mock(Cursor.class);
        when(mContext.getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query((Uri) any(), (String[]) any(), (String) any(), (String[]) any(), (String) any())).thenReturn(mockCursor);
        when(mockCursor.moveToFirst()).thenReturn(true);
        Record record = MediaDBUtils.queryRecordByRelativePathAndDisplayName(FILE_PATH, FILE_NAME);
        Assert.assertNotNull(record);
    }

    @Test
    public void should_scanMediaAndRenameFile_when_throwException() throws Exception {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(BaseApplication.getAppContext().getContentResolver()).thenReturn(mockContentResolver);
        //doThrow(new IllegalStateException()).when(mockContentResolver).update((Uri) any(), (ContentValues) any(), anyString(), (String[]) any());
        when(mockContentResolver.update(any(), any(), any(), any())).thenThrow(new IllegalArgumentException());
        //when(mockContentResolver.update((Uri) any(), (ContentValues) any(), null, null)).thenThrow(new IllegalStateException());
        MockedStatic<MediaDBUtils> mockStatic = Mockito.mockStatic(MediaDBUtils.class);
        Record mockRecord = new Record();
        mockRecord.mData = "";
        when(MediaDBUtils.rename(any(), any(), any(), any())).thenCallRealMethod();
        when(MediaDBUtils.renameInternal(any(), any())).thenCallRealMethod();
        when(MediaDBUtils.getRecordFromMediaByUriId(any())).thenReturn(mockRecord);
        int result = MediaDBUtils.rename(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, "stardard recording 1", ".mp3", "audio/mpeg");
        Assert.assertEquals(result, -1);
        mockStatic.close();
    }



    @Test
    public void should_return_1_when_rename_throwException() throws Exception {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(BaseApplication.getAppContext().getContentResolver()).thenReturn(mockContentResolver);
        //doThrow(new IllegalStateException()).when(mockContentResolver).update((Uri) any(), (ContentValues) any(), anyString(), (String[]) any());
        when(mockContentResolver.update(any(), any(), any(), any())).thenThrow(new IllegalArgumentException());
        //when(mockContentResolver.update((Uri) any(), (ContentValues) any(), null, null)).thenThrow(new IllegalStateException());
        MockedStatic<MediaDBUtils> mockStatic = Mockito.mockStatic(MediaDBUtils.class);
        Record mockRecord = new Record();
        mockRecord.mData = "";
        when(MediaDBUtils.rename(any(), any(Record.class))).thenCallRealMethod();
        when(MediaDBUtils.renameInternal(any(), any())).thenCallRealMethod();
        when(MediaDBUtils.getRecordFromMediaByUriId(any())).thenReturn(mockRecord);
        Record inputRecord = new Record();
        inputRecord.setDisplayName("1.mp3");
        int result = MediaDBUtils.rename(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, inputRecord);
        Assert.assertEquals(result, -1);
        mockStatic.close();
    }


    @Test
    public void should_return_1_when_renameUri_throwException() throws Exception {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        when(BaseApplication.getAppContext().getContentResolver()).thenReturn(mockContentResolver);
        //doThrow(new IllegalStateException()).when(mockContentResolver).update((Uri) any(), (ContentValues) any(), anyString(), (String[]) any());
        when(mockContentResolver.update(any(), any(), any(), any())).thenThrow(new IllegalArgumentException());
        //when(mockContentResolver.update((Uri) any(), (ContentValues) any(), null, null)).thenThrow(new IllegalStateException());
        MockedStatic<MediaDBUtils> mockStatic = Mockito.mockStatic(MediaDBUtils.class);
        Record mockRecord = new Record();
        mockRecord.mData = "";
        when(MediaDBUtils.rename(any(), any(String.class))).thenCallRealMethod();
        when(MediaDBUtils.renameInternal(any(), any())).thenCallRealMethod();
        when(MediaDBUtils.getRecordFromMediaByUriId(any())).thenReturn(mockRecord);
        int result = MediaDBUtils.rename(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, "String");
        Assert.assertEquals(result, -1);
        mockStatic.close();
    }

    @Test
    public void should_returnFormatString_when_getWhereForInKeyword_with_sizeAndColumn() {
        StringBuilder result = MediaDBUtils.getWhereForInKeyword(0, null);
        StringBuilder result1 = MediaDBUtils.getWhereForInKeyword(2, null);
        StringBuilder result2 = MediaDBUtils.getWhereForInKeyword(3, TEST);
        assertEquals(EMPTY_STRING, result.toString());
        assertEquals(STRING_NULL_IN, result1.toString());
        assertEquals(STRING_TEST_IN, result2.toString());
    }

    @Test
    public void should_correct_when_queryAudioFileExist() {
        ContentResolver mockContentResolver = mock(ContentResolver.class);
        Cursor mockCursor = mock(Cursor.class);
        when(BaseApplication.getAppContext().getContentResolver()).thenReturn(mockContentResolver);
        when(mockContentResolver.query(any(), any(), any(), any(), any())).thenReturn(mockCursor);
        when(mockCursor.getCount()).thenReturn(1);
        Assert.assertTrue(MediaDBUtils.queryAudioFileExist(1));

        when(mockContentResolver.query(any(), any(), any(), any(), any())).thenThrow(new IllegalArgumentException());
        Assert.assertFalse(MediaDBUtils.queryAudioFileExist(1));
    }
}
