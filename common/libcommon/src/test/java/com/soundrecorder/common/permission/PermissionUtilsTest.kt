package com.soundrecorder.common.permission

import android.Manifest.permission.READ_MEDIA_AUDIO
import android.Manifest.permission.READ_MEDIA_IMAGES
import android.app.Activity
import android.content.ContentResolver
import android.content.Context
import android.content.pm.PackageManager.PERMISSION_DENIED
import android.content.pm.PackageManager.PERMISSION_GRANTED
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Environment
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.common.permission.PermissionUtils.*
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

/*
File:perTest
Description:
Version:
Date:2022/6/29
Author:W9013204

------------------Revision History------------------
<author> <date> <version> <desc>
*/
@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowOS12FeatureUtil::class])
class PermissionUtilsTest {
    private var context: Context? = null
    private var activityController: ActivityController<PermissionProxyActivity>? = null
    private val baseUtilMock = Mockito.mockStatic(BaseUtil::class.java)
    private val baseApplicationMock = Mockito.mockStatic(BaseApplication::class.java)
    private val contextCompatMock = Mockito.mockStatic(ContextCompat::class.java)
    private val environmentMock = Mockito.mockStatic(Environment::class.java)

    private val storageMock = Mockito.mockStatic(StorageManager::class.java)
    private val activityCompatMock = Mockito.mockStatic(ActivityCompat::class.java)

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        activityController = Robolectric.buildActivity(PermissionProxyActivity::class.java)
    }

    @After
    fun tearDown() {
        context = null
        activityController = null
        baseUtilMock.reset()
        baseUtilMock.close()
        baseApplicationMock.reset()
        baseApplicationMock.close()
        contextCompatMock.reset()
        contextCompatMock.close()
        environmentMock.reset()
        environmentMock.close()
        storageMock.reset()
        storageMock.close()
        activityCompatMock.reset()
        activityCompatMock.close()
    }

    @Test
    fun verify_value_when_READ_AUDIO_PERMISSION() {
        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(true)
        Assert.assertTrue(BaseUtil.isAndroidTOrLater)

        Assert.assertEquals(READ_MEDIA_AUDIO, READ_AUDIO_PERMISSION())

        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(false)
        Assert.assertEquals(READ_EXTERNAL_STORAGE, READ_AUDIO_PERMISSION())

        baseUtilMock.reset()
    }

    @Test
    fun verify_value_when_READ_IMAGE_PERMISSION() {
        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(true)
        Assert.assertEquals(READ_MEDIA_IMAGES, READ_IMAGE_PERMISSION())

        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(false)
        Assert.assertEquals(READ_EXTERNAL_STORAGE, READ_IMAGE_PERMISSION())
        baseUtilMock.reset()
    }

    @Test
    fun verify_value_when_hasRecordAudioPermission() {
        val context = Mockito.spy(context!!)
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        contextCompatMock.`when`<Int> {
            ContextCompat.checkSelfPermission(
                Mockito.any(Context::class.java),
                Mockito.any(String::class.java)
            )
        }
            .thenReturn(
                PERMISSION_GRANTED
            )
        Assert.assertTrue(hasRecordAudioPermission())
        contextCompatMock.`when`<Int> {
            ContextCompat.checkSelfPermission(
                Mockito.any(Context::class.java),
                Mockito.any(String::class.java)
            )
        }
            .thenReturn(
                PERMISSION_DENIED
            )
        Assert.assertFalse(hasRecordAudioPermission())
        contextCompatMock.reset()
        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_hasReadAudioPermission() {
        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }.thenReturn(true)
        Assert.assertTrue(hasReadAudioPermission())

        val context = Mockito.mock(Context::class.java)
        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }.thenReturn(false)
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)

        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_GRANTED)
        Assert.assertTrue(hasReadAudioPermission())

        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_DENIED)
        Assert.assertFalse(hasReadAudioPermission())

        environmentMock.reset()
        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_hasReadImagesPermission() {
        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }.thenReturn(true)
        Assert.assertTrue(hasReadImagesPermission())

        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }.thenReturn(false)

        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)

        contextCompatMock.`when`<Int> {
            ContextCompat.checkSelfPermission(
                Mockito.any(Context::class.java),
                Mockito.any(String::class.java)
            )
        }
            .thenReturn(
                PERMISSION_GRANTED
            )
        Assert.assertTrue(hasReadImagesPermission())

        contextCompatMock.`when`<Int> {
            ContextCompat.checkSelfPermission(
                Mockito.any(Context::class.java),
                Mockito.any(String::class.java)
            )
        }
            .thenReturn(
                PERMISSION_DENIED
            )
        Assert.assertFalse(hasReadImagesPermission())

        environmentMock.reset()
        contextCompatMock.reset()
        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_shouldFirstRequestReadImagesPermission() {
        val activity = Mockito.mock(Activity::class.java)
        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(false)
        Assert.assertFalse(shouldFirstRequestReadImagesPermission(activity))

        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(true)
        contextCompatMock.`when`<Int> {
            ContextCompat.checkSelfPermission(
                Mockito.any(),
                Mockito.any()
            )
        }.thenReturn(
            PERMISSION_GRANTED
        )
        Assert.assertTrue(hasReadImagesPermission())
        Assert.assertFalse(shouldFirstRequestReadImagesPermission(activity))

        contextCompatMock.`when`<Int> {
            ContextCompat.checkSelfPermission(
                Mockito.any(),
                Mockito.any()
            )
        }.thenReturn(
            PERMISSION_DENIED
        )
        Assert.assertFalse(hasReadImagesPermission())
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(
            PERMISSION_NOT_APPLY
        )
        Assert.assertTrue(shouldFirstRequestReadImagesPermission(activity))

        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(
            PERMISSION_APPLIED
        )
        Assert.assertFalse(shouldFirstRequestReadImagesPermission(activity))
        storageMock.reset()
        baseUtilMock.reset()
        environmentMock.reset()
        activityCompatMock.reset()
        contextCompatMock.reset()
    }

    @Test
    fun verify_value_when_hasAllPermissions() {
        val context = Mockito.mock(Context::class.java)
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)

        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_DENIED)

        val permissions = arrayOf(READ_MEDIA_IMAGES)
        val permissions2 = arrayOf(READ_MEDIA_AUDIO)
        //模拟continue
        Assert.assertTrue(hasAllPermissions(permissions, true))

        Assert.assertFalse(hasAllPermissions(permissions2, true))
        Assert.assertFalse(hasAllPermissions(permissions, false))
        Assert.assertFalse(hasAllPermissions(permissions2, false))

        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_GRANTED)
        Assert.assertTrue(hasAllPermissions(permissions2, true))
        Assert.assertTrue(hasAllPermissions(permissions, false))
        Assert.assertTrue(hasAllPermissions(permissions2, false))

        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_hasPermissionsRationale() {
        val permissions = arrayOf(READ_MEDIA_IMAGES)
        val activity = Mockito.mock(Activity::class.java)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(Mockito.any(), Mockito.anyString(), Mockito.anyInt())
        }.thenReturn(PERMISSION_NOT_APPLY)
        activityCompatMock.`when`<Boolean> {
            ActivityCompat.shouldShowRequestPermissionRationale(Mockito.any(), Mockito.anyString())
        }.thenReturn(true)
        //false true -> true
        Assert.assertTrue(hasPermissionsRationale(activity, permissions))
        //false false ->false
        activityCompatMock.`when`<Boolean> {
            ActivityCompat.shouldShowRequestPermissionRationale(Mockito.any(), Mockito.anyString())
        }.thenReturn(false)
        Assert.assertFalse(hasPermissionsRationale(activity, permissions))
        //true false ->false
        storageMock.`when`<Int> {
            StorageManager.getIntPref(Mockito.any(), Mockito.anyString(), Mockito.anyInt())
        }.thenReturn(PERMISSION_APPLIED)
        Assert.assertFalse(hasPermissionsRationale(activity, permissions))
        activityCompatMock.`when`<Boolean> {
            ActivityCompat.shouldShowRequestPermissionRationale(Mockito.any(), Mockito.anyString())
        }.thenReturn(false)
        Assert.assertFalse(hasPermissionsRationale(activity, permissions))
        //true true ->false
        storageMock.`when`<Int> {
            StorageManager.getIntPref(Mockito.any(), Mockito.anyString(), Mockito.anyInt())
        }.thenReturn(PERMISSION_APPLIED)
        Assert.assertFalse(hasPermissionsRationale(activity, permissions))
        activityCompatMock.`when`<Boolean> {
            ActivityCompat.shouldShowRequestPermissionRationale(Mockito.any(), Mockito.anyString())
        }.thenReturn(true)
        Assert.assertTrue(hasPermissionsRationale(activity, permissions))
        storageMock.reset()
        activityCompatMock.reset()
    }

    @Test
    fun verify_value_when_hasReadAudioPermissionRationale() {
        val activity = Mockito.mock(Activity::class.java)
        Mockito.`when`(activity.shouldShowRequestPermissionRationale(Mockito.anyString()))
            .thenReturn(true)
        Assert.assertTrue(hasReadAudioPermissionRationale(activity))
        Mockito.`when`(activity.shouldShowRequestPermissionRationale(Mockito.anyString()))
            .thenReturn(false)
        Assert.assertFalse(hasReadAudioPermissionRationale(activity))
    }

    @Test
    fun verify_value_when_cannotRequestPermissions() {
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        val activity = Mockito.mock(Activity::class.java)
        val permissions = arrayOf(READ_MEDIA_IMAGES)

        //true true > false
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_APPLIED)

        activityCompatMock.`when`<Boolean> {
            ActivityCompat.shouldShowRequestPermissionRationale(
                Mockito.any(Activity::class.java),
                Mockito.anyString()
            )
        }.thenReturn(true)
        Assert.assertFalse(cannotRequestPermissions(activity, permissions))
        //true false > true
        activityCompatMock.`when`<Boolean> {
            ActivityCompat.shouldShowRequestPermissionRationale(
                Mockito.any(Activity::class.java),
                Mockito.anyString()
            )
        }.thenReturn(false)
        Assert.assertTrue(cannotRequestPermissions(activity, permissions))
        //false false > false
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }
            .thenReturn(PERMISSION_NOT_APPLY)

        Assert.assertFalse(cannotRequestPermissions(activity, permissions))
        //false true > false
        activityCompatMock.`when`<Boolean> {
            ActivityCompat.shouldShowRequestPermissionRationale(
                Mockito.any(Activity::class.java),
                Mockito.anyString()
            )
        }.thenReturn(true)
        Assert.assertFalse(cannotRequestPermissions(activity, permissions))

        storageMock.reset()
        activityCompatMock.reset()
        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_requestPermissions() {
        val activity = Mockito.mock(Activity::class.java)
        val permissions = arrayOf(READ_MEDIA_IMAGES)
        requestPermissions(activity, permissions, REQUEST_CODE_RECORDER)
    }

    @Test
    fun verify_value_when_requestReadAudioPermission() {
        val activity = Mockito.mock(Activity::class.java)
        requestReadAudioPermissionForBrowseFile(activity)
    }

    @Test
    fun verify_value_when_hasRequestNotificationPermission() {
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }
            .thenReturn(PERMISSION_NOT_APPLY)
        Assert.assertFalse(hasRequestNotificationPermission(context))

        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }
            .thenReturn(PERMISSION_APPLIED)
        Assert.assertTrue(hasRequestNotificationPermission(context))

        storageMock.reset()
    }

    @Test
    fun verify_value_when_isNeedShowNotificationPermissionSnackBar() {
        val context = Mockito.spy(context!!)
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)

        //true false false > true
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }
            .thenReturn(PERMISSION_APPLIED)
        Mockito.`when`(context!!.checkSelfPermission(POST_NOTIFICATIONS))
            .thenReturn(PERMISSION_DENIED)
        storageMock.`when`<Boolean> {
            StorageManager.getBooleanPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyBoolean()
            )
        }
            .thenReturn(false)
        //期望结果为true
        Assert.assertTrue(isNeedShowNotificationPermissionSnackBar(context))

        //true false true > false
        storageMock.`when`<Boolean> {
            StorageManager.getBooleanPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyBoolean()
            )
        }
            .thenReturn(true)
        //期望结果为false
        Assert.assertFalse(isNeedShowNotificationPermissionSnackBar(context))

        //true true true > false
        Mockito.`when`(context.checkSelfPermission(POST_NOTIFICATIONS))
            .thenReturn(PERMISSION_GRANTED)
        //期望结果为false
        Assert.assertFalse(isNeedShowNotificationPermissionSnackBar(context))

        //true true false > false
        storageMock.`when`<Boolean> {
            StorageManager.getBooleanPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyBoolean()
            )
        }
            .thenReturn(false)
        //期望结果为false
        Assert.assertFalse(isNeedShowNotificationPermissionSnackBar(context))

        //false true false > false
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }
            .thenReturn(PERMISSION_NOT_APPLY)
        //期望结果为false
        Assert.assertFalse(isNeedShowNotificationPermissionSnackBar(context))

        //false true true > false
        storageMock.`when`<Boolean> {
            StorageManager.getBooleanPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyBoolean()
            )
        }
            .thenReturn(true)
        //期望结果为false
        Assert.assertFalse(isNeedShowNotificationPermissionSnackBar(context))

        //false false true > false
        Mockito.`when`(context.checkSelfPermission(POST_NOTIFICATIONS))
            .thenReturn(PERMISSION_DENIED)
        //期望结果为false
        Assert.assertFalse(isNeedShowNotificationPermissionSnackBar(context))

        //false false false > false
        storageMock.`when`<Boolean> {
            StorageManager.getBooleanPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyBoolean()
            )
        }
            .thenReturn(false)
        //期望结果为false
        Assert.assertFalse(isNeedShowNotificationPermissionSnackBar(context))

        storageMock.reset()
        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_setHasShowNotificationPermissionSnackBar() {
        setHasShowNotificationPermissionSnackBar(context)
    }

    @Test
    fun verify_value_when_checkNotificationPermission() {
        val activity = Mockito.mock(Activity::class.java)
        val context = Mockito.spy(context!!)
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)

        checkNotificationPermission(null)

        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_GRANTED)
        checkNotificationPermission(activity)

        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_DENIED)
        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(false)
        checkNotificationPermission(activity)

        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidTOrLater }.thenReturn(true)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_APPLIED)
        checkNotificationPermission(activity)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_NOT_APPLY)
        checkNotificationPermission(activity)

        storageMock.reset()
        baseUtilMock.reset()
        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_hasPermissions() {
        val context = Mockito.spy(context!!)
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        Assert.assertTrue(hasPermissions(arrayOf()))
        hasPermissions(arrayOf(POST_NOTIFICATIONS))

        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_GRANTED)
        hasPermissions(arrayOf(POST_NOTIFICATIONS))

        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_goToAppSettingConfigurePermissions() {
        val activity = Mockito.mock(Activity::class.java)
        val uriMock = Mockito.mockStatic(Uri::class.java)
        val uri = Uri.parse("uripackage:com.coloros.soundrecorder")
        uriMock.`when`<Uri> {
            Uri.fromParts(
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyString()
            )
        }.thenReturn(uri)

        goToAppSettingConfigurePermissions(activity)

        Mockito.`when`(activity.startActivity(Mockito.any())).thenThrow(NullPointerException(""))
        goToAppSettingConfigurePermissions(activity)

        uriMock.reset()
        uriMock.close()
    }

    @Test
    fun verify_value_when_goToAppAllFileAccessConfigurePermissions() {
        val activity = Mockito.mock(Activity::class.java)
        val uriMock = Mockito.mockStatic(Uri::class.java)


        val uri = Uri.parse("uripackage:com.coloros.soundrecorder")
        uriMock.`when`<Uri> {
            Uri.fromParts(
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyString()
            )
        }.thenReturn(uri)
        Mockito.`when`(activity.packageName).thenReturn("com.coloros.soundrecorder")
        goToAppAllFileAccessConfigurePermissions(activity)

        Mockito.`when`(activity.startActivity(Mockito.any())).thenThrow(NullPointerException(""))
        goToAppAllFileAccessConfigurePermissions(activity)

        uriMock.reset()
        uriMock.close()
    }

    @Test
    fun verify_value_when_getStatementOrAllFileGrantedStatus() {
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        Assert.assertFalse(FeatureOption.OPLUS_VERSION_EXP)
        getNextAction()

        Whitebox.setInternalState(FeatureOption::class.java, "OPLUS_VERSION_EXP", true)
        Assert.assertTrue(FeatureOption.OPLUS_VERSION_EXP)
        getNextAction()

        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_setAllFileAccessStatus() {
        setNextActionForRequestPermission(context!!)
    }

    @Test
    fun verify_value_when_setStatementStatus() {
        setNextActionForShowAllFileDialog(context!!)
    }

    @Test
    fun verify_value_when_isStatementConvertGranted() {
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_APPLIED)
        isStatementConvertGranted(context!!)

        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_DENIED)
        isStatementConvertGranted(context!!)
        storageMock.reset()
    }

    @Test
    fun verify_value_when_isNetWorkGranted() {
        storageMock.`when`<Boolean> {
            StorageManager.containsKey(
                Mockito.any(),
                Mockito.anyString()
            )
        }.thenReturn(true)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_APPLIED)
        Assert.assertTrue(isNetWorkGranted(context!!))

        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_NOT_APPLY)
        Assert.assertFalse(isNetWorkGranted(context!!))

        storageMock.reset()
        storageMock.`when`<Boolean> {
            StorageManager.containsKey(
                Mockito.any(),
                Mockito.anyString()
            )
        }.thenReturn(false)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_APPLIED)
        Assert.assertTrue(isNetWorkGranted(context!!))
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_NOT_APPLY)
        Assert.assertFalse(isNetWorkGranted(context!!))

        storageMock.reset()
    }

    @Test
    fun verify_value_when_setConvertGrantedStatus() {
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_NOT_APPLY)
        setConvertGrantedStatus(context!!)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_APPLIED)
        setConvertGrantedStatus(context!!)
        storageMock.reset()
        baseApplicationMock.reset()
    }


    @Test
    fun verify_value_when_clearConvertGrantedStatus() {
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_NOT_APPLY)
        clearConvertGrantedStatus()
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_APPLIED)
        clearConvertGrantedStatus()
        storageMock.reset()
        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_setNetWorkGrantedStatus() {
        setNetWorkGrantedStatus(context!!, true)
        setNetWorkGrantedStatus(context!!, false)
    }

    @Test
    fun verify_value_when_hasAllFilePermission() {
        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }.thenReturn(true)
        Assert.assertTrue(hasAllFilePermission())
        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }.thenReturn(false)
        Assert.assertFalse(hasAllFilePermission())

        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }
            .thenThrow(java.lang.NullPointerException(""))
        Assert.assertFalse(hasAllFilePermission())
        environmentMock.reset()
    }

    @Test
    fun verify_value_when_hasOwnerPackageName() {
        val context = Mockito.spy(context!!)
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)

        baseApplicationMock.`when`<ContentResolver> { context.contentResolver }
            .thenThrow(java.lang.NullPointerException(""))
        Assert.assertFalse(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )


        Mockito.`when`(context.packageName).thenReturn("soundrecorder")
        val contentResolver = Mockito.mock(ContentResolver::class.java)
        val cursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(
            contentResolver.query(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
            )
        ).thenReturn(cursor)
        Mockito.`when`(cursor.moveToFirst()).thenReturn(true)
        baseApplicationMock.`when`<ContentResolver> { context.contentResolver }
            .thenReturn(contentResolver)
        Mockito.`when`(cursor.moveToFirst()).thenReturn(true)
        Mockito.`when`(cursor.getString(Mockito.anyInt())).thenReturn("soundrecorder")

        Assert.assertTrue(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )

        Mockito.`when`(cursor.getString(Mockito.anyInt())).thenReturn("soundrecorder222")
        Assert.assertFalse(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )

        Mockito.`when`(
            contentResolver.query(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
            )
        ).thenReturn(null)
        Assert.assertFalse(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )

        Mockito.`when`(
            contentResolver.query(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
            )
        ).thenReturn(cursor)
        Mockito.`when`(cursor.moveToFirst()).thenReturn(false)
        Assert.assertFalse(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )

        baseApplicationMock.`when`<ContentResolver> { context.contentResolver }
            .thenThrow(java.lang.NullPointerException(""))
        Assert.assertFalse(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )

        Mockito.`when`(context.packageName).thenThrow(NullPointerException(""))
        Assert.assertFalse(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )


        baseApplicationMock.reset()
    }

    @Test
    fun verify_value_when_checkUriPermission() {
        val context = Mockito.spy(context!!)
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        Mockito.`when`(context.packageName).thenReturn("soundrecorder")
        val contentResolver = Mockito.mock(ContentResolver::class.java)
        val cursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(
            contentResolver.query(
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any(),
                Mockito.any()
            )
        ).thenReturn(cursor)
        Mockito.`when`(cursor.moveToFirst()).thenReturn(true)
        baseApplicationMock.`when`<ContentResolver> { BaseApplication.getAppContext().contentResolver }
            .thenReturn(contentResolver)
        Mockito.`when`(cursor.moveToFirst()).thenReturn(true)
        Mockito.`when`(cursor.getString(Mockito.anyInt())).thenReturn("soundrecorder")

        Assert.assertTrue(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )
        Assert.assertTrue(checkUriPermission(1233L))

        Mockito.`when`(cursor.getString(Mockito.anyInt())).thenReturn("soundrecorder222")
        Assert.assertFalse(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )

        Mockito.`when`(
            context.checkUriPermission(
                Mockito.any(Uri::class.java),
                Mockito.anyInt(),
                Mockito.anyInt(),
                Mockito.anyInt()
            )
        )
            .thenReturn(PERMISSION_GRANTED)
        Assert.assertTrue(checkUriPermission(1233L))

        Mockito.`when`(
            context.checkUriPermission(
                Mockito.any(Uri::class.java),
                Mockito.anyInt(),
                Mockito.anyInt(),
                Mockito.anyInt()
            )
        )
            .thenReturn(PERMISSION_DENIED)
        Assert.assertFalse(checkUriPermission(1233L))

        Mockito.`when`(
            context.checkUriPermission(
                Mockito.any(Uri::class.java),
                Mockito.anyInt(),
                Mockito.anyInt(),
                Mockito.anyInt()
            )
        )
            .thenThrow(java.lang.NullPointerException(""))
        Assert.assertFalse(checkUriPermission(1233L))


        val list = listOf<Long>()
        Assert.assertTrue(checkUriPermissions(list))
        val list2 = listOf(1L)
        Assert.assertFalse(checkUriPermissions(list2))

        Mockito.`when`(cursor.getString(Mockito.anyInt())).thenReturn("soundrecorder")
        Assert.assertTrue(
            Whitebox.invokeMethod(
                PermissionUtils::class.java,
                "hasOwnerPackageName",
                1233L
            )
        )
        Assert.assertTrue(checkUriPermission(1233L))
        Assert.assertTrue(checkUriPermissions(list2))

        baseApplicationMock.reset()
    }

    @Test
    fun check_hasFirstCheckAllPermissionsOnResumeForBrowseFile() {
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(Context::class.java),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_NOT_APPLY, PERMISSION_APPLIED)
        Assert.assertTrue(hasFirstCheckAllPermissionsOnResumeForBrowseFile())
        Assert.assertFalse(hasFirstCheckAllPermissionsOnResumeForBrowseFile())
        baseApplicationMock.reset()
        storageMock.reset()
    }

    @Test
    fun check_hasRequestPermissions() {
        baseApplicationMock.`when`<Context> { BaseApplication.getAppContext() }.thenReturn(context)
        storageMock.`when`<Int> {
            StorageManager.getIntPref(
                Mockito.any(),
                Mockito.anyString(),
                Mockito.anyInt()
            )
        }.thenReturn(PERMISSION_NOT_APPLY)
        putRequestPermissions(arrayOf(READ_MEDIA_AUDIO))
        Assert.assertFalse(hasRequestPermissions(READ_MEDIA_AUDIO))
        baseApplicationMock.reset()
        storageMock.reset()
    }

    @Test
    fun check_checkPermissionUpdateAlreadyApply() {
        val ctx = context ?: return
        setStatementUpdateStatus(ctx)
        Assert.assertFalse(checkPermissionUpdateAlreadyApply(ctx))
    }

    @Test
    fun should_correct_when_hasOnlyReadVisualUserSelectedPermission() {
        Assert.assertFalse(hasOnlyReadVisualUserSelectedPermission())
    }

    @Test
    fun should_correct_when_hasReadVisualUserSelectedPermission() {
        contextCompatMock.`when`<Int> {
            ContextCompat.checkSelfPermission(
                Mockito.any(Context::class.java), Mockito.any(String::class.java))
        }.thenReturn(PERMISSION_GRANTED)
//        Assert.assertFalse(hasReadVisualUserSelectedPermission())
        Assert.assertTrue(hasReadVisualUserSelectedPermission())
    }

    @Test
    fun check_hasOnlyReadVisualUserSelectedPermission() {
        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        Assert.assertFalse(hasOnlyReadVisualUserSelectedPermission())
        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }.thenReturn(false)
        Assert.assertFalse(hasAllFilePermission())

        baseUtilMock.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(true)
        environmentMock.`when`<Boolean> { Environment.isExternalStorageManager() }.thenReturn(true)
        val context = Mockito.mock(Context::class.java)

        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_DENIED)
        Mockito.`when`(context.checkSelfPermission(Mockito.anyString()))
            .thenReturn(PERMISSION_GRANTED)
        Assert.assertFalse(hasOnlyReadVisualUserSelectedPermission())
        environmentMock.reset()
        baseUtilMock.reset()
    }
}