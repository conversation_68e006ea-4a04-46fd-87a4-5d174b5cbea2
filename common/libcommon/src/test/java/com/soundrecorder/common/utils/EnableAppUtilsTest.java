package com.soundrecorder.common.utils;

import static com.soundrecorder.common.utils.EnableAppUtil.APP_IS_DISABLED;
import static com.soundrecorder.common.utils.EnableAppUtil.APP_NOT_INSTALLED;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.AppUtil;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.shadows.ShadowOplusUsbEnvironment;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class EnableAppUtilsTest {
    private Context mContext;
    private MockedStatic<BaseApplication> mMockBaseApplication = null;

    private static final String PACKNAME = "packname";

    @Before
    public void setUp() {
        mContext = Mockito.mock(Context.class);
        mMockBaseApplication = Mockito.mockStatic(BaseApplication.class);
        mMockBaseApplication.when(() -> BaseApplication.getAppContext()).thenReturn(mContext);
    }

    @Test
    public void should_returnTrue_when_appEnabled() throws PackageManager.NameNotFoundException {
        boolean isAppEnadle = EnableAppUtil.isAppEnabled(mContext, PACKNAME);
        Assert.assertTrue(isAppEnadle);

        isAppEnadle = EnableAppUtil.isAppEnabled(null, PACKNAME);
        Assert.assertTrue(isAppEnadle);

        ApplicationInfo info = new ApplicationInfo();
        info.enabled = true;
        PackageManager packageManager = Mockito.mock(PackageManager.class);
        Mockito.when(packageManager.getApplicationInfo(anyString(), anyInt())).thenReturn(null, info);
        Mockito.when(mContext.getPackageManager()).thenReturn(packageManager);

        isAppEnadle = EnableAppUtil.isAppEnabled(mContext, PACKNAME);
        Assert.assertTrue(isAppEnadle);

    }

    @Test
    public void should_returnFalse_when_appEnabled() throws PackageManager.NameNotFoundException {
        Mockito.when(mContext.getPackageManager()).thenReturn(null);
        boolean isAppEnadle = EnableAppUtil.isAppEnabled(mContext, PACKNAME);
        Assert.assertTrue(isAppEnadle);

        ApplicationInfo info = new ApplicationInfo();
        info.enabled = false;
        PackageManager packageManager = Mockito.mock(PackageManager.class);
        Mockito.when(packageManager.getApplicationInfo(anyString(), anyInt())).thenReturn(null, info);
        Mockito.when(mContext.getPackageManager()).thenReturn(packageManager);

        // applicationInfo is null
        isAppEnadle = EnableAppUtil.isAppEnabled(mContext, PACKNAME);
        Assert.assertTrue(isAppEnadle);

        // applicationInfo not null
        isAppEnadle = EnableAppUtil.isAppEnabled(mContext, PACKNAME);
        Assert.assertFalse(isAppEnadle);

        // throw exception
        Mockito.when(packageManager.getApplicationInfo(anyString(), anyInt())).thenThrow(new PackageManager.NameNotFoundException());
        isAppEnadle = EnableAppUtil.isAppEnabled(mContext, PACKNAME);
        Assert.assertTrue(isAppEnadle);
    }


    @Test
    public void should_returnFalse_when_isAppInstallEnabled() throws PackageManager.NameNotFoundException {
        Mockito.when(mContext.getPackageManager()).thenReturn(null);
        Assert.assertEquals(APP_NOT_INSTALLED, EnableAppUtil.isAppInstallEnabled(mContext, PACKNAME));

        ApplicationInfo info = new ApplicationInfo();
        info.enabled = false;
        PackageManager packageManager = Mockito.mock(PackageManager.class);
        Mockito.when(packageManager.getApplicationInfo(anyString(), anyInt())).thenReturn(null, info);
        Mockito.when(mContext.getPackageManager()).thenReturn(packageManager);

        // applicationInfo is null
        Assert.assertEquals(APP_NOT_INSTALLED, EnableAppUtil.isAppInstallEnabled(mContext, PACKNAME));

        // applicationInfo not null
        Assert.assertEquals(APP_IS_DISABLED, EnableAppUtil.isAppInstallEnabled(mContext, PACKNAME));

        // throw exception
        Mockito.when(packageManager.getApplicationInfo(anyString(), anyInt())).thenThrow(new PackageManager.NameNotFoundException());
        Assert.assertEquals(APP_NOT_INSTALLED, EnableAppUtil.isAppInstallEnabled(mContext, PACKNAME));
    }

    @Test
    public void should_correct_when_getAppName() throws Exception {
        String functionName = "getAppName";
        ApplicationInfo info = new ApplicationInfo();
        info.enabled = false;
        PackageManager packageManager = Mockito.mock(PackageManager.class);
        Mockito.when(packageManager.getApplicationInfo(anyString(), anyInt())).thenReturn(info);
        Mockito.when(packageManager.getApplicationLabel(any())).thenReturn("package");
        Mockito.when(mContext.getPackageManager()).thenReturn(packageManager);
        Assert.assertEquals("package", Whitebox.invokeMethod(AppUtil.INSTANCE, functionName, ""));

        // throw exception
        Mockito.when(packageManager.getApplicationLabel(any())).thenThrow(new NullPointerException());
        Assert.assertEquals("", Whitebox.invokeMethod(AppUtil.INSTANCE, functionName, ""));
    }

    @After
    public void tearDown() {
        if (mMockBaseApplication != null) {
            mMockBaseApplication.reset();
            mMockBaseApplication.close();
            mMockBaseApplication = null;
        }
        mContext = null;
    }
}

