package com.soundrecorder.common.widget

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

/*
File:AnimateColorTextViewTest
Description:
Version:
Date:2022/6/27
Author:W9013204

------------------Revision History------------------
<author> <date> <version> <desc>
*/
@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class AnimateColorTextViewTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        context = null
    }

    @Test
    fun verify_data_when_setAnimateSelected() {
        val context = context ?: return
        val animateColorTextView = AnimateColorTextView(context)
        Assert.assertFalse(animateColorTextView.getAnimateSelected())
        animateColorTextView.setAnimateSelected(false)
        animateColorTextView.setAnimateSelected(true)
        Assert.assertTrue(animateColorTextView.getAnimateSelected())
        animateColorTextView.setAnimateSelected(false)
    }

    @Test
    fun verify_cancel_animation_when_onDetachedFromWindow() {
        val context = context ?: return
        val animateColorTextView = AnimateColorTextView(context)
        animateColorTextView.setAnimateSelected(true)
        Whitebox.invokeMethod<Void>(animateColorTextView, "onDetachedFromWindow")
    }
}