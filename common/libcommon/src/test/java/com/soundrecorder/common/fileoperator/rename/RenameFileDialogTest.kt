package com.soundrecorder.common.fileoperator.rename

import android.app.Activity
import android.os.Build
import android.view.View
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.R
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.Assert.*

import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class RenameFileDialogTest {

    private var mActivity: Activity? = null
    private var mRenameFileDialog: RenameFileDialog? = null
    private val content = "test"

    @Before
    fun setUp() {
        mActivity = Robolectric.buildActivity(Activity::class.java).get()
        mRenameFileDialog = RenameFileDialog(mActivity!!, 0, content, null)
    }

    @After
    fun tearDown() {
        mActivity = null
        mRenameFileDialog = null
    }

    @Test
    fun getMediaRecord() {
        val record = Record()
        mRenameFileDialog?.mediaRecord = record
        assertEquals(record, mRenameFileDialog?.mediaRecord)
    }

    @Test
    fun setMediaRecord() {
        val record = Record()
        mRenameFileDialog?.mediaRecord = record
        assertNotNull(mRenameFileDialog?.mediaRecord)
    }

    @Test
    fun onInitCustomView() {
        val dialog = Mockito.mock(RenameFileDialog::class.java)
        val view = View(mActivity)
        dialog?.onInitCustomView(view)
        Mockito.verify(dialog, Mockito.times(1)).onInitCustomView(view)
    }

    @Test
    fun getTitleText() {
        val titleText = mRenameFileDialog?.getTitleText()
        assertEquals(R.string.oplus_rename_dialog_title, titleText)
    }

    @Test
    fun onSave() {
        val dialog = Mockito.mock(RenameFileDialog::class.java)
        dialog?.onSave()
        Mockito.verify(dialog, Mockito.times(1)).onSave()
    }

    @Test
    fun onCancel() {
        val dialog = Mockito.mock(RenameFileDialog::class.java)
        dialog?.onCancel()
        Mockito.verify(dialog, Mockito.times(1)).onCancel()
    }

    @Test
    fun getOriginalContent() {
        val originalContent = mRenameFileDialog?.getOriginalContent()
        assertEquals(content, originalContent)
    }
}