package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyMap
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CloudStaticsUtilTest {
    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mContext: Context? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication!!.`when`<Context> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mockedStatic?.close()
        mockedStatic = null
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        mContext = null
    }

    @Test
    fun check_addCloudTipsCardPopEvent() {
        CloudStaticsUtil.addCloudTipsCardPopEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCloudTipsClickOpenEvent() {
        CloudStaticsUtil.addCloudTipsClickOpenEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCloudTipsClickIgnoreEvent() {
        CloudStaticsUtil.addCloudTipsClickIgnoreEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCloudTipsClickViewDataEvent() {
        CloudStaticsUtil.addCloudTipsClickViewDataEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCloudTipsUpgradeSpacePopEvent() {
        CloudStaticsUtil.addCloudTipsUpgradeSpacePopEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCloudTipsClickUpgradeSpaceEvent() {
        CloudStaticsUtil.addCloudTipsClickUpgradeSpaceEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addCloudLog() {
        CloudStaticsUtil.addCloudLog("", "")
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }
}