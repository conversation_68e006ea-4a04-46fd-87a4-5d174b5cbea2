/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: TaskBarUtilTest
 * Description:
 * Version: 1.0
 * Date: 2023/6/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/12 1.0 create
 */

package com.soundrecorder.common.utils.taskbar

import android.app.Activity
import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.StatusBarUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class TaskBarUtilTest {

    private var mContext: Context? = null
    private var mActivity: Activity? = null
    private var mMockStaticBaseUtil: MockedStatic<BaseUtil>? = null
    private var mMockStaticFeatureOption: MockedStatic<FeatureOption>? = null

    @Before
    fun setUp() {
        mMockStaticBaseUtil = Mockito.mockStatic(BaseUtil::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(Activity::class.java).get()
        mMockStaticFeatureOption = Mockito.mockStatic(FeatureOption::class.java)

        mMockStaticBaseUtil?.`when`<Boolean> { BaseUtil.isAndroidTOrLater }?.thenReturn(true)
    }

    @After
    fun release() {
        mMockStaticBaseUtil?.close()
        mMockStaticFeatureOption?.close()
        mMockStaticBaseUtil = null
        mMockStaticFeatureOption = null
        mActivity = null
        mContext = null
    }

    @Test
    fun should_setNavigationColorOnSupportTaskBar() {
        val mMockStaticStatusBarUtil = Mockito.mockStatic(StatusBarUtil::class.java)

        TaskBarUtil.setNavigationColorOnSupportTaskBar(0, null)
        mMockStaticStatusBarUtil?.verify({ StatusBarUtil.setNavigationBarColor(any(Activity::class.java), anyInt()) }, Mockito.times(0))

        mMockStaticFeatureOption?.`when`<Boolean> { FeatureOption.isTaskBarEnable(mContext) }?.thenReturn(false, true)

        TaskBarUtil.setNavigationColorOnSupportTaskBar(0, mActivity)
        mMockStaticStatusBarUtil?.verify({ StatusBarUtil.setNavigationBarColor(any(Activity::class.java), anyInt()) }, Mockito.times(0))

        TaskBarUtil.setNavigationColorOnSupportTaskBar(0, mActivity)
        mMockStaticStatusBarUtil?.verify({ StatusBarUtil.setNavigationBarColor(any(Activity::class.java), anyInt()) }, Mockito.times(1))

        mMockStaticStatusBarUtil.close()
    }

    @Test
    fun should_isTaskBarShowing() {
        mMockStaticFeatureOption?.`when`<Boolean> { FeatureOption.isTaskBarEnable(mContext) }?.thenReturn(false, true, true)

        Assert.assertFalse(TaskBarUtil.isTaskBarShowing(0, mActivity!!))

        val mockStaticScreenUtil = Mockito.mockStatic(ScreenUtil::class.java)
        Assert.assertFalse(TaskBarUtil.isTaskBarShowing(0, mActivity!!))
        mockStaticScreenUtil.`when`<Boolean> { ScreenUtil.isSmallScreen(mActivity) }.thenReturn(false)
        Assert.assertTrue(TaskBarUtil.isTaskBarShowing(10000, mActivity!!))

        mockStaticScreenUtil.close()
    }
}