/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FoldingWindowObserverTest
 * Description:
 * Version: 1.0
 * Date: 2022/8/11
 * Author: W9010241(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9010241 2022/8/8 1.0 create
 */


package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class HoverStaticsUtilTest {

    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mContext: Context? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication?.`when`<Context> { BaseApplication.getAppContext() }
            ?.thenReturn(mContext)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mockedStatic?.close()
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        mContext = null
    }

    @Test
    fun check_addHoverNumEvent() {
        HoverStaticsUtil.addHoverNumEvent("BrowseFile")
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                ArgumentMatchers.any(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(),
                ArgumentMatchers.anyMap<String, String>(),
                ArgumentMatchers.anyBoolean()
            )
        }, Mockito.times(1))
    }
}