package com.soundrecorder.common.buryingpoint

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.*
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class ConvertStaticsUtilTest {
    private var mockedStatic: MockedStatic<RecorderUserAction>? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mContext: Context? = null
    private val TEST_STRING = "test_string"
    private val TEST_LONG = 1L

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mockedStatic = Mockito.mockStatic(RecorderUserAction::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication!!.`when`<Context> { BaseApplication.getAppContext() }?.thenReturn(mContext)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mockedStatic?.close()
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        mContext = null
    }

    @Test
    fun check_addConvertStartEvent() {
        ConvertStaticsUtil.addConvertStartEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertStopEvent() {
        ConvertStaticsUtil.addConvertStopEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertCancelEvent() {
        ConvertStaticsUtil.addConvertCancelEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addExportCopyEvent() {
        ConvertStaticsUtil.addExportCopyEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addExportTxtEvent() {
        ConvertStaticsUtil.addExportTxtEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addExportCancelEvent() {
        ConvertStaticsUtil.addExportCancelEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addSpeakerClickSpeakerTipEvent() {
        ConvertStaticsUtil.addSpeakerClickSpeakerTipEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addSpeakerRenameByHistoryEvent() {
        ConvertStaticsUtil.addSpeakerRenameByHistoryEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addSpeakerRenameAllEvent() {
        ConvertStaticsUtil.addSpeakerRenameAllEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addSpeakerRenameCurrentEvent() {
        ConvertStaticsUtil.addSpeakerRenameCurrentEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addClickSpeakerSwitchEventOnConvert() {
        ConvertStaticsUtil.addClickSpeakerSwitchEventOnConvert(true)
        ConvertStaticsUtil.addClickSpeakerSwitchEventOnConvert(false)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(2))
    }

    @Test
    fun check_addExportDocEvent() {
        ConvertStaticsUtil.addExportDocEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addShowInstallWpsDialogEvent() {
        ConvertStaticsUtil.addShowInstallWpsDialogEvent()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addClickSegmentedSwitchEventOnShare() {
        ConvertStaticsUtil.addClickSegmentedSwitchEventOnShare(TEST_STRING)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addClickDateSwitchEventOnShare() {
        ConvertStaticsUtil.addClickDateSwitchEventOnShare(TEST_STRING)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addClickSpeakerSwitchEventOnShare() {
        ConvertStaticsUtil.addClickSpeakerSwitchEventOnShare(TEST_STRING)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyString(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addInConvertSearchEvent() {
        ConvertStaticsUtil.addInConvertSearchEvent(1)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, Int>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_clickKeyWordChipEvent() {
        ConvertStaticsUtil.clickKeyWordChipEvent(1)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, Int>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertFailedMessage() {
        ConvertStaticsUtil.addConvertFailedMessage(0, 0, "")
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertText() {
        ConvertStaticsUtil.addConvertText()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addLookText() {
        ConvertStaticsUtil.addLookText()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                any(),
                anyInt(),
                anyString(),
                anyMap<String, String>(),
                anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertCancelDialog() {
        ConvertStaticsUtil.addConvertCancelDialog()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertContinueDialog() {
        ConvertStaticsUtil.addConvertContinueDialog()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addShareText() {
        ConvertStaticsUtil.addShareText()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addClickExport() {
        ConvertStaticsUtil.addClickExport(TEST_STRING)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addExportDoc() {
        ConvertStaticsUtil.addExportDoc()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addSendToNote() {
        ConvertStaticsUtil.addSendToNote()
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertFileDuration() {
        ConvertStaticsUtil.addConvertFileDuration(TEST_LONG)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertSuccessDuration() {
        ConvertStaticsUtil.addConvertSuccessDuration(TEST_LONG)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertCancelDuration() {
        ConvertStaticsUtil.addConvertCancelDuration(TEST_LONG)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }

    @Test
    fun check_addConvertFailDuration() {
        ConvertStaticsUtil.addConvertFailDuration(TEST_LONG)
        mockedStatic?.verify({
            RecorderUserAction.addNewCommonUserAction(
                    any(),
                    anyInt(),
                    anyString(),
                    anyMap<String, String>(),
                    anyBoolean()
            )
        }, Mockito.times(1))
    }
}