apply from:"../../common_build.gradle"

android {
    namespace "com.oplus.recorderlog"
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat

    //SystemPropertiesNative 依赖
    compileOnly libs.oplus.support.adapter
    //OplusUsbEnvironment, OplusFeatureConfigManager 依赖，主包中存在，这里compile即可
    compileOnly libs.oplus.addon
    testImplementation libs.oplus.addon
    /*Q上需依赖老版本addon，否则找不到addon相关类，导致crash*/
    implementation libs.oplus.addon.adapter

    //AppFeature 依赖，主包中存在，这里compile即可
    compileOnly libs.oplus.appfeature
    //Gson移植到base  gson 依赖， 主包中存在，这里compile即可
    compileOnly libs.gson
    //opendId sdk 移植到base 依赖
    compileOnly libs.oplus.stdid.sdk
    implementation project(':common:modulerouter')
    // Koin for Android
    implementation(libs.koin)
}