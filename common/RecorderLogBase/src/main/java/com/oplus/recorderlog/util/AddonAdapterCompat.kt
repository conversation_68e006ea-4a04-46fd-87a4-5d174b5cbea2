/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: UsbEnvironmentCompat
 * Description:
 * Version: 1.0
 * Date: 2023/10/7
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/10/7 1.0 create
 */

package com.oplus.recorderlog.util

import android.content.Context
import android.os.UserHandle
import java.io.File

object AddonAdapterCompat {
    private val addonUtil by lazy {
        if (BaseUtil.isAndroidROrLater) {
            AddonAdapterCompatR
        } else {
            AddonAdapterCompatQ
        }
    }

    @JvmStatic
    fun isVolumeMounted(context: Context?, path: String): Boolean {
        return addonUtil.isVolumeMounted(context, path)
    }

    @JvmStatic
    fun getInternalSdDirectory(context: Context?): File? {
        return addonUtil.getInternalSdDirectory(context)
    }

    @JvmStatic
    fun getInternalSdState(context: Context?): String? {
        return addonUtil.getInternalSdState(context)
    }

    @JvmStatic
    fun getInternalPath(context: Context?): String? {
        return addonUtil.getInternalPath(context)
    }

    @JvmStatic
    fun getExternalSdDirectory(context: Context?): File? {
        return addonUtil.getExternalSdDirectory(context)
    }

    @JvmStatic
    fun getExternalSdState(context: Context?): String? {
        return addonUtil.getExternalSdState(context)
    }

    @JvmStatic
    fun getExternalPath(context: Context?): String? {
        return addonUtil.getExternalPath(context)
    }

    @JvmStatic
    fun getExternalStorageDirectory(): File? {
        return addonUtil.getExternalStorageDirectory()
    }

    @JvmStatic
    fun isExternalSdMounted(context: Context?): Boolean {
        return addonUtil.isExternalSdMounted(context)
    }

    @JvmStatic
    fun isInternalSdMounted(context: Context?): Boolean {
        return addonUtil.isInternalSdMounted(context)
    }

    @JvmStatic
    fun isMultiSystemUserHandle(userHandler: UserHandle): Boolean {
        return addonUtil.isMultiSystemUserHandle(userHandler)
    }

    @JvmStatic
    fun getOplusOSVERSION(): Int {
        return addonUtil.getOplusOSVERSION()
    }

    @JvmStatic
    fun getVirtualcommDeviceType(context: Context): Int {
        return addonUtil.getVirtualcommDeviceType(context)
    }

    @JvmStatic
    fun addBackgroundRestrictedInfo(packageName: String, pkgList: List<String>) {
        return addonUtil.addBackgroundRestrictedInfo(packageName, pkgList)
    }

    interface IUsbEnvironment {
        fun isVolumeMounted(context: Context?, path: String): Boolean

        fun getInternalSdDirectory(context: Context?): File?

        fun getInternalSdState(context: Context?): String?

        fun getInternalPath(context: Context?): String?

        fun getExternalSdDirectory(context: Context?): File?

        fun getExternalSdState(context: Context?): String?

        fun getExternalPath(context: Context?): String?

        fun getExternalStorageDirectory(): File?

        fun isInternalSdMounted(context: Context?): Boolean

        fun isExternalSdMounted(context: Context?): Boolean

        fun isMultiSystemUserHandle(userHandler: UserHandle): Boolean
        fun getOplusOSVERSION(): Int
        fun getVirtualcommDeviceType(context: Context): Int

        fun addBackgroundRestrictedInfo(packageName: String, pkgList: List<String>)
    }
}