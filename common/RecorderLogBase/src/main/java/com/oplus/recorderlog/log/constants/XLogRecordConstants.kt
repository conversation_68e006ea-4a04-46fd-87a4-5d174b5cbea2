package com.oplus.recorderlog.log.constants

import android.os.Environment
import java.io.File

class XLogRecordConstants {
    companion object {

        const val RECORDINGS = "Recordings"
        const val STANDARD_RECORDINGS = "Standard Recordings"
        const val INTERVIEW_RECORDINGS = "Interview Recordings"
        const val MEETING_RECORDINGS = "Meeting Recordings"
        const val CALL_RECORDINGS = "Call Recordings"


        const val RECORD_TYPE_ALL = 0
        const val RECORD_TYPE_STANDARD = 1
        const val RECORD_TYPE_INTERVIEW = 2
        const val RECORD_TYPE_MEETING = 3
        const val RECORD_TYPE_CALL = 4

        const val SEPARATOR: String = "/"
        val RELATIVE_PATH_BASE: String = Environment.DIRECTORY_MUSIC + File.separator +  RECORDINGS + File.separator
        val RELATIVE_PATH_RECORDINGS_NO_SLASH: String = Environment.DIRECTORY_MUSIC + File.separator +  RECORDINGS
        val RELATIVE_PATH_STANDARD_NO_SLASH: String = "${RELATIVE_PATH_BASE}Standard Recordings"
        val RELATIVE_PATH_MEETING_NO_SLASH: String = "${RELATIVE_PATH_BASE}Meeting Recordings"
        val RELATIVE_PATH_INTERVIEW_NO_SLASH: String = "${RELATIVE_PATH_BASE}Interview Recordings"
        val RELATIVE_PATH_CALL_NO_SLASH: String = "${RELATIVE_PATH_BASE}Call Recordings"

        val RELATIVE_PATH_STANDARD: String = "$RELATIVE_PATH_STANDARD_NO_SLASH$SEPARATOR"
        val RELATIVE_PATH_MEETING: String = "$RELATIVE_PATH_MEETING_NO_SLASH$SEPARATOR"
        val RELATIVE_PATH_INTERVIEW: String = "$RELATIVE_PATH_INTERVIEW_NO_SLASH$SEPARATOR"
        val RELATIVE_PATH_CALL: String = "$RELATIVE_PATH_CALL_NO_SLASH$SEPARATOR"

        // ONE_PLUS Record folders
        const val OP_RECORD = "Record"
        const val OP_RELATIVE_PATH_RECORD_ABOVE_Q = "Music/Record"
        const val OP_STORAGE_RECORD_ABOVE_AND_R = "Music/Record/SoundRecord"
        const val OP_STORAGE_RECORD_BELOW_Q = "Record/SoundRecord"

        const val STORAGE_RECORD = "Recordings/"
        const val STORAGE_RECORD_ABOVE_Q = "Music/Recordings/"
        const val DIR_STANDARD = "Standard Recordings"
        val DIR_STANDARD_END = File.separator + "Standard Recordings" + File.separator
        const val DIR_MEETING = "Meeting Recordings"
        val DIR_MEETING_END = File.separator + "Meeting Recordings" + File.separator
        const val DIR_INTERVIEW = "Interview Recordings"
        val DIR_INTERVIEW_END = File.separator + "Interview Recordings" + File.separator
        const val DIR_CALL = "Call Recordings"
        val DIR_CALL_END = File.separator + "Call Recordings" + File.separator


        const val MIMETYPE_MP3 = "audio/mpeg"
        const val MIMETYPE_RAW = "audio/raw"
        const val MIMETYPE_WAV = "audio/x-wav"
        const val MIMETYPE_AMR = "audio/amr"
        const val MIMETYPE_AMR_WB = "audio/amr-wb"
        const val MIMETYPE_3GPP = "audio/3gpp"
        const val MIMETYPE_ACC = "audio/aac"
        const val MIMETYPE_ACC_ADTS = "audio/aac-adts"


        const val RECORD_NORMAL = 0
        const val RECORD_DELETED = 1
    }
}




