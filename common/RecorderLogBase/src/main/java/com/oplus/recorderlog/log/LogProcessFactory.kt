package com.oplus.recorderlog.log

import com.oplus.recorderlog.log.local.LocalLog
import com.oplus.recorderlog.XLogApiWrapper

object LogProcessFactory {

    const val LOG_TYPE_LOCAL = 1
    const val LOG_TYPE_X = 2


    fun createLogProcess(type: Int): ILogProcess? {
        return when (type) {
            LOG_TYPE_LOCAL -> LocalLog()
            LOG_TYPE_X -> XLogApiWrapper()
            else -> null
        }
    }
}