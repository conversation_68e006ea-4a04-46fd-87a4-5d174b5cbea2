package com.oplus.recorderlog.log.dbprint

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import com.oplus.recorderlog.log.IDbPrinter
import com.oplus.recorderlog.log.constants.XLogRecordConstants
import com.oplus.recorderlog.log.ILog
import com.oplus.recorderlog.log.bean.Record
import com.oplus.recorderlog.log.constants.XLogDbConstant
import com.oplus.recorderlog.log.constants.XLogDbConstant.RecorderColumn
import com.oplus.recorderlog.log.util.MediaCursorHelper
import java.lang.Exception
import java.lang.StringBuilder

class DbPrinterImpl(var iLog: ILog) : IDbPrinter {

    companion object {
        const val TAG = "DbPrinterImpl"

        val BASE_MEDIA_URI: Uri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
        val BASE_RECORD_URI: Uri = XLogDbConstant.RecordUri.RECORD_CONTENT_URI

        const val MEDIA_BATCH_COUNT = 10
        const val RECORD_BATCH_COUNT = 10

        private val MEDIA_DB_PROJECTION = arrayOf(
            MediaStore.Audio.Media._ID,
            MediaStore.Audio.Media.DATA,
            MediaStore.Audio.Media.SIZE,
            MediaStore.Audio.Media.DISPLAY_NAME,
            MediaStore.Audio.Media.MIME_TYPE,
            MediaStore.Audio.Media.DATE_ADDED,
            MediaStore.Audio.Media.DATE_MODIFIED,
            MediaStore.Audio.Media.DURATION,
        )

        private val RECDOORD_PROJECTION = arrayOf(
            RecorderColumn.COLUMN_NAME_ID,
            RecorderColumn.COLUMN_NAME_UUID,
            RecorderColumn.COLUMN_NAME_DATA,
            RecorderColumn.COLUMN_NAME_SIZE,
            RecorderColumn.COLUMN_NAME_DISPLAY_NAME,
            RecorderColumn.COLUMN_NAME_MIMETYPE,
            RecorderColumn.COLUMN_NAME_DATE_CREATED,
            RecorderColumn.COLUMN_NAME_DATE_MODIFIED,
            RecorderColumn.COLUMN_NAME_RECORD_TYPE,
            RecorderColumn.COLUMN_NAME_MARK_DATA,
            RecorderColumn.COLUMN_NAME_AMP_DATA,
            RecorderColumn.COLUMN_NAME_DURATION,
            RecorderColumn.COLUMN_NAME_BUCKET_ID,
            RecorderColumn.COLUMN_NAME_BUCKET_DISPLAY_NAME,
            RecorderColumn.COLUMN_NAME_DIRTY,
            RecorderColumn.COLUMN_NAME_DELETE,
            RecorderColumn.COLUMN_NAME_MD5,
            RecorderColumn.COLUMN_NAME_FILE_ID,
            RecorderColumn.COLUMN_NAME_GLOBAL_ID,
            RecorderColumn.COLUMN_NAME_SYNC_TYPE,
            RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS,
            RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS,
            RecorderColumn.COLUMN_NAME_ERROR_CODE,
            RecorderColumn.COLUMN_NAME_LEVEL,
            RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS,
            RecorderColumn.COLUMN_NAME_SYNC_DATE,
            RecorderColumn.COLUMN_NAME_FAIL_COUNT,
            RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME,
            RecorderColumn.COLUMN_NAME_RELATIVE_PATH,
            RecorderColumn.COLUMN_NAME_AMP_FILE_PATH,
            RecorderColumn.COLUMN_NAME_PRIVATE_STATUS,
            RecorderColumn.COLUMN_NAME_MIGRATE_STATUS,
            RecorderColumn.COLUMN_NAME_IS_MARKLIST_SHOWING,
            RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION,
            RecorderColumn.COLUMN_NAME_CLOUD_CHECK_PAYLOAD
        )
    }

    @Suppress("TooGenericExceptionCaught")
    override fun printMediaDb(context: Context?) {
        if (context == null) {
            return
        }
        iLog.i(TAG, "\r\n PRINT RECORD MEDIA START")
        var sb = StringBuilder()
        val queryUri: Uri = BASE_MEDIA_URI
        val whereClause = MediaCursorHelper.getAllRecordForFilterAndQueryWhereClause(context, XLogRecordConstants.RECORD_TYPE_ALL)
        val whereArgs = MediaCursorHelper.sAcceptableAudioTypes
        val orderDate = MediaStore.Audio.Media.DATE_MODIFIED
        val orderTitle = MediaStore.Audio.Media.TITLE
        val sortOrder = "$orderDate DESC,$orderTitle DESC"
        var cursor: Cursor? = null
        var count = 0
        try {
            cursor = context.contentResolver.query(queryUri, null, whereClause, whereArgs, sortOrder, null)
            if (cursor != null && cursor.count > 0) {
                iLog.i(TAG, getVolumeStringForMediaDb())
                iLog.i(TAG, "RECORD MEDIA DB SIZE : ${cursor.count}")
                while (cursor.moveToNext()) {
                    sb = getStringForMediaRecord(sb, cursor)
                    count += 1
                    if (count == MEDIA_BATCH_COUNT) {
                        count = 0
                        iLog.i(TAG, sb.toString())
                        sb.clear()
                    }
                }
                iLog.i(TAG, sb.toString())
            }
        } catch (e: Exception) {
            iLog.e(TAG, "printMediaDb query cursor exception", e)
        } finally {
            cursor?.close()
            sb.clear()
            iLog.i(TAG, "\r\n PRINT RECORD MEDIA END")
        }
    }

    private fun getVolumeStringForMediaDb(): String {
        val columnNameList = mutableListOf<String>()
        columnNameList.addAll(MEDIA_DB_PROJECTION)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            columnNameList.add(MediaStore.Audio.Media.RELATIVE_PATH)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            columnNameList.add(MediaStore.Audio.Media.OWNER_PACKAGE_NAME)
        }
        val sb = StringBuilder("MediaDB Column Names")
        sb.append(columnNameList.joinToString() + "\r\n")
        return sb.toString()
    }

    private fun getStringForMediaRecord(sb: StringBuilder, cursor: Cursor): StringBuilder {
        val record = Record(cursor, Record.TYPE_FROM_MEDIA)
        val recordString = record.toMediaLogString()
        sb.append(recordString + "\r\n")
        return sb
    }

    @Suppress("TooGenericExceptionCaught")
    override fun printRecordDb(context: Context?) {
        if (context == null) {
            return
        }
        iLog.i(TAG, "PRINT RECORD DB START \r\n")
        var sb = StringBuilder()
        val queryUri: Uri = BASE_RECORD_URI
        val sortOrder = "${RecorderColumn.COLUMN_NAME_ID} ASC"

        var cursor: Cursor? = null
        var count = 0
        try {
            cursor = context.contentResolver.query(queryUri, null, null, null, sortOrder, null)
            if (cursor != null && cursor.count > 0) {
                iLog.i(TAG, getVolumeStringForRecordDb())
                iLog.i(TAG, "SOUND RECORD SIZE : ${cursor.count}")
                while (cursor.moveToNext()) {
                    sb = getStringForDbRecord(sb, cursor)
                    count += 1
                    if (count == RECORD_BATCH_COUNT) {
                        count = 0
                        iLog.i(TAG, sb.toString())
                        sb.clear()
                    }
                }
                iLog.i(TAG, sb.toString())
            }
        } catch (e: Exception) {
            iLog.e(TAG, "printRecorderDb query cursor exception", e)
        } finally {
            cursor?.close()
            sb.clear()
            iLog.i(TAG, "\r\n PRINT RECORD DB END")
        }
    }

    private fun getVolumeStringForRecordDb(): String {
        val columnNameList = mutableListOf<String>()
        columnNameList.addAll(RECDOORD_PROJECTION)
        val sb = StringBuilder("RecordDB Column Names")
        sb.append(columnNameList.joinToString() + "\r\n")
        return sb.toString()
    }

    private fun getStringForDbRecord(sb: StringBuilder, cursor: Cursor): StringBuilder {
        val record = Record(cursor, Record.TYPE_FROM_RECORD)
        val recordString = record.toRecordDbString()
        sb.append(recordString + "\r\n")
        return sb
    }
}