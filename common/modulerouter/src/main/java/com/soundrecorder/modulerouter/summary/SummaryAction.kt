/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryAction
 * Description:
 * Version: 1.0
 * Date: 2024/3/7
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/7 1.0 create
 */

package com.soundrecorder.modulerouter.summary

import android.app.Activity
import android.content.Context
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object SummaryAction {

    const val COMPONENT_NAME = "SummaryAction"
    const val FUN_IS_NOTE_SUPPORT = "isNoteSupport"
    const val FUN_START_SUMMARY = "startSummary"
    const val START_SUMMARY_NO_PRE_CHECK = "startSummaryNoPreCheck"
    const val FUN_TO_NOTES_SUMMARY_ACTIVITY = "toNotesSummaryActivity"
    const val FUN_REGISTER_SUMMARY_CALLBACK = "registerSummaryCallback"
    const val FUN_UNREGISTER_SUMMARY_CALLBACK = "unregisterSummaryCallback"
    const val FUN_SUPPORT_THIRD_RECORD_SUMMARY = "isSupportThirdRecordSummary"
    const val FUN_SUPPORT_GLOBAL_SUMMARY = "isSupportGlobalSummary"
    const val FUN_INIT_SUPPORT_SUMMARY = "initSupportSummary"
    const val FUN_LOAD_SUPPORT_SUMMARY_BY_COUNTRY = "loadSupportSummaryByCountry"
    const val FUN_GET_SUPPORT_SUMMARY_VALUE = "getSupportRecordSummaryValue"
    const val FUN_GET_SUMMARY_FRAGMENT = "getSummaryFragment"
    const val FUN_SUMMARY_SELECTED = "summaryFragmentSelected"
    const val FUN_SUMMARY_UN_SELECTED = "summaryFragmentUnSelected"
    const val FUN_SUMMARY_AVAILABLE = "summaryAvailable"
    const val FUN_SUMMARY_GET_SUMMARY_CONTENT = "getSummaryContent"

    const val ACTION_SUMMARY_STATE_CHANGED = "recorder.action.summaryStateChanged"
    const val BUNDLE_FROM_WHERE = "from_where"
    const val BUNDLE_NOTE_ID = "note_id"
    const val BUNDLE_MEDIA_ID = "media_id"
    const val BUNDLE_CALL_ID = "call_id"
    const val BUNDLE_RECORD_TYPE = "record_type"
    const val BUNDLE_RECORD_MODIFY_TIME = "record_modify_time"

    const val ERROR_CODE_SUCCESS = 0 // 预处理校验通过
    const val ERROR_CODE_NO_INTERNET = -7 // 无网络
    const val ERROR_CODE_FORMAT = -1 // 格式不支持
    const val ERROR_CODE_SIZE_SMALL = -2 // 文件太小
    const val ERROR_CODE_SIZE_LARGE = -3 // 文件大小过大，超过500M
    const val ERROR_CODE_DURATION_SMALL = -4 //文件时长过小
    const val ERROR_CODE_DURATION_LARGE_WARN = -5 //文件时长过大提醒
    const val ERROR_CODE_DURATION_LARGE = -6 // 文件时长过大
    const val ERROR_CODE_SUPER_SAVE_MODE = -8 // 超级省电模式


    private val hasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun isNoteSupport(context: Context): Boolean {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_IS_NOTE_SUPPORT)
                .param(context)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun <T, V, K> startSummary(from: String, mediaRecord: T, sentenceList: List<V>?, markList: List<K>?) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_START_SUMMARY)
                .param(from, mediaRecord, sentenceList, markList)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }

    @JvmStatic
    fun <T, V, K> startSummaryNoPreCheck(from: String, mediaRecord: T, sentenceList: List<V>?, markList: List<K>?) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, START_SUMMARY_NO_PRE_CHECK)
                .param(from, mediaRecord, sentenceList, markList)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }

    @JvmStatic
    fun registerSummaryCallback(mediaId: Long, listener: ISummaryCallback) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_REGISTER_SUMMARY_CALLBACK)
                .param(mediaId, listener)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }

    @JvmStatic
    fun unregisterSummaryCallback(callback: ISummaryCallback) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_UNREGISTER_SUMMARY_CALLBACK)
                .param(callback)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }

    @JvmStatic
    fun toNotesSummaryActivity(
        context: Activity,
        callId: String,
        noteId: String,
        requestCode: Int,
        funCallBack: ((clearSummary: Boolean, disableDialog: AlertDialog?) -> Unit)
    ) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_TO_NOTES_SUMMARY_ACTIVITY)
                .param(context, callId, noteId, requestCode, funCallBack)
                .build()
            OStitch.execute<Boolean>(apiRequest)
        }
    }

    /**
     * 判断第三方通话录音是否支持摘要功能--依赖模块侧
     */
    @JvmStatic
    fun isSupportThirdRecordSummary(context: Context): Boolean {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_SUPPORT_THIRD_RECORD_SUMMARY)
                .param(context)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    /**
     * 判断是否支持全局语音摘要功能
     */
    @JvmStatic
    fun isSupportGlobalSummary(context: Context): Boolean {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_SUPPORT_GLOBAL_SUMMARY)
                .param(context)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    /**
     * 获取能力支持状态
     * @param forceUpdate 强制更新
     */
    @JvmStatic
    fun initSupportSummary(forceUpdate: Boolean) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_INIT_SUPPORT_SUMMARY)
                .param(forceUpdate)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    /**
     * 根据需要加载能力支持状态
     * @param countryCode 当前国家码
     */
    @JvmStatic
    fun loadSupportSummaryByCountry(countryCode: String?) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_LOAD_SUPPORT_SUMMARY_BY_COUNTRY)
                .param(countryCode)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun getSupportRecordSummaryValue(): MutableLiveData<Boolean>? {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_GET_SUPPORT_SUMMARY_VALUE).build()
            OStitch.execute<MutableLiveData<Boolean>>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun getSummaryFragment(): Fragment? {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_GET_SUMMARY_FRAGMENT).build()
            OStitch.execute<Fragment>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun summaryFragmentSelected(fragment: Fragment?) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_SUMMARY_SELECTED)
                .param(fragment)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun summaryFragmentUnSelected(fragment: Fragment?) {
        if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_SUMMARY_UN_SELECTED)
                .param(fragment)
                .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun summaryAvailable(context: Context): Boolean {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_SUMMARY_AVAILABLE)
                .param(context)
                .build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun getSummaryContent(context: Context, mediaId: Long): String {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_SUMMARY_GET_SUMMARY_CONTENT)
                .param(context, mediaId)
                .build()
            OStitch.execute<String>(apiRequest).result ?: ""
        } else {
            ""
        }
    }
}