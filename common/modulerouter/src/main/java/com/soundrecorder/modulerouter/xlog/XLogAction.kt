package com.soundrecorder.modulerouter.xlog

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest


object XLogAction {

    // module 的组件名称
    const val COMPONENT_NAME = "RecordXLog"

    // int方法Action
    const val INIT_LOG = "INIT_LOG"

    // flush方法Action
    const val FLUSH_LOG = "FLUSH_LOG"

    // processPushLog方法Action
    const val PROCESS_PUSH_LOG = "PROCESS_PUSH_LOG"

    // processManueReportLog方法Action
    const val PROCESS_MANUAL_REPORT_LOG = "PROCESS_MANUAL_REPORT_LOG"

    // processPrintDb方法Action
    const val PROCESS_PRINT_DB = "processDBPrint"

    // log.v 方法Action
    const val LOG_V = "LOG_V"

    // log.d 方法Action
    const val LOG_D = "LOG_D"

    // log.i 方法Action
    const val LOG_I = "LOG_I"

    // log.w 方法Action
    const val LOG_W = "LOG_W"

    // log.e 方法Action
    const val LOG_E = "LOG_E"

    // log.e_throwable 方法Action
    const val LOG_E_THROWABLE = "LOG_E_THROWABLE"



    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }



    @JvmStatic
    fun initLog(context: Context) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, INIT_LOG)
                    .param(context).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun flushLog(isSync: Boolean) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, FLUSH_LOG)
                    .param(isSync).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }


    @JvmStatic
    fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, PROCESS_PUSH_LOG)
                    .param(context, cloudLogConfigMsg).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }


    @JvmStatic
    fun processManualReportLog() {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, PROCESS_MANUAL_REPORT_LOG)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }



    @JvmStatic
    fun processDBPrint(context: Context?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, PROCESS_PRINT_DB)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }




    @JvmStatic
    fun v(tag: String?, message: String?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, LOG_V)
                    .param(tag, message)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }


    @JvmStatic
    fun i(tag: String?, message: String?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, LOG_I)
                    .param(tag, message)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }


    @JvmStatic
    fun d(tag: String?, message: String?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, LOG_D)
                    .param(tag, message)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }


    @JvmStatic
    fun w(tag: String?, message: String?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, LOG_W)
                    .param(tag, message)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }


    @JvmStatic
    fun e(tag: String?, message: String?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, LOG_E)
                    .param(tag, message)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }


    @JvmStatic
    fun eWithThrowable(tag: String?, message: String?, e: Throwable?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, LOG_E_THROWABLE)
                    .param(tag, message, e)
                    .build()
            OStitch.execute<Void>(apiRequest).result
        }
    }
}