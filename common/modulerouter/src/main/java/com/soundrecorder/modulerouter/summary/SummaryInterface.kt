/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  SummaryInterface.kt
 * * Description : SummaryInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.summary

import android.app.Activity
import android.content.Context
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.MutableLiveData

interface SummaryInterface {
    fun getSupportRecordSummaryValue(): MutableLiveData<Boolean>
    fun loadSupportSummaryByCountry(countryCode: String?)
    fun initSupportSummary(forceUpdate: Boolean)
    fun isSupportGlobalSummary(context: Context): Boolean
    fun isSupportThirdRecordSummary(context: Context): Boolean
    fun toNotesSummaryActivity(
        context: Activity,
        callId: String,
        noteId: String,
        requestCode: Int,
        funCallBack: (clearSummary: Boolean, disableDialog: AlertDialog?) -> Unit
    )

    fun unregisterSummaryCallback(callback: ISummaryCallback)
    fun registerSummaryCallback(mediaId: Long, listener: ISummaryCallback)
    fun <T, R, E> startSummaryNoPreCheck(
        from: String,
        mediaRecord: T,
        sentenceList: List<R>? = null,
        markList: List<E>?
    )

    fun <T, R, E> startSummary(
        from: String,
        mediaRecord: T,
        sentenceList: List<R>?,
        markList: List<E>?
    )

    fun isNoteSupport(context: Context): Boolean
    fun getSummaryContent(context: Context, mediaId: Long): Pair<String, String>
    fun getAISummaryInterface(): IAISummaryInterface
}


const val ACTION_SUMMARY_STATE_CHANGED = "recorder.action.summaryStateChanged"
const val BUNDLE_FROM_WHERE = "from_where"
const val BUNDLE_NOTE_ID = "note_id"
const val BUNDLE_MEDIA_ID = "media_id"
const val BUNDLE_CALL_ID = "call_id"
const val BUNDLE_RECORD_TYPE = "record_type"
const val BUNDLE_RECORD_MODIFY_TIME = "record_modify_time"
const val BUNDLE_RECORD_TITLE = "record_title"
const val BUNDLE_RECORD_FILE_PATH = "record_file_path"
const val BUNDLE_RECORD_FILE_DURATION = "record_file_duration"

const val ERROR_CODE_SUCCESS = 0 // 预处理校验通过
const val ERROR_CODE_NO_INTERNET = -7 // 无网络
const val ERROR_CODE_FORMAT = -1 // 格式不支持
const val ERROR_CODE_SIZE_SMALL = -2 // 文件太小
const val ERROR_CODE_SIZE_LARGE = -3 // 文件大小过大，超过500M
const val ERROR_CODE_DURATION_SMALL = -4 //文件时长过小
const val ERROR_CODE_DURATION_LARGE_WARN = -5 //文件时长过大提醒
const val ERROR_CODE_DURATION_LARGE = -6 // 文件时长过大
const val ERROR_CODE_SUPER_SAVE_MODE = -8 // 超级省电模式