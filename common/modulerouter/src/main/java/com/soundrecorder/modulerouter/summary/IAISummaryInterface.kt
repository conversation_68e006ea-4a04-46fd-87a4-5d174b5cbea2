/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IAISummaryCallback
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.summary

import androidx.fragment.app.Fragment

interface IAISummaryInterface {

    fun getFragment(): Fragment

    fun summaryFragmentSelected()

    fun summaryFragmentUnSelected()

    fun summaryFragmentScrollEnd(select: Boolean)

    fun summaryFragmentScroll(positionOffset: Float)

    fun isSummaryRunning(): Boolean

    fun setSummaryCallback(callback: IAISummaryCallback)

    fun setBottomMargin(marginBottom: Int)

    fun setPaddingBottom(paddingBottom: Int)

    fun setImmersiveCallback(callback: IImmersiveCallback)

    fun checkSummaryAvailable(callback: (state: Boolean) -> Unit)
}