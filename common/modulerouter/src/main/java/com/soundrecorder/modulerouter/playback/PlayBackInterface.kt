/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  PlayBackInterface.kt
 * * Description : PlayBackInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.playback

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.soundrecorder.modulerouter.smartname.ISmartNameManager

interface PlayBackInterface {
    fun createPlayBackIntent(context: Context): Intent
    fun isPlaybackActivity(context: Context): Boolean
    fun cancelAllConvertTask()
    fun stopConvertService(context: Context)
    fun clearMuteCache(filePath: String)
    fun getPlaybackActivityClass(): Class<*>
    fun startMuteDetectIfNecessary(fullPath: String?)
    fun pausePlay(fragment: Fragment?, clearNotification: Boolean)
    fun onPrivacyPolicySuccess(fragment: Fragment?, type: Int)
    fun setRequestCodeX(fragment: Fragment?, code: Int)
    fun onNewIntent(fragment: Fragment?, intent: Intent?)
    fun onRestoreInstanceState(fragment: Fragment?, saveState: Bundle)
    fun onSaveInstanceState(fragment: Fragment?, outState: Bundle)
    fun onPermissionGranted(fragment: Fragment?)
    fun newPlaybackEmptyFragment(): Fragment
    fun newPlaybackFragment(showLoading: Boolean = true): Fragment
    fun updateConvertConfig()
    fun isSupportWpsExport(): Boolean
    fun readOShareConvertContent(filePath: String?, serverPlanCode: Int?): java.util.ArrayList<*>?
    fun readConvertContent(appContext: Context?, filename: String?, serverPlanCode: Int?): java.util.ArrayList<*>?
    fun getSmartNameManager(): ISmartNameManager
    fun releaseMp3File()
    fun parseCallName(mContext: Context, path: String? = null, mediaId: Long = -1L, mimeType: String): String?
}


const val NOTIFY_CONVERT_STATUS_UPDATE = "oplus.multimedia.soundrecorder.convertStatusUpdate"
const val KEY_NOTIFY_RECORD_ID = "key_convert_record_id"
const val KEY_NOTIFY_CONVERT_STATUS = "key_convert_status"

const val NOTIFY_SMART_NAME_STATUS_UPDATE = "oplus.multimedia.soundrecorder.smartNameStatusUpdate"
const val KEY_NOTIFY_SMART_NAME_STATUS = "key_notify_smart_name_status"
const val KEY_NOTIFY_SMART_NAME_NAME_TEXT = "key_notify_smart_name_name_text"
const val KEY_NOTIFY_SMART_NAME_CONVERT_COMPLETE = "key_notify_smart_name_convert_complete"
