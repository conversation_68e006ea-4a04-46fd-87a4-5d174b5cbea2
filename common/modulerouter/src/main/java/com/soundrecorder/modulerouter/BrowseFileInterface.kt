/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  BrowseFileInterface.kt
 * * Description : BrowseFileInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

interface BrowseFileInterface {
    fun createBrowseFileIntent(context: Context): Intent?
    fun isBrowseFile(context: Context): Boolean
    fun getBrowseFileClass(): Class<*>?
    fun onFileUpdateSuccess(rowId: Long, newPath: String?)
    fun hasPlayBackRecord(activity: Activity?): Boolean
    fun getBrowseActivityViewModel(activity: AppCompatActivity?): ViewModel?
    fun <T> getViewModelWindowType(viewModel: ViewModel?): LiveData<T>?
    fun getViewModelAnimRunning(viewModel: ViewModel?): MutableLiveData<Boolean>?
    fun getViewModelIsFromOther(viewModel: ViewModel?): Boolean
    fun <T> getViewModelSpeakerModeController(viewModel: ViewModel?): T?
    fun <T> getViewModelPlayData(viewModel: ViewModel?): LiveData<T>?
    fun getDeleteSummaryNoteIdLiveData(viewModel: ViewModel?): MutableLiveData<String>?
    fun <T> setViewModelPlayData(viewModel: ViewModel?, playData: T?)
    fun getViewModelClickedToRecord(viewModel: ViewModel?): Boolean
    fun clearViewModelPlayData(viewModel: ViewModel?)
    fun isSmallWindow(viewModel: ViewModel?): Boolean
    fun onConvertSearchStateChanged(
        activity: Activity?,
        inSearch: Boolean,
        searchTextNotEmpty: Boolean,
        outSearchFun: (() -> Unit)
    )

    fun getParentPercentDefault(): Float
    fun getBrowseFileActivityName(): String?
    fun hasFastPlayingFile(activity: Activity?): Boolean
    fun getBrowseFileActivityViewModel(activity: AppCompatActivity?): MutableLiveData<Boolean>
    fun isNoteExist(noteId: String): Boolean
    fun setViewModelWindowType(viewModel: ViewModel?, config: Configuration?)
    fun <T> showGroupChooseFragment(fragment: Fragment?, record: T)
    fun isSupportSmartName(viewModel: ViewModel?): Boolean
    fun isSmartNaming(mediaId: Long): Boolean
}