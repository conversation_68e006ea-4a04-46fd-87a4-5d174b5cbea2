/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BrowseFileAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object BrowseFileAction {
    const val COMPONENT_NAME = "BrowseFile"
    const val CREATE_BROWSE_FILE_INTENT = "createBrowseFileIntent"
    const val IS_BROWSE_FILE = "isBrowseFile"
    const val GET_BROWSE_FILE_CLASS = "getBrowseFileClass"
    const val ON_FILE_UPDATE_SUCCESS = "onFileUpdateSuccess"
    const val HAS_PLAYBACK_RECORD = "hasPlayBackRecord"
    const val GET_BROWSE_ACTIVITY_VIEWMODEL = "getBrowseActivityViewModel"
    const val SET_VIEWMODEL_WINDOWTYPE = "setViewModelWindowType"
    const val GET_VIEWMODEL_WINDOWTYPE = "getViewModelWindowType"
    const val GET_VIEWMODEL_CHILD_ANIM_RUNNING = "getViewModelChildAnimRunning"
    const val GET_VIEWMODEL_IS_FROM_OTHER = "getViewModelIsFromOther"
    const val GET_VIEWMODEL_SPEAKER_MODE_CONTROLLER = "getViewModelSpeakerModeController"
    const val GET_VIEWMODEL_PLAYDATA = "getViewModelPlayData"
    const val SET_VIEWMODEL_PLAYDATA = "setViewModelPlayData"
    const val GET_VIEWMODEL_CLICKED_TO_RECORD = "getViewModelClickedToRecord"
    const val FUN_VIEWMODEL_CLEAR_PLAYDATA = "funViewModelClearPlayData"
    const val FUN_VIEWMODEL_IS_SMALL_WINDOW = "funViewModelIsSmallWindow"
    const val ON_CONVERT_SEARCH_STATE_CHANGED = "onConvertSearchStateChanged"
    const val GET_PARENT_PERCENT_DEFAULT = "getParentPercentDefault"
    const val GET_BROWSE_FILE_ACTIVITY_NAME = "getBrowseFileActivityName"
    const val GET_LIVE_DATA_DELETE_SUMMARY_NOTE_ID = "getDeleteSummaryNoteIdLiveData"
    const val FUN_SHOW_GROUP_CHOOSE_FRAGMENT = "funShowGroupChooseFragment"

    /*是否快捷播放文件*/
    const val HAS_FAST_PLAYING_FILE = "hasFastPlayingFile"
    const val GET_BROWSE_FILE_ACTIVITY_NEED_REFRESH = "getBrowseFileActivityNeedRefresh"
    const val IS_NOTE_EXIST = "isNoteExist"

    const val FUN_VIEWMODEL_IS_SUPPORT_SMART_NAME = "funViewModelIsSupportSmartName"
    const val FUN_IS_SMART_NAMING = "funIsSmartNaming"

    val mHasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun createBrowseFileIntent(context: Context): Intent? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CREATE_BROWSE_FILE_INTENT)
                .param(context).build()
            OStitch.execute<Intent>(apiRequest).result
        } else {
            null
        }
    }

    @JvmStatic
    fun isBrowseFile(context: Context): Boolean {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, IS_BROWSE_FILE)
                .param(context).build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun getBrowseFileClass(): Class<*>? {
        return if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_BROWSE_FILE_CLASS).build()
            OStitch.execute<Class<*>>(apiRequest).result
        } else null
    }

    @JvmStatic
    fun onFileUpdateSuccess(rowId: Long, newPath: String?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_FILE_UPDATE_SUCCESS)
                .param(rowId, newPath).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun hasPlayBackRecord(activity: Activity?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, HAS_PLAYBACK_RECORD)
                .param(activity).build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun getBrowseActivityViewModel(activity: AppCompatActivity?): ViewModel? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_BROWSE_ACTIVITY_VIEWMODEL)
                .param(activity).build()
            return OStitch.execute<ViewModel>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun setViewModelWindowType(viewModel: ViewModel?, config: Configuration?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SET_VIEWMODEL_WINDOWTYPE)
                .param(viewModel, config).build()
            OStitch.execute<Void>(apiRequest).result
        }
    }

    @JvmStatic
    fun <T> getViewModelWindowType(viewModel: ViewModel?): LiveData<T>? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_VIEWMODEL_WINDOWTYPE)
                .param(viewModel).build()
            return OStitch.execute<LiveData<T>>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun getViewModelAnimRunning(viewModel: ViewModel?): MutableLiveData<Boolean>? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_VIEWMODEL_CHILD_ANIM_RUNNING)
                .param(viewModel).build()
            return OStitch.execute<MutableLiveData<Boolean>>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun getViewModelIsFromOther(viewModel: ViewModel?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_VIEWMODEL_IS_FROM_OTHER)
                .param(viewModel).build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    inline fun <reified T> getViewModelSpeakerModeController(viewModel: ViewModel?): T? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, GET_VIEWMODEL_SPEAKER_MODE_CONTROLLER)
                    .param(viewModel).build()
            return OStitch.execute<T>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun <T> getViewModelPlayData(viewModel: ViewModel?): LiveData<T>? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_VIEWMODEL_PLAYDATA)
                .param(viewModel).build()
            return OStitch.execute<LiveData<T>>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun getDeleteSummaryNoteIdLiveData(viewModel: ViewModel?): MutableLiveData<String>? {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, GET_LIVE_DATA_DELETE_SUMMARY_NOTE_ID)
                    .param(viewModel).build()
            return OStitch.execute<MutableLiveData<String>>(apiRequest).result
        }
        return null
    }

    @JvmStatic
    fun <T> setViewModelPlayData(viewModel: ViewModel?, playData: T?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, SET_VIEWMODEL_PLAYDATA)
                .param(viewModel, playData).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun getViewModelClickedToRecord(viewModel: ViewModel?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_VIEWMODEL_CLICKED_TO_RECORD)
                .param(viewModel).build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun clearViewModelPlayData(viewModel: ViewModel?) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_VIEWMODEL_CLEAR_PLAYDATA)
                .param(viewModel).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun isSmallWindow(viewModel: ViewModel?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_VIEWMODEL_IS_SMALL_WINDOW)
                .param(viewModel).build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun onConvertSearchStateChanged(
        activity: Activity?,
        inSearch: Boolean,
        searchTextNotEmpty: Boolean,
        outSearchFun: (() -> Unit)
    ) {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, ON_CONVERT_SEARCH_STATE_CHANGED)
                .param(activity, inSearch, searchTextNotEmpty, outSearchFun).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun getParentPercentDefault(): Float {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_PARENT_PERCENT_DEFAULT)
                .build()
            return OStitch.execute<Float>(apiRequest).result ?: 1F
        }
        return 1F
    }

    @JvmStatic
    fun getBrowseFileActivityName(): String? {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, GET_BROWSE_FILE_ACTIVITY_NAME)
                .build()
            return OStitch.execute<String>(apiRequest).result
        }
        return ""
    }

    /**
     * 是否有音频文件在快捷播放
     */
    @JvmStatic
    fun hasFastPlayingFile(activity: Activity?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, HAS_FAST_PLAYING_FILE)
                .param(activity)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun getBrowseFileActivityViewModel(activity: AppCompatActivity?): MutableLiveData<Boolean> {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, GET_BROWSE_FILE_ACTIVITY_NEED_REFRESH)
                    .param(activity).build()
            return OStitch.execute<MutableLiveData<Boolean>>(apiRequest).result ?: MutableLiveData(
                true
            )
        }
        return MutableLiveData(true)
    }

    @JvmStatic
    fun isNoteExist(noteId: String): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, IS_NOTE_EXIST)
                .param(noteId)
                .build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun <T> showGroupChooseFragment(fragment: Fragment?, record: T?) {
        if (mHasComponent) {
            val apiRequest =
                ApiRequest.Builder(COMPONENT_NAME, FUN_SHOW_GROUP_CHOOSE_FRAGMENT)
                    .param(fragment, record).build()
            OStitch.execute<Void>(apiRequest)
        }
    }

    @JvmStatic
    fun isSupportSmartName(viewModel: ViewModel?): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_VIEWMODEL_IS_SUPPORT_SMART_NAME)
                .param(viewModel).build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }

    @JvmStatic
    fun isSmartNaming(mediaId: Long): Boolean {
        if (mHasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, FUN_IS_SMART_NAMING)
                .param(mediaId).build()
            return OStitch.execute<Boolean>(apiRequest).result ?: false
        }
        return false
    }
}