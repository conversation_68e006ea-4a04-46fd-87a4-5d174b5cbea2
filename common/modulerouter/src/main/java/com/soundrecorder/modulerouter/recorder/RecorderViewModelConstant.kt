/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  constant
 * * Description : constant
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.recorder

/**
 * MarkAction
 * WaveState
 * SaveFileState
 * 与RecorderViewModel里面保持一致
 */
object MarkAction {
    const val SINGLE_ADD = 0
    const val MULTI_ADD = 1
    const val DELETE = 2
    const val RENAME = 3
}

object WaveState {
    const val START = 0
    const val STOP = 1
    const val UPDATE = 2
}

object SaveFileState {
    const val INIT = -1
    const val START_LOADING = 0
    const val SHOW_LOADING_DIALOG = 1
    const val SUCCESS = 2
    const val ERROR = 3
}


const val INIT = -1
const val HALT_ON = 0
const val RECORDING = 1
const val PAUSED = 2