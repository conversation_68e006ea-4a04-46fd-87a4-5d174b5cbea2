/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IAsrPluginDownloadDelegate
 * Description:
 * Version: 1.0
 * Date: 2025/4/7
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/4/7 1.0 create
 */

package com.soundrecorder.modulerouter.translate

import android.content.Context

interface IAsrPluginDownloadDelegate {
    fun isAsrPluginDownload(): Boolean
    fun checkAndDownloadAsrPlugin(context: Context, isSummaryTab: Boolean, callback: IAsrDownloadCallback?)
    fun isSmartNamePluginDownload(): Boolean
    fun checkAndDownloadSmartNamePlugin(context: Context, isSummaryTab: Boolean, callback: IAsrDownloadCallback?)
    fun checkAndDownloadAsrUnifiedPlugins(context: Context, isSummaryTab: <PERSON>olean, callback: IAsrDownloadCallback?)
}

interface IAsrDownloadCallback {
    fun downloadSuccess(sceneName: String)
    fun downloadFail(errorMessage: String)
}