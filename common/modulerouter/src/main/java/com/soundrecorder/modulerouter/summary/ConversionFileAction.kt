/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConversionFileAction.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.summary

import android.content.Context
import com.inno.ostitch.OStitch
import com.inno.ostitch.model.ApiRequest

object ConversionFileAction {
    const val COMPONENT_NAME = "ConversionFileAction"
    const val CONVERSION_FILE_TO_WORD = "ConversionFileToWord"
    const val CONVERSION_FILE_TO_PDF = "ConversionFileToPdf"
    const val CONVERSION_FILE_TO_TXT = "ConversionFileToTxt"
    private val hasComponent by lazy {
        OStitch.hasComponent(COMPONENT_NAME)
    }

    @JvmStatic
    fun conversionFileToWord(
        act: Context,
        targetPath: String,
        title: String,
        text: String
    ): Boolean {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CONVERSION_FILE_TO_TXT)
                .param(act, targetPath, title, text)
                .build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }

    @JvmStatic
    fun conversionFileToPdf(
        act: Context,
        targetPath: String,
        title: String,
        text: String
    ): Boolean {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CONVERSION_FILE_TO_PDF)
                .param(act, targetPath, title, text)
                .build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }
    @JvmStatic
    fun conversionFileToTxt(
        act: Context,
        targetPath: String,
        title: String,
        text: String
    ): Boolean {
        return if (hasComponent) {
            val apiRequest = ApiRequest.Builder(COMPONENT_NAME, CONVERSION_FILE_TO_PDF)
                .param(act, targetPath, title, text)
                .build()
            OStitch.execute<Boolean>(apiRequest).result ?: false
        } else {
            false
        }
    }
}