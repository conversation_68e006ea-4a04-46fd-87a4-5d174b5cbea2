/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  NetworkUtils.java
 * * Description: NetworkUtils.java
 * * Version: 1.0
 * * Date : 2019/9/17
 * * Author: liuyulong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * liuyulong  2019/9/17      1.0    build this module
 ****************************************************************/
package com.soundrecorder.base.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

public class NetworkUtils {
    private static final String TAG = "NetworkUtils";
    public static final int NETWORK_NONE = 0; // 没有网络连接
    public static final int NETWORK_WIFI = 1; // wifi连接
    public static final int NETWORK_MOBILE = 2; // 手机流量

    public static boolean isNetworkInvalid(Context context) {
        if (null == context) {
            return true;
        }
        ConnectivityManager manager = (ConnectivityManager) context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (manager == null) {
            return true;
        }
        NetworkInfo networkinfo = manager.getActiveNetworkInfo();
        return (networkinfo == null) || (!networkinfo.isAvailable()) || (!networkinfo.isConnected());
    }

    public static int getNetState(Context context) {
        if (null == context) {
            return NETWORK_NONE;
        }
        ConnectivityManager connManager = (ConnectivityManager) context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE); // 获取网络服务
        if (null == connManager) { // 为空则认为无网络
            return NETWORK_NONE;
        }
        // 获取网络类型，如果为空，返回无网络
        NetworkInfo activeNetInfo = connManager.getActiveNetworkInfo();
        if (activeNetInfo == null || !activeNetInfo.isAvailable()) {
            return NETWORK_NONE;
        }
        // 判断是否为WIFI
        NetworkInfo wifiInfo = connManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
        if (null != wifiInfo) {
            NetworkInfo.State state = wifiInfo.getState();
            if ((state == NetworkInfo.State.CONNECTED || state == NetworkInfo.State.CONNECTING)) {
                return NETWORK_WIFI;
            }
        }
        return NETWORK_MOBILE;
    }
}
