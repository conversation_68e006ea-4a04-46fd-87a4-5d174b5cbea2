package com.soundrecorder.base.utils;
/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: PrefUtil
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.os.UserManager;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class PrefUtil {

    public static final String KEY_DATA_CLEARED = "key_data_cleared";
    public static final String KEY_LAST_ENTER_TIME = "key_last_enter_time";
    public static final String KEY_GUIDE_STATE = "key_guide_state";
    public static final String KEY_GUIDE_IGNORE_TIME = "key_guide_ignore_time";
    public static final String KEY_CLOUD_CONFIG_IGNORE_TIME = "key_cloud_config_ignore_time";
    public static final String FIRST_USE_CONVERT = "first_use_convert";
    public static final String KEY_LAST_SYNC_TIME = "key_last_sync_time";
    public static final String KEY_CURRENT_CONVERT_FILE_MEDIA_ID = "key_current_convert_file_media_id";
    public static final String KEY_SAVE_RECORD_ID_WHEN_RECORDING = "key_save_record_id_when_recording";
    public static final String KEY_SAVE_RECORD_ID_FOR_ABNORMAL_EXIT = "key_save_record_id_for_abnormal_exit";
    public static final String KEY_VERIFIED_ACCOUNT_SSOID = "key_verified_account_ssoid";

    public static final String KEY_RECORD_FEEDBACK_PERMISSION = "key_record_feedback_permission";

    public static final String KEY_RECORD_GROUP_ID = "key_record_group_id";
    public static final String KEY_RECORD_GROUP_UUID = "key_record_group_uuid";
    /*通话录音是否分组*/
    public static final String KEY_RECORD_CALL_GROUP_SORT = "key_record_call_group_sort";
    public static final String KEY_LINK_SHARE_DOMAIN_NAME = "key_link_share_domain_name";

    public static final String KEY_SMART_NAME_GUIDE_SHOW = "key_smart_name_guide_show";

    public static final String KEY_SMART_NAME_SWITCH_OPEN = "key_smart_name_switch_open";

    // 实时ASR语种
    public static final String OPLUS_AI_TEXT_ASR_LANGUAGE = "oplus_ai_text_asr_language";

    // ASR支持的语言列表
    public static final String OPLUS_AI_TEXT_ASR_SUPPORT_LANGUAGE_LIST = "oplus_ai_text_asr_support_language_list";

    public static final long DEFAULT_VALUE_CURRENT_CONVERT_FILE_MEDIA_ID = -1;
    public static final String SPEAKER_STATE = "speaker_state";
    private static final String TAG = "PrefUtil";
    private static final String PREF_SUFFIX = "_sync_info";
    private static SharedPreferences sPref;
    private static Editor sEditor;


    public static SharedPreferences getSharedPreferences(Context context) {
        if (context == null) {
            return null;
        }
        if (sPref == null) {
            sPref = getSharedPreferences(context, PREF_SUFFIX);
        }
        return sPref;
    }

    private static Editor getEditor(Context context) {
        if (context == null) {
            return null;
        }
        if (sEditor == null) {
            SharedPreferences sp = getSharedPreferences(context);
            if (sp != null) {
                sEditor = sp.edit();
            }
        }
        return sEditor;
    }

    public static void clearPreference(Context context, String key) {
        clearPreference(getEditor(context), key);
    }


    private static SharedPreferences getSharedPreferences(Context context, String suffix) {
        if (context == null) {
            DebugUtil.e(TAG, "[getSharedPreferences] Context is null!");
            return null;
        }
        if (!isUserUnlocked(context)) {
            DebugUtil.e(TAG, "[getSharedPreferences] Can not get SP while device locking.");
            return null;
        }
        String sharedPreferencesName = context.getPackageName() + suffix;
        return context.getSharedPreferences(sharedPreferencesName, Context.MODE_PRIVATE);
    }


    /**
     * whether, File System is unlocked
     *
     * @param context
     * @return
     */
    private static boolean isUserUnlocked(Context context) {

        boolean isUserUnlocked = true;
        if (context == null) {
            return isUserUnlocked;
        }
        final UserManager userManager = (UserManager) context.getSystemService(UserManager.class);
        if (userManager != null) {
            isUserUnlocked = userManager.isUserUnlocked();
        }

        DebugUtil.i(TAG, "isUserUnlocked = " + isUserUnlocked);
        return isUserUnlocked;
    }

    public static void putStringMap(Context context, String key, Map<String, String> values) {
        putStringMap(getEditor(context), key, values);
    }

    public static void putStringSet(Context context, String key, Set<String> values) {
        putStringSet(getEditor(context), key, values);
    }

    public static void putString(Context context, String key, String value) {
        putString(getEditor(context), key, value);
    }

    public static void putBoolean(Context context, String key, boolean value) {
        putBoolean(getEditor(context), key, value);
    }

    public static void putFloat(Context context, String key, float value) {
        putFloat(getEditor(context), key, value);
    }

    public static void putLong(Context context, String key, long value) {
        putLong(getEditor(context), key, value);
    }

    public static void putInt(Context context, String key, int value) {
        putInt(getEditor(context), key, value);
    }

    public static boolean putObject(Context context, String key, Object value) {
        return putObject(getEditor(context), key, value);
    }

    public static Map<String, String> getStringMap(Context context, String key, Map<String, String> defValues) {
        return getStringMap(getSharedPreferences(context), key, defValues);
    }

    public static Set<String> getStringSet(Context context, String key, Set<String> defValues) {
        return getStringSet(getSharedPreferences(context), key, defValues);
    }

    public static String getString(Context context, String key, String defValue) {
        return getString(getSharedPreferences(context), key, defValue);
    }

    public static boolean getBoolean(Context context, String key, boolean defValue) {
        return getBoolean(getSharedPreferences(context), key, defValue);
    }

    public static float getFloat(Context context, String key, float defValue) {
        return getFloat(getSharedPreferences(context), key, defValue);
    }

    public static long getLong(Context context, String key, long defValue) {
        return getLong(getSharedPreferences(context), key, defValue);
    }

    public static int getInt(Context context, String key, int defValue) {
        return getInt(getSharedPreferences(context), key, defValue);
    }

    public static Object getObject(Context context, String key, Object defValue) {
        return getObject(getSharedPreferences(context), key, defValue);
    }

    /**
     * 存储ASR支持的语言列表
     * @param context 上下文
     * @param languageList 语言列表
     */
    public static void putAsrSupportLanguageList(Context context, List<String> languageList) {
        if (languageList != null) {
            Set<String> languageSet = new HashSet<>(languageList);
            putStringSet(context, OPLUS_AI_TEXT_ASR_SUPPORT_LANGUAGE_LIST, languageSet);
        }
    }

    /**
     * 获取ASR支持的语言列表
     * @param context 上下文
     * @return 语言列表，如果没有数据返回null
     */
    public static List<String> getAsrSupportLanguageList(Context context) {
        Set<String> languageSet = getStringSet(context, OPLUS_AI_TEXT_ASR_SUPPORT_LANGUAGE_LIST, null);
        if (languageSet != null && !languageSet.isEmpty()) {
            return new ArrayList<>(languageSet);
        }
        return null;
    }


    private static void clearPreference(Editor editor, String key) {
        if (TextUtils.isEmpty(key)) {
            return;
        }
        if (editor == null) {
            return;
        }
        editor.remove(key);
        editor.apply();
    }

    protected static void clearAll(Editor editor) {
        if (editor == null) {
            return;
        }
        editor.clear();
        editor.apply();
    }

    private static void putStringMap(Editor editor, String key, Map<String, String> values) {
        if (editor == null) {
            return;
        }
        Gson gson = new Gson();
        String json = gson.toJson(values);
        editor.putString(key, json);
        editor.apply();
    }

    private static void putStringSet(Editor editor, String key, Set<String> values) {
        if (editor == null) {
            return;
        }
        editor.putStringSet(key, values);
        editor.apply();
    }

    private static void putString(Editor editor, String key, String value) {
        if (editor == null) {
            return;
        }
        editor.putString(key, value);
        editor.apply();
    }

    private static void putBoolean(Editor editor, String key, boolean value) {
        if (editor == null) {
            return;
        }
        editor.putBoolean(key, value);
        editor.apply();
    }

    private static void putFloat(Editor editor, String key, float value) {
        if (editor == null) {
            return;
        }
        editor.putFloat(key, value);
        editor.apply();
    }

    private static void putLong(Editor editor, String key, long value) {
        if (editor == null) {
            return;
        }
        editor.putLong(key, value);
        editor.apply();
    }

    private static void putInt(Editor editor, String key, int value) {
        if (editor == null) {
            return;
        }
        editor.putInt(key, value);
        editor.apply();
    }

    /**
     * 将对象储存到 sharepreference
     *
     * @param key
     * @param device
     * @param <T>
     */
    private static <T> boolean putObject(Editor editor, String key, T device) {
        String oAuth_Base64 = Base64Util.object2Base64Str(device);
        if (oAuth_Base64 == null) {
            return false;
        }
        editor.putString(key, oAuth_Base64);
        editor.apply();
        return true;
    }

    private static Map<String, String> getStringMap(SharedPreferences sharedPreferences, String key, Map<String, String> defValues) {
        if (sharedPreferences == null) {
            return defValues;
        }
        String json = sharedPreferences.getString(key, null);
        if (json != null) {
            Gson gson = new Gson();
            return gson.fromJson(json, new TypeToken<Map<String, String>>(){}.getType());
        } else {
            return defValues;
        }
    }

    private static Set<String> getStringSet(SharedPreferences sharedPreferences, String key, Set<String> defValues) {
        if (sharedPreferences == null) {
            return defValues;
        }
        return sharedPreferences.getStringSet(key, defValues);
    }

    private static String getString(SharedPreferences sharedPreferences, String key, String defValue) {
        if (sharedPreferences == null) {
            return defValue;
        }
        return sharedPreferences.getString(key, defValue);
    }

    private static boolean getBoolean(SharedPreferences sharedPreferences, String key, boolean defValue) {
        if (sharedPreferences == null) {
            return defValue;
        }
        return sharedPreferences.getBoolean(key, defValue);
    }

    private static float getFloat(SharedPreferences sharedPreferences, String key, float defValue) {
        if (sharedPreferences == null) {
            return defValue;
        }
        return sharedPreferences.getFloat(key, defValue);
    }

    private static long getLong(SharedPreferences sharedPreferences, String key, long defValue) {
        if (sharedPreferences == null) {
            return defValue;
        }
        return sharedPreferences.getLong(key, defValue);
    }

    private static int getInt(SharedPreferences sharedPreferences, String key, int defValue) {
        if (sharedPreferences == null) {
            return defValue;
        }
        return sharedPreferences.getInt(key, defValue);
    }

    /**
     * 将对象从 shareprerence 中取出来
     *
     * @param key
     * @param <T>
     * @return
     */
    private static <T> T getObject(SharedPreferences sharedPreferences, String key, T defValue) {
        if (sharedPreferences == null) {
            return defValue;
        }
        String productBase64 = sharedPreferences.getString(key, null);
        return Base64Util.base64Str2Object(productBase64);
    }

}