package com.soundrecorder.base.utils

import android.content.Context
import android.os.Environment
import android.os.StatFs

object StorageUtil {

    const val TAG = "StorageUtil"
    private const val STORAGE_SIZE_UNIT: Long = 1024


    /**
     * getAvailableSpace for internal sdcard
     * input context appContext
     * output availableSpace in byte on this phone internal sdcard
     */
    fun getAvailableSpace(context: Context): Long {
        var space: Long = -1
        val mStoragePhone = AddonAdapterCompatUtil.getInternalSdDirectory(context)?.path
        var fs: StatFs? = null
        val hasInternal: Boolean = AddonAdapterCompatUtil.isInternalSdMounted(context)
        try {
            if (hasInternal) {
                fs = StatFs(mStoragePhone)
                val blocks = fs.availableBlocksLong
                val blockSize = fs.blockSizeLong
                space = blocks * blockSize
                DebugUtil.i(TAG, " getAvailableSpace blocks " + blocks + " blockSize " + blockSize
                        + ", blocks * blockSize " + space + ", phoneStorage from mSettingAdapter: " + mStoragePhone)
            }
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "getAvailableSpace failed. ", e)
        }
        DebugUtil.i(TAG, "getAvailableSpace hasInternal: $hasInternal, space $space")
        return space
    }

    /**
     * 获取手机存储剩余空间
     * @return free space unit MB
     */
    @JvmStatic
    fun getFreeDiskSpace(): Long {
        val statFs = StatFs(Environment.getExternalStorageDirectory().path)
        val byteAvailable = statFs.blockSizeLong * statFs.availableBlocksLong
        return byteAvailable / (STORAGE_SIZE_UNIT * STORAGE_SIZE_UNIT)
    }
}