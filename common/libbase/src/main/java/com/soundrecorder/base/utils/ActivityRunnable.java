package com.soundrecorder.base.utils;

import java.lang.ref.WeakReference;

public class ActivityRunnable<T> implements Runnable {

    private static final String TAG = "ActivityRunnable";
    private String name = "ActivityRunnable";
    protected WeakReference<T> mActivityWeakReference = null;

    public ActivityRunnable(String name, T activity) {
        this.name = name;
        this.mActivityWeakReference = new WeakReference<>(activity);
    }

    @Override
    public void run() {
        if (mActivityWeakReference != null) {
            T activity = (T) mActivityWeakReference.get();
            if (activity != null) {
                DebugUtil.i(TAG, "name: " + name + ", start run");
                run(activity);
            } else {
                DebugUtil.i(TAG, "name: " + name + ", weakreference.get is null");
            }
        } else {
            DebugUtil.i(TAG, "name: " + name + ", weakreference is null");
        }
    }

    public void run(T activity) {

    }


}
