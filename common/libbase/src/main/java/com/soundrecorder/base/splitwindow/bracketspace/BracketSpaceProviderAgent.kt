/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BracketSpaceProviderAgent
 * Description: 往悬停空间写入数据工具类
 * Version: 1.0
 * Date: 2022/9/13
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/9/13 1.0 create
 */

package com.soundrecorder.base.splitwindow.bracketspace

import android.content.Context
import android.net.Uri
import android.os.Bundle
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption

object BracketSpaceProviderAgent {
    private const val TAG = "BracketSpaceProviderAgent"
    private const val BYTE_SIZE = 1024

    /*悬停空间provider authority */
    private val URI_MESSAGE_PROVIDER: Uri = Uri.parse("content://com.oplus.bracketspace.outward.provider")

    /*方法名：往悬停空间写入数据*/
    private const val METHOD_INSERT_MESSAGE = "METHOD_INSERT"

    /*方法名：更新悬停空间录音启动时间*/
    private const val METHOD_UPDATE_LAUNCH_TIME = "METHOD_UPDATE_LAUNCH_TIME"

    /*悬停空间包名*/
    private const val BRACKETSPACE_PACKAGE_NAME = "com.oplus.bracketspace"

    /**
     * 更新录音主进程启动时间；
     * 前提：主进程 + 非重建
     * @param context
     */
    @JvmStatic
    fun updateAppLaunchTime(context: Context): Boolean {
        if (!checkCanCallBracketSpaceProvider()) {
            return false
        }
        val dataBundle = Bundle()
        dataBundle.putString("message_id", "${context.packageName}_01")
        dataBundle.putString("package_name", context.packageName)
        try {
            /*返回值是1表示插入成功;返回值是-1表示插入失败或者说当前不允许写入(比如用户没有授权隐私协议)*/
            val resultCode = context.contentResolver.acquireUnstableContentProviderClient(
                URI_MESSAGE_PROVIDER
            )?.use {
                it.call(METHOD_UPDATE_LAUNCH_TIME, null, dataBundle)?.getInt("call_result")
            }
            DebugUtil.i(TAG, "updateAppLaunchTime resultCode= $resultCode")
            return resultCode == 1
        } catch (ignored: Exception) {
            DebugUtil.e(TAG, "updateAppLaunchTime error $ignored ")
        }
        return false
    }

    /**
     * 往悬停空间PROVIDER 写入录音数据
     * @param context
     * @param messageEntryBean
     */
    @JvmStatic
    fun insertBracketSpaceData(context: Context, messageEntryBean: MessageEntryBean): Boolean {
        if (!checkCanCallBracketSpaceProvider()) {
            return false
        }
        val dataBundle = Bundle().apply {
            putString("message_id", messageEntryBean.messageId)
            putString("package_name", messageEntryBean.packageName ?: context.packageName)
            //视频、音乐名称等
            putString("title", messageEntryBean.title)
            //歌手名称、视频播放进度信息、主播名称等
            messageEntryBean.summary?.let {
                putString("summary", it)
            }
            //提供一些辅助信息，如歌曲专辑名称、直播间分区、运动难度等
            messageEntryBean.content?.let {
                putString("content", it)
            }
            //进度百分比
            messageEntryBean.playProgress?.let {
                putInt("play_progress", it)
            }
            putString("target_intent", messageEntryBean.targetIntent)
            //注意命名格式，messageId + 图片格式 此处为保存到悬停空间的文件名,预览图片格式最好是png或者jpg
            putString("picture_name", messageEntryBean.pictureName)
        }

        try {
            /*返回值是1表示插入成功;返回值是-1表示插入失败或者说当前不允许写入(比如用户没有授权隐私协议)*/
            val resultCode = context.contentResolver.acquireUnstableContentProviderClient(
                URI_MESSAGE_PROVIDER
            )?.use {
                it.call(METHOD_INSERT_MESSAGE, null, dataBundle)?.getInt("call_result")
            }
            DebugUtil.i(TAG, " insertBracketSpaceData resultCode= $resultCode")
            return resultCode == 1
        } catch (ignored: Exception) {
            DebugUtil.e(TAG, "insertBracketSpaceData error $ignored ")
        }
        return false
    }

    /**
     * 将海报或者预览图保存到悬停空间目录
     * @param context
     * @param assetResName
     * @param pictureName 写入悬停空间的图片文件名称，要跟provider中的一致，xxx.png/.jpg
     */
    @JvmStatic
    fun writePictureToBracketDir(context: Context, assetResName: String, pictureName: String): Boolean {
        if (!checkCanCallBracketSpaceProvider()) {
            return false
        }
        val uri = Uri.parse(URI_MESSAGE_PROVIDER.toString() + pictureName)
        try {
            return context.assets.open(assetResName).use { inputSteam ->
                //从悬停空间provider获取文件句柄，通过句柄写到悬停空间中
                return context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                    val buffer = ByteArray(BYTE_SIZE)
                    var byteRead: Int
                    while (-1 != inputSteam.read(buffer).also { byteRead = it }) {
                        outputStream.write(buffer, 0, byteRead)
                    }
                    outputStream.flush()
                    return true
                } ?: false
            }
        } catch (ignored: Exception) {
            DebugUtil.w(TAG, "writePictureToBracketDir error: $ignored")
        }
        return false
    }

    @JvmStatic
    fun checkCanCallBracketSpaceProvider(): Boolean = FeatureOption.getIsFoldFeature()
            && AppUtil.isAppInstalled(BRACKETSPACE_PACKAGE_NAME)
}