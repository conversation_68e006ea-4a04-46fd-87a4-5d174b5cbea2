package com.soundrecorder.base.utils

object OS12FeatureUtil {

    const val TAG = "OS12FeatureUtil"

    private const val PROJECT_PROPERTIES_NAME = "ro.boot.prjname"
    private const val PRODUCT_PROPERTIES_NAME = "ro.oplus.version.my_product"
    private const val OS12_OPLUSFEATURE_NAME = "oplus.software.newsoundrecord.os12_1_function"

    /*PEP项目（RSA4.0覆盖）-属性名称*/
    private const val PROPERTY_RSA4_0 = "ro.oplus.rsa"

    /*PEP项目（RSA4.0覆盖）-对应值;PEP tier3是只有Android 13新项目才开始存在*/
    private const val RSA4_0_VALUE = "rsa4-tier3"

    private val PRODUCTS_FIND_X4 = arrayOf("21001", "21201", "21005", "21205", "21007")

    /**
     * konka
     * 内销：23105  - PKC110
     *      23106 -  PKC130
     * 外销：23216 - CPH2659
     *
     * yala
     * 内销：23101 - PKB110
     * 外销：23205 - CPH2651
     *
     * dodge
     * 内销：23821 - PJZ110
     * 外销：23893 - CPH2649
     *      23894 - CPH2653
     *      23895 - CPH2655
     */
    private val PRODUCTS_KYD = arrayOf("23105", "23106", "23101", "23821")

    private const val COLOROS_13 = 26
    private const val COLOROS_13_2 = 29
    private const val COLOROS_12_0_VERSION = 23
    private const val COLOROS_11_3 = 22
    private const val COLOROS_14_0 = 30
    private const val COLOROS_14_1 = 33
    private const val COLOROS_16 = 37

    val isRSA4_0_PET by lazy {
        val value = OSDKCompatUtils.getFeatureProperty(PROPERTY_RSA4_0)
        val result = (RSA4_0_VALUE == value)
        DebugUtil.i(TAG, "is RSA4.0 : $result")
        result
    }

    /**
     * os的版本
     */
    private val osVersion: Int by lazy {
        AddonAdapterCompatUtil.getOplusOSVERSION()
    }


    /**
     *  是否是FindX4项目，true 为findX4；false为非findX4项目
     *
     */
    @JvmStatic
    fun isFindX4(): Boolean {
        var result = false
        val projectName = OSDKCompatUtils.getFeatureProperty(PROJECT_PROPERTIES_NAME)
        DebugUtil.i(TAG, "isFindX4 projectName $projectName")
        if (projectName.isEmpty()) {
            val productName = OSDKCompatUtils.getFeatureProperty(PRODUCT_PROPERTIES_NAME)
            DebugUtil.i(TAG, "isFindX4 productName $productName")
            if (productName.isNotEmpty()) {
                for (product in PRODUCTS_FIND_X4) {
                    if (productName.contains(product, true)) {
                        result = true
                        break
                    }
                }
            }
        } else {
            result = PRODUCTS_FIND_X4.contains(projectName)
        }
        DebugUtil.i(TAG, "isFindX4 : $result")
        return result
    }

    /**
     * 是否是 konka、yala、dodge项目
     */
    @JvmStatic
    fun isFindKYD(): Boolean {
        var result = false
        val projectName = OSDKCompatUtils.getFeatureProperty(PROJECT_PROPERTIES_NAME)
        if (projectName.isEmpty()) {
            val productName = OSDKCompatUtils.getFeatureProperty(PRODUCT_PROPERTIES_NAME)
            if (productName.isNotEmpty()) {
                for (product in PRODUCTS_KYD) {
                    if (productName.contains(product, true)) {
                        result = true
                        break
                    }
                }
            }
        } else {
            result = PRODUCTS_KYD.contains(projectName)
        }
        DebugUtil.i(TAG, "isFindKYD : $result")
        return result
    }

    @JvmStatic
    fun isFindX4AndNotConfidential(): Boolean {
        //return isFindX4()
        return readFeatureByOplusFeature()
    }

    @JvmStatic
    fun readFeatureByOplusFeature(): Boolean {
        val result = OSDKCompatUtils.getFeatureOldCompat(OS12_OPLUSFEATURE_NAME)
        DebugUtil.i(TAG, "readFeatureByOplusFeature result $result")
        return result
    }

    @JvmStatic
    fun isColorOs12(): Boolean {
        DebugUtil.i(TAG, "COLOROS_VERSION : $osVersion")
        return osVersion in COLOROS_12_0_VERSION until COLOROS_13
    }

    @JvmStatic
    fun isColorOs13OrLater(): Boolean {
        return osVersion >= COLOROS_13
    }

    @JvmStatic
    fun isColorOS14OrLater(): Boolean {
        return osVersion > COLOROS_13_2
    }

    @JvmStatic
    fun isColorOS14Later(): Boolean {
        return osVersion > COLOROS_14_0
    }

    @JvmStatic
    fun isColorOS11Point3Later(): Boolean {
        return osVersion > COLOROS_11_3
    }

    @JvmStatic
    fun isColorOS11Point3OrLater(): Boolean {
        return osVersion >= COLOROS_11_3
    }

    @JvmStatic
    fun isColorOS14Point1OrLater(): Boolean {
        return osVersion >= COLOROS_14_1
    }

    /**
     * os15及以上
     */
    @JvmStatic
    fun isColorOS15OrLater(): Boolean {
        return osVersion > COLOROS_14_1
    }

    @JvmStatic
    fun isColorOS16OrLater(): Boolean {
        return osVersion >= COLOROS_16
    }

    /**
     * 2022.3.1 超级录音需求已经解密。
     * 在这之后总是返回 true。
     */
    @JvmStatic
    fun isSuperSoundRecorderEpicEffective(): Boolean {
        return true
    }

    /**
     * 当前仅支持ColorOS16及以上版本
     * 后续如需支持OS15或其它，可在此添加条件
     */
    @JvmStatic
    fun isSupportASRFeature(): Boolean {
        return isColorOS16OrLater()
    }
}