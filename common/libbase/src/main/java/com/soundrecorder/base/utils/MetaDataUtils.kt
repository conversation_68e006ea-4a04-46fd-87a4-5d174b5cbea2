/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MetaDataUtils
 * Description:
 * Version: 1.0
 * Date: 2024/3/5
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/5 1.0 create
 */

package com.soundrecorder.base.utils

import android.content.Context
import android.content.pm.PackageManager

object MetaDataUtils {

    @JvmStatic
    fun getMetaDataBoolean(context: Context, packageName: String, name: String): <PERSON><PERSON><PERSON> {
        return runCatching {
            context.packageManager.getApplicationInfo(
                packageName, PackageManager.GET_META_DATA).metaData?.let {
                return it.getBoolean(name)
            }
        }.onFailure {
            DebugUtil.e("MetaDataUtils", "getMetaDataInt error ${it.message}")
        }.getOrNull() ?: false
    }

    @JvmStatic
    fun getMetaDataString(context: Context, packageName: String, name: String): String {
        return runCatching {
            context.packageManager.getApplicationInfo(
                packageName, PackageManager.GET_META_DATA).metaData?.let {
                return it.getString(name) ?: ""
            }
        }.onFailure {
            DebugUtil.e("MetaDataUtils", "getMetaDataString error ${it.message}")
        }.getOrNull() ?: ""
    }

    @JvmStatic
    fun getMetaDataInt(context: Context, packageName: String, name: String): Int {
        return runCatching {
            context.packageManager.getApplicationInfo(
                packageName, PackageManager.GET_META_DATA).metaData?.let {
                return it.getInt(name, -1)
            }
        }.onFailure {
            DebugUtil.e("MetaDataUtils", "getMetaDataInt error ${it.message}")
        }.getOrNull() ?: -1
    }
}