/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IntentExt
 * Description:
 * Version: 1.0
 * Date: 2023/8/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/31 1.0 create
 */

package com.soundrecorder.base.ext

import android.content.Intent

object IntentExt {

    @JvmStatic
    fun Intent.getStringValue(name: String, defaultValue: String? = null): String? {
        return try {
            getStringExtra(name) ?: defaultValue
        } catch (_: Exception) {
            defaultValue
        }
    }

    @JvmStatic
    fun Intent.getBooleanValue(name: String, defaultValue: Boolean = false): Boolean {
        return try {
            getBooleanExtra(name, defaultValue)
        } catch (_: Exception) {
            defaultValue
        }
    }

    @JvmStatic
    fun Intent.getIntValue(name: String, defaultValue: Int): Int {
        return try {
            getIntExtra(name, defaultValue)
        } catch (_: Exception) {
            defaultValue
        }
    }

    @JvmStatic
    fun Intent.getLongValue(name: String, defaultValue: Long): Long {
        return try {
            getLongExtra(name, defaultValue)
        } catch (_: Exception) {
            defaultValue
        }
    }
}