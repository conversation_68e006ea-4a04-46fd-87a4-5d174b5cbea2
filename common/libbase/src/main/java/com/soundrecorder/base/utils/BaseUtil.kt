/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Build
import android.text.TextUtils
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.oplus.recorderlog.util.CommonFlavor
import java.io.File

object BaseUtil {
    const val TAG = "BaseUtil"
    const val PAGE_SIZE_100 = 100
    const val PAGE_SIZE_500 = 500

    /*使用录音开始时间*/
    @JvmField
    var sStartTime: Long = 0L

    /*使用录音结束时间*/
    @JvmField
    var sEndTime: Long = 0L

    /* 是否启动录音服务 */
    @JvmField
    var sStartRecordingService: Boolean = false

    /* 是否启动录音app */
    @JvmField
    var sStartRecordingActivity: Boolean = false

    /* 启动类型 进入录音app -> 1 启动录制音频 -> 2 默认值为 1 */
    const val VALUE_KEY_ENTRY_TYPE_APP: String = "1"
    const val VALUE_KEY_ENTRY_TYPE_RECORD: String = "2"

    /* 落地页前后台 前台 -> 1 后台 -> 2 */
    const val VALUE_KEY_LANDING_TYPE_FRONT: Int = 1
    const val VALUE_KEY_LANDING_TYPE_BACK: Int = 2

    /* 录音启动类型：（默认）进入app or 启动录制音频 默认为 0*/
    @JvmField
    var sValueEntryType: String = "0"


    const val PACKAGE_INCALLUI = "com.android.incallui"

    private var sIsOplusIncalluiEnabled: Boolean? = null

    @JvmStatic
    val isAndroidJELLY_BEAN_MR1OrLater by lazy {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1
    }

    @JvmStatic
    val isAndroidNOrLater by lazy {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
    }

    @JvmStatic
    val isAndroidN_MR1OrLater by lazy {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1
    }

    @JvmStatic
    val isAndroidOOrLater by lazy {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
    }

    @JvmStatic
    val isAndroidQOrLater by lazy {
        Build.VERSION.SDK_INT > Build.VERSION_CODES.P
    }

    @JvmStatic
    val isAndroidROrLater by lazy {
        Build.VERSION.SDK_INT > Build.VERSION_CODES.Q
    }

    @JvmStatic
    val isAndroidSOrLater by lazy {
        Build.VERSION.SDK_INT > Build.VERSION_CODES.R
    }

    @JvmStatic
    val isAndroidTOrLater by lazy {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
    }

    @JvmStatic
    val isAndroidUOrLater by lazy {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
    }

    @JvmStatic
    val isAndroidVOrLater by lazy {
        Build.VERSION.SDK_INT > Build.VERSION_CODES.UPSIDE_DOWN_CAKE
    }

    @JvmStatic
    val isAndroidQ by lazy {
        Build.VERSION.SDK_INT == Build.VERSION_CODES.Q
    }

    @JvmStatic
    fun getPhoneStorageFile(context: Context?): File? {
        return AddonAdapterCompatUtil.getInternalSdDirectory(context)
    }

    @JvmStatic
    fun getPhoneStorageDir(context: Context?): String? {
        val internalFile: File? = AddonAdapterCompatUtil.getInternalSdDirectory(context)
        return internalFile?.path
    }

    @JvmStatic
    fun getSDCardStorageDir(context: Context?): String {
        return  (AddonAdapterCompatUtil.getExternalSdDirectory(context)?.path)
            ?: (AddonAdapterCompatUtil.getExternalStorageDirectory()?.path) ?: ""
    }

    fun isOplusIncalluiEnabledForExp(context: Context?): Boolean {
        if (sIsOplusIncalluiEnabled == null) {
            sIsOplusIncalluiEnabled = isSystemAppEnabledForExp(context, PACKAGE_INCALLUI)
        }
        return sIsOplusIncalluiEnabled ?: false
    }


    fun isSystemAppEnabledForExp(context: Context?, packageName: String): Boolean {
        if (context == null || TextUtils.isEmpty(packageName)) {
            DebugUtil.w(
                TAG, "isSystemAppEnabledForExp return true "
                        + "for context is null or package name is null!"
            )
            return false
        }
        var appInfo: ApplicationInfo? = null
        var installed = false
        DebugUtil.w(TAG, "isSystemAppEnabledForExp method begin!")
        try {
            appInfo = context.packageManager.getApplicationInfo(
                packageName,
                PackageManager.MATCH_SYSTEM_ONLY
            )
        } catch (e: Exception) {
            DebugUtil.w(TAG, "isSystemAppEnabledForExp : e = " + e.message)
        }
        if (appInfo != null
            && appInfo.enabled
        ) {
            installed = true
        }
        DebugUtil.w(TAG, "isSystemAppEnabledForExp : $installed packageName : $packageName")
        return installed
    }



    @JvmStatic
    fun sendLocalBroadcast(context: Context, intent: Intent) {
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
    }

    @JvmStatic
    fun isOPPO(): Boolean = CommonFlavor.getInstance().isOPPO()

    @JvmStatic
    fun isOnePlus(): Boolean = CommonFlavor.getInstance().isOnePlus()

    @JvmStatic
    fun isRealme(): Boolean = CommonFlavor.getInstance().isRealme()

    /**
     * 是否为一加外销手机
     * @return true：一加外销手机 false：非一加外销
     */
    @JvmStatic
    fun isOnePlusExp(): Boolean {
        return FeatureOption.OPLUS_VERSION_EXP && CommonFlavor.getInstance().isOnePlus()
    }

    /**
     * 是否是其他品牌
     */
    @JvmStatic
    fun isOtherBrand(): Boolean {
        return !isOPPO() && !isOnePlus() && !isRealme()
    }

    /**
     * @param pageNob  0, 1, 2.....
     * @param pageSize default 100
     * @return [pageNob * pageSize, (pageNob + 1) * pageSize)
     */
    @JvmStatic
    fun <T> getPageItems(list: List<T>, pageNob: Int, pageSize: Int): List<T>? {
        var pageSize = pageSize
        if (pageSize <= 0) {
            pageSize = PAGE_SIZE_100
        }
        val size = list.size
        val min = pageNob * pageSize
        if (min >= size) {
            return ArrayList()
        }
        if (size <= pageSize) {
            return list
        }
        var max = (pageNob + 1) * pageSize
        if (max > size) {
            max = size
        }
        val items: MutableList<T> = ArrayList()
        for (i in pageNob * pageSize until max) {
            items.add(list[i])
        }
        return items
    }

    @JvmStatic
    fun isLightOS(): Boolean {
        return FeatureOption.OPLUS_SYSTEM_LIGHT_0S_FUNCTION
    }

    @JvmStatic
    fun isEXP() = FeatureOption.OPLUS_VERSION_EXP

    @JvmStatic
    fun getPackageName(): String = CommonFlavor.getInstance().getPackageName()

    @JvmStatic
    fun getConfigFromSystem(attr: String): String {
        val result = OSDKCompatUtils.getFeatureProperty(attr)
        DebugUtil.i(TAG, "$attr: $result")
        return result
    }

    @JvmStatic
    fun isApkInDebug(context: Context): Boolean {
        return try {
            val info = context.applicationInfo
            info.flags and ApplicationInfo.FLAG_DEBUGGABLE != 0
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 前提：一加渠道包
     * @return true: 显示一加主题，false：显示oppo主题
     */
    @JvmStatic
    fun isKeepOnePlusCompanyTheme(): Boolean {
        if (!isOnePlus()) {
            DebugUtil.w(TAG, "isKeepOnePlusCompanyTheme,current is not oneplus ")
            return false
        }
        return if (isEXP() || Build.VERSION.SDK_INT <= Build.VERSION_CODES.S_V2) {
            // 外销 或者 T版本以下，保持一加主题
            true
        } else {
            //根据项目是否有一加主题feature
            FeatureOption.getHasOnePlusCompanyFeature()
        }
    }

    /**
     * 外销RSA4.0项目
     */
    @JvmStatic
    fun isExpRSA4(): Boolean {
        return CommonFlavor.getInstance().hasExpFeature() && OS12FeatureUtil.isRSA4_0_PET
    }
}