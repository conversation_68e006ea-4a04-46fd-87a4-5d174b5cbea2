/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: FileRenameUtil
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/
package com.soundrecorder.base.utils;

import android.text.TextUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


public class FileRenameUtil {

    private static final String TAG = "FileRenameUtil";

    private static List<String> scanFolderFilePath(String folderPath) {
        ArrayList<String> scanResult = new ArrayList<>();
        if (TextUtils.isEmpty(folderPath)) {
            DebugUtil.i(TAG, "scanFolderFilePath folderPath empty");
            return scanResult;
        }
        File file = new File(folderPath);
        if (file.isDirectory()) {
            String[] filepaths = file.list();
            if ((filepaths != null) && (filepaths.length > 0)) {
                for (String path : filepaths) {
                    DebugUtil.i(TAG, "scanFolderFilePath: for name: " + FileUtils.getDisplayNameByPath(path));
                    File innerFile = new File(path);
                    if (innerFile.isFile()) {
                        scanResult.add(path);
                    } else if (file.isDirectory()) {
                        List<String> inerResult = scanFolderFilePath(path);
                        scanResult.addAll(inerResult);
                    }
                }
            }
        }
        return scanResult;
    }

    public static File create(String path, final String fileDisplayName) {
        if (TextUtils.isEmpty(path) || TextUtils.isEmpty(fileDisplayName)) {
            return null;
        }
        List<String> filePathsUnderFolder = scanFolderFilePath(path);
        String originalData = path + File.separator + fileDisplayName;
        int index = originalData.lastIndexOf(".");
        String postfix = "";
        String prefix = originalData;
        if (index > 0) {
            postfix = originalData.substring(index);
            prefix = originalData.substring(0, index);
        }
        String newPath = null;
        File newFile = null;
        int i = 0;
        do {
            if (i == 0) {
                newPath = prefix + postfix;
            } else {
                newPath = prefix + "_" + i + postfix;
            }
            i++;
            newFile = new File(newPath);
            if (newFile.exists()) {
                continue;
            }
            if (!filePathsUnderFolder.contains(newPath)) {
                break;
            }
        } while (true);
        return newFile;
    }

    public static String rename(File oldFile, List<String> path) {
        String originalData = oldFile.getAbsolutePath();
        int index = originalData.lastIndexOf(".");
        String postfix = "";
        String prefix = originalData;
        if (index > 0) {
            postfix = originalData.substring(index);
            prefix = originalData.substring(0, index);
        }
        int num = 100;
        for (int i = 1; i < num; i++) {
            String renamePath = prefix + "_" + i + postfix;
            File newFile = new File(renamePath);
            if (newFile.exists()) {
                continue;
            }
            if (!path.contains(renamePath) && oldFile.renameTo(newFile)) {
                DebugUtil.d(TAG, "rename, " + oldFile.getName() + "  to  " + newFile.getName());
                return renamePath;
            }
        }
        return null;
    }

    public static boolean renameTo(String srcPath, String destPath) {
        return !TextUtils.isEmpty(srcPath)
                && !TextUtils.isEmpty(destPath)
                && renameTo(new File(srcPath), new File(destPath));
    }

    private static boolean renameTo(File srcFile, File destFile) {
        if ((srcFile == null) || (destFile == null)) {
            return false;
        }
        if (!srcFile.exists()) {
            return false;
        }
        final File parentFile = destFile.getParentFile();
        if (parentFile != null) {
            if (!ensureMakeDirectory(parentFile)) {
                return false;
            }
        }
        return srcFile.renameTo(destFile);
    }


    private static boolean ensureMakeDirectory(File file) {
        boolean success = true;
        if (!file.exists()) {
            success = file.mkdirs();
        } else if (file.isFile()) {
            DebugUtil.v(TAG, "ensureMakeDirectory, directory is a file, we need delete it and recreate the directory:" + file);
            if (!file.delete()) {
                DebugUtil.d(TAG, "ensureMakeDirectory, file delete failed.");
            }
            success = file.mkdirs();
        }
        return success;
    }

}
