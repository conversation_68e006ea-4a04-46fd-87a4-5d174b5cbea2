/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.soundrecorder.base.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.content.pm.PackageManager;
import android.provider.Settings;
import android.text.TextUtils;

import com.oplus.coreapp.appfeature.AppFeatureProviderUtils;
import com.oplus.recorderlog.util.CommonFlavor;
import com.soundrecorder.base.BaseApplication;

public class FeatureOption {
    public static final String TAG = "FeatureOption";

    public static final String NO_DISPALY_RECORD_FEATURE_NAME_R = "com.oplus.soundrecorder.no_display_record";
    public static final String NO_DISPALY_RECORD_FEATURE_NAME_Q = "oppo.phone.nodisplayrecord";
    public static final String CONFLICT_FEATURE_Q = "oppo.multimedia.record.conflict";
    public static final String SETTING_FEATURE_Q = "oppo.newsoundrecorder.setting";
    public static final String SETTING_FEATURE_R = "com.oplus.soundrecorder.setting";
    /** 非一加外销轻量OS */
    public static final String LIGHT_OS_FEATURE_OPLUS = "oppo.sys.light.func";
    /** 一加外销轻量OS */
    public static final String LIGHT_OS_FEATURE_ONEPLUS = "oplus.software.product.oh_light";

    public static final String PROPERTY_LIGHT_OS = "ro.oplus.lightos";
    public static final String FEATURE_TABLET = "oplus.hardware.type.tablet";
    public static final String FEATURE_FOLD = "oplus.hardware.type.fold";
    public static final String SETTING_RECORD_PICTURE_RECOMMENDATION = "pref_setting_record_picture_recommendation";
    public static final String FEATURE_ONE_PLUS_COMPANY_THEME_SUPPORT = "oplus.companyname.not.support";
    public static final String SETTING_CHILDREN_MODE = "children_mode_on";
    /**
     * R之后新的卖场模式feature
     */
    public static final String FEATURE_SALE_MODE_R = "oplus.software.pms_sellmode";

    /**
     * Q之前老版的卖场模式feature
     */
    public static final String FEATURE_SALE_MODE_Q = "oppo.specialversion.exp.sellmode";
    public static final int CHILDREN_MODE_ON = 1;
    public static final int CHILDREN_MODE_OFF = 0;
    /*是否支持蜻蜓设备feature*/
    public static final String OPLUS_SOFTWARE_FOLD_REMAP_DISPLAY_DISABLED = "oplus.software.fold_remap_display_disabled";
    /*泛在系统服务禁用feature*/
    private static final String DISABLE_SYSTEM_SEEDING_FEATURE = "com.oplus.systemui.disable_system_seeding";
    /* 是否支持流体云 */
    private static final String ENABLE_FLUID_ENTRY_FEATURE = "oplus.software.support_fluid_entry";
    private static final String SUPPORT_MINI_CAPSULE_FEATURE = "com.oplus.software.support.mini_capsule";

    /* 泛在系统通知栏卡片去重是否支持 */
    private static final String ENABLE_REPEAT_FLUID_ENTRY_FEATURE_CARD = "oplus.software.fluid_replace_ntf";

    private static final String FEATURE_TASKBAR_ENABLE = "com.android.launcher.TASKBAR_ENABLE";
    /*三方通话录音feature*/
    private static final String FEATURE_FOR_THIRD_RECORD_SUPPORT = "ro.oplus.audio.voip_record_white_app_support";
    /*三方通话录音云控名称*/
    private static final String NAME_THIRD_RECORD_CLOUD_CONTROL = "is_open_third_summary";
    /*三方通话录音云控开关可用值*/
    private static final int THIRD_RECORD_CLOUD_CONTROL_ON = 1;


    /*是否支持悬停模式，需求开关*/
    public static final boolean IS_SUPPORT_HOVER_MODE = true;

    public static boolean OPLUS_VERSION_EXP = false;
    public static boolean OPLUS_RUNTIME_PERMISSION_ALERT_SUPPORT = false;
    public static boolean OPLUS_NEW_SOUND_RECORDER_SETTING = false;
    public static boolean OPLUS_MULTIMEDIA_RECORD_CONFLICT = false;
    public static boolean OPLUS_PHONE_NODISPLAYRECORD = false;
    public static boolean OS12_1_0_FEATURE = false;
    /*是否为平板*/
    public static boolean IS_PAD = false;

    /*this feature is expected to get weather the os is light function os*/
    protected static boolean OPLUS_SYSTEM_LIGHT_0S_FUNCTION = false;
    /*手机支持折叠*/
    private static boolean IS_FOLD_FEATURE = false;
    private static boolean HAS_ONEPLUS_COMPANY_FEATURE = false;
    /*是否支持蜻蜓设备判断*/
    private static boolean HAS_SUPPORT_DRAGONFLY = false;
    private static boolean HAS_SELL_MODE = false;
    private static boolean DISABLE_SYSTEM_SEEDING = false;
    private static boolean ENABLE_FLUID_ENTRY = false;
    private static boolean ENABLE_FLUID_ENTRY_CARD_REPEAT = false;
    private static Boolean HAS_TELE_PHONY_FEATURE = null;
    private static Boolean IS_TASKBAR_ENABLE = null;
    /*是否支持三方应用通话录音*/
    private static Boolean HAS_SUPPORT_THIRD_APP_RECORD = null;

    public static void loadOptions(Context context) {
        boolean contextNull = (context == null);
        DebugUtil.v(TAG, "loadOptions， context null is " + contextNull);
        if (contextNull) {
            return;
        }
        OPLUS_VERSION_EXP = CommonFlavor.getInstance().hasExpFeature();
        OPLUS_NEW_SOUND_RECORDER_SETTING = hasSettingFeature(context);
        OPLUS_MULTIMEDIA_RECORD_CONFLICT = hasConflictFeature(context);
        OPLUS_PHONE_NODISPLAYRECORD = hasNoDisPlayRecorderFeature(context);
        OPLUS_SYSTEM_LIGHT_0S_FUNCTION = hasLightFeature(context);
        IS_PAD = OSDKCompatUtils.getFeatureOldCompat(FEATURE_TABLET, false);
        OS12_1_0_FEATURE = OS12FeatureUtil.isFindX4AndNotConfidential();
        IS_FOLD_FEATURE = OSDKCompatUtils.getFeatureOldCompat(FEATURE_FOLD, false);
        HAS_ONEPLUS_COMPANY_FEATURE = hasOnePlusCompanyFeature(context);
        HAS_SUPPORT_DRAGONFLY = hasSupportDragonfly();
        HAS_SELL_MODE = hasSellMode();
        DISABLE_SYSTEM_SEEDING = hasFeature(context, DISABLE_SYSTEM_SEEDING_FEATURE, DISABLE_SYSTEM_SEEDING_FEATURE);
        ENABLE_FLUID_ENTRY = OSDKCompatUtils.getFeatureOldCompat(ENABLE_FLUID_ENTRY_FEATURE, false)
                || hasFeature(context, SUPPORT_MINI_CAPSULE_FEATURE, SUPPORT_MINI_CAPSULE_FEATURE);
        ENABLE_FLUID_ENTRY_CARD_REPEAT = OSDKCompatUtils.getFeatureOldCompat(ENABLE_REPEAT_FLUID_ENTRY_FEATURE_CARD, false);
        DebugUtil.i(TAG, "OS12_1_0_FEATURE : " + OS12_1_0_FEATURE + ";ENABLE_FLUID_ENTRY_CARD_REPEAT = " + ENABLE_FLUID_ENTRY_CARD_REPEAT
                + ";ENABLE_FLUID_ENTRY = " + ENABLE_FLUID_ENTRY + ";IS_FOLD_FEATURE=" + IS_FOLD_FEATURE);
    }

    public static boolean hasLightFeature(Context context) {
        return hasLightOsFeature(context) || hasLightProperty();
    }

    public static boolean hasLightOsFeature(Context context) {
        try {
            if (isOPExt()) {
                return OSDKCompatUtils.getFeatureConfig(LIGHT_OS_FEATURE_ONEPLUS, false);
            } else {
                return context.getPackageManager().hasSystemFeature(LIGHT_OS_FEATURE_OPLUS);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "hasLightOsFeature, error: " + e);
            return false;
        }
    }

    public static boolean hasLightProperty() {
        if (BaseUtil.isAndroidROrLater()) {
            return "true".equalsIgnoreCase(OSDKCompatUtils.getFeatureProperty(PROPERTY_LIGHT_OS));
        } else {
            return false;
        }
    }

    public static boolean hasSupportDragonfly() {
        boolean isSupportDragonfly = OSDKCompatUtils.getFeatureConfig(OPLUS_SOFTWARE_FOLD_REMAP_DISPLAY_DISABLED, false);
        DebugUtil.i(TAG, "isSupportDragonfly : " + isSupportDragonfly);
        return isSupportDragonfly;
    }

    public static boolean isHasSupportDragonfly() {
        return HAS_SUPPORT_DRAGONFLY;
    }

    public static boolean hasSettingFeature(Context context) {
        return hasFeature(context, SETTING_FEATURE_R, SETTING_FEATURE_Q);
    }

    public static boolean hasConflictFeature(Context context) {
        if (BaseUtil.isAndroidQOrLater()) {
            return false;
        } else {
            return hasFeature(context, CONFLICT_FEATURE_Q, CONFLICT_FEATURE_Q);
        }
    }

    public static boolean hasNoDisPlayRecorderFeature(Context context) {
        return hasFeature(context, NO_DISPALY_RECORD_FEATURE_NAME_R, NO_DISPALY_RECORD_FEATURE_NAME_Q);
    }

    private static boolean hasFeature(Context context, String featureNameR, String featureNameQ) {
        if (context == null) {
            DebugUtil.i(TAG, "hasFeature input context is null");
            return false;
        }
        boolean result = false;
        if (BaseUtil.isAndroidROrLater()) {
            ContentResolver cr = context.getContentResolver();
            result = AppFeatureProviderUtils.isFeatureSupport(cr, featureNameR);
        } else {
            if (TextUtils.isEmpty(featureNameQ) || (featureNameR.equalsIgnoreCase(featureNameQ))) {
                result = context.getPackageManager().hasSystemFeature(featureNameR);
            } else {
                result = context.getPackageManager().hasSystemFeature(featureNameQ);
            }
        }
        DebugUtil.i(TAG, "hasFeature: featureNameR: " + featureNameR + ", featureNameQ: " + featureNameQ + ", result: " + result);
        return result;
    }

    /**
     * 为一加老项目配置的是否显示一加主题feature，新项目无此feature
     *
     * @param context
     * @return true: 需要显示一加主题  false：需要根据android版本以及外销版本判断
     */
    public static boolean hasOnePlusCompanyFeature(Context context) {
        if (context == null) {
            DebugUtil.i(TAG, "isOnePlusCompanyFeature input context is null");
            return false;
        }
        return context.getPackageManager().hasSystemFeature(FEATURE_ONE_PLUS_COMPANY_THEME_SUPPORT);
    }

    /**
     * android平台系统级别的，是判断是否支持蜂窝能力
     * 1.  手机会配置true，
     * 2.  wifi-only 平板 会上 false；
     * 3.  蜂窝版 平板是 true；
     */
    public static boolean checkHasTelePhonyFeature(Context context) {
        if (context == null) {
            DebugUtil.i(TAG, "checkHasTelePhonyFeature input context is null");
            return false;
        }
        if (HAS_TELE_PHONY_FEATURE != null) {
            return HAS_TELE_PHONY_FEATURE;
        }
        try {
            HAS_TELE_PHONY_FEATURE = context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_TELEPHONY);
            return HAS_TELE_PHONY_FEATURE;
        } catch (Exception ignored) {
            DebugUtil.w(TAG, "checkHasTelePhonyFeature error: " + ignored, false);
        }
        return false;
    }

    public static boolean isOPExt() {
        return OPLUS_VERSION_EXP && CommonFlavor.getInstance().isOnePlus();
    }

    public static boolean getSystemLightOs() {
        return OPLUS_SYSTEM_LIGHT_0S_FUNCTION;
    }

    public static boolean getIsFoldFeature() {
        return IS_FOLD_FEATURE;
    }

    public static void setFoldFeature(boolean feature) {
        IS_FOLD_FEATURE = feature;
    }

    public static boolean checkInChildrenMode(Context context) {
        boolean isInChildrenMode = false;
        if (context != null) {
            try {
                isInChildrenMode = Settings.Global.getInt(context.getContentResolver(), SETTING_CHILDREN_MODE, CHILDREN_MODE_OFF) == CHILDREN_MODE_ON;
            } catch (Exception e) {
                DebugUtil.e(TAG, "checkInChildrenMode error", e);
            }
        }
        DebugUtil.i(TAG, "checkInChildrenMode: " + isInChildrenMode);
        return isInChildrenMode;
    }

    public static boolean getHasOnePlusCompanyFeature() {
        return HAS_ONEPLUS_COMPANY_FEATURE;
    }

    /**
     * 判断当前app是否处于卖场模式
     */
    public static boolean hasSellMode() {
        String featureName = BaseUtil.isAndroidROrLater() ? FEATURE_SALE_MODE_R : FEATURE_SALE_MODE_Q;
        return OSDKCompatUtils.getFeatureOldCompat(featureName, false);
    }

    public static boolean isTaskBarEnable(Context context) {
        if (IS_TASKBAR_ENABLE == null) {
            IS_TASKBAR_ENABLE = hasFeature(context, FEATURE_TASKBAR_ENABLE, FEATURE_TASKBAR_ENABLE);
        }
        return IS_TASKBAR_ENABLE;
    }

    public static boolean isHasSellMode() {
        return HAS_SELL_MODE;
    }

    /**
     * 泛在系统服务禁用feature
     */
    public static boolean hasDisableSystemSeeding() {
        return DISABLE_SYSTEM_SEEDING;
    }

    /**
     * 流体云泛在支持feature
     */
    public static boolean isEnableFluidEntry() {
        return ENABLE_FLUID_ENTRY;
    }

    /**
     * 流体云卡片是否支持去重
     */
    public static boolean isEnableCardFluidEntry() {
        return ENABLE_FLUID_ENTRY_CARD_REPEAT;
    }


    /**
     * @return
     */
    public static boolean isHasSupportThirdAppRecord() {
        if (HAS_SUPPORT_THIRD_APP_RECORD == null) {
            /*三方通话录音feature*/
            HAS_SUPPORT_THIRD_APP_RECORD = "true".equals(OSDKCompatUtils.getFeatureProperty(FEATURE_FOR_THIRD_RECORD_SUPPORT));
            if (HAS_SUPPORT_THIRD_APP_RECORD) {
                /*若支持三方通话录音设备，判断第三方应用云控开关，1：功能可用*/
                HAS_SUPPORT_THIRD_APP_RECORD = (THIRD_RECORD_CLOUD_CONTROL_ON
                        == Settings.System.getInt(BaseApplication.getAppContext().getContentResolver(),
                                NAME_THIRD_RECORD_CLOUD_CONTROL, THIRD_RECORD_CLOUD_CONTROL_ON));
            }
        }
        DebugUtil.d(TAG, "isHasSupportThirdAppRecord: " + HAS_SUPPORT_THIRD_APP_RECORD);
        return HAS_SUPPORT_THIRD_APP_RECORD;
    }

    /**
     * 录音摘要功能开关：14.0及以上支持
     * @return
     */
    public static boolean supportRecordSummaryFunction() {
        return OS12FeatureUtil.isColorOS14OrLater();
    }

    public static boolean supportInnerRecordSummaryFunction() {
        return OS12FeatureUtil.isColorOS16OrLater() && isNoteFunctionFinish();
    }

    public static boolean isNoteFunctionFinish() {
        return true;
    }
}
