/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  OpenIdUtils.java
 * * Description: OpenIdUtils.java
 * * Version: 1.0
 * * Date : 2019/9/17
 * * Author: liuyulong
 * * OPLUS Java File Skip Rule:IllegalCatch
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * liuyulong  2019/9/17      1.0    build this module
 ****************************************************************/
package com.soundrecorder.base.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.oplus.stdid.bean.StdIDInfo;
import com.oplus.stdid.sdk.StdIDSDK;

import java.util.UUID;

public enum  OpenIdUtils {

    INSTANCE;

    private String mDUID = "";
    private String mUUID = "";

    public OpenIdUtils init(Context context) {
        StdIDSDK.init(context);
        boolean isSupport = StdIDSDK.isSupported();
        Log.d("OpenIdUtils", "isSupport:" + isSupport);
        try {
            if (isSupport) {
                StdIDInfo stdIDInfo = StdIDSDK.getStdIds(context, StdIDInfo.Type_DUID);
                if (stdIDInfo.DUID != null && !stdIDInfo.DUID.isEmpty()) {
                    mDUID = stdIDInfo.DUID;
                }
            }
        } catch (java.lang.Exception e) {
            Log.e("OpenIdUtils", "e " + e.getMessage());
        }
        mUUID = UUID.randomUUID().toString();
        Log.i("OpenIdUtils", "d is empty:" + !TextUtils.isEmpty(mDUID) + ", u is empty: " + !TextUtils.isEmpty(mUUID));
        return this;
    }

    public String getDUID() {
        return mDUID;
    }

    public String getUUID() { return mUUID; }
}
