/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: SyncTimeUtils
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/
package com.soundrecorder.base.utils;

import java.util.Random;

public class SyncTimeUtils {
    public static final long TIME_1_MIN = 60 * 1000;
    public static final long TIME_5_MIN = 5 * 60 * 1000;
    public static final long TIME_30_MIN = 30 * 60 * 1000;
    public static final long TIME_1_HOUR = 60 * 60 * 1000;
    public static final long TIME_3_HOUR = 3 * 60 * 60 * 1000;
    public static final long TIME_6_HOUR = 6 * 60 * 60 * 1000;

    public static final int MAX_COUNT = 5;

    public static final int FAILED_1_COUNT = 1;
    private static final int FAILED_2_COUNT = 2;
    private static final int FAILED_3_COUNT = 3;
    private static final int FAILED_4_COUNT = 4;
    private static final int FAILED_5_COUNT = 5;

    public static long getTime(int failedCount, long failedTime) {
        return failedTime + getDeltaTime(failedCount);
    }

    private static long getDeltaTime(int failedCount) {
        switch (failedCount) {
            case FAILED_1_COUNT:
                return TIME_5_MIN;
            case FAILED_2_COUNT:
                return TIME_30_MIN;
            case FAILED_3_COUNT:
                return TIME_1_HOUR;
            case FAILED_4_COUNT:
                return TIME_3_HOUR;
            case FAILED_5_COUNT:
                return TIME_6_HOUR;
            default:
                return TIME_6_HOUR;
        }
    }

    public static long getRetryDelayTime(int times) {
        Random random = new Random();
        if (times == 1) {
            return TIME_5_MIN + random.nextInt((int) TIME_1_MIN);
        } else if (times == 2) {
            return TIME_30_MIN + random.nextInt((int) TIME_1_MIN);
        } else if (times == 3) {
            return TIME_1_HOUR + random.nextInt((int) TIME_1_MIN);
        } else if (times == 4) {
            return TIME_3_HOUR + random.nextInt((int) TIME_1_MIN);
        } else if (times == 5) {
            return TIME_6_HOUR + random.nextInt((int) TIME_1_MIN);
        } else {
            return TIME_6_HOUR + random.nextInt((int) TIME_1_MIN);
        }
    }
}
