/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: LanguageUtilTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.base.utils

import android.content.Context
import android.os.Build
import android.text.TextUtils
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.robolectric.annotation.Config
import java.util.Locale

@RunWith(AndroidJUnit4::class)
@PrepareForTest(Locale::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class LanguageUtilTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
    }

    @Test
    fun should_empty_when_getCurrentLanguageFromSystem() {
        val language = LanguageUtil.getCurrentLanguageFromSystem()
        Assert.assertTrue(!TextUtils.isEmpty(language))
    }

    @Test
    fun should_empty_when_isZHCN() {
        val isChina = LanguageUtil.isZHCN()
        Assert.assertFalse(isChina)
    }

    @Test
    fun should_empty_when_isZHTW() {
        val isZHTW = LanguageUtil.isZHTW()
        Assert.assertFalse(isZHTW)
    }

    @Test
    fun should_empty_when_isZHHK() {
        val isZHHK = LanguageUtil.isZHHK()
        Assert.assertFalse(isZHHK)
    }

    @Test
    fun should_empty_when_isUG() {
        val isUG = LanguageUtil.isUG()
        Assert.assertFalse(isUG)
    }

    @Test
    fun should_empty_when_isBO() {
        val isUG = LanguageUtil.isBO()
        Assert.assertFalse(isUG)
    }

    /**
     * 验证getLanguageDisplayName方法的正确性
     */
    @Test
    fun test_get_language_display_name() {
        val languageCode = "zh"
        val displayName = LanguageUtil.getLanguageDisplayName(context, languageCode)
        Assert.assertNotNull(displayName)

        val languageCode2 = "123"
        val displayName2 = LanguageUtil.getLanguageDisplayName(context, languageCode2)
        Assert.assertNull(displayName2)
    }

    /**
     * 验证getRegionSupportedLanguages读取出来的语种code是否都有对应的词条映射
     * 这个测试确保配置文件中的所有语言代码都能通过getLanguageDisplayName方法获取到对应的显示名称
     */
    @Test
    fun test_get_region_supported_languages_have_display_name_mapping_domestic() {
        should_all_region_supported_languages_have_display_name_mapping(false)
    }
    @Test
    fun test_get_region_supported_languages_have_display_name_mapping_export() {
        should_all_region_supported_languages_have_display_name_mapping(true)
    }

    private fun should_all_region_supported_languages_have_display_name_mapping(isEXP: Boolean) {
        // 获取区域支持的语言列表
        val supportedLanguages = LanguageUtil.getRegionSupportedLanguages(isEXP)

        // 验证不为空
        Assert.assertFalse("支持的语言列表不应为空", supportedLanguages.isEmpty())

        // 记录没有映射的语言代码
        val unmappedLanguages = mutableListOf<String>()
        val mappedLanguages = mutableMapOf<String, String>()

        // 遍历每个语言代码，检查是否有对应的显示名称
        supportedLanguages.forEach { languageCode ->
            val displayName = LanguageUtil.getLanguageDisplayName(context, languageCode)
            if (displayName == null) {
                unmappedLanguages.add(languageCode)
            } else {
                mappedLanguages[languageCode] = displayName
            }
        }

        // 断言：所有语言代码都应该有对应的显示名称
        Assert.assertTrue(
            "以下语言代码缺少显示名称映射: $unmappedLanguages\n" +
            "请在LanguageUtil.getLanguageDisplayName方法中添加对应的映射，" +
            "或在strings.xml中添加对应的字符串资源",
            unmappedLanguages.isEmpty()
        )

        // 验证映射的显示名称不为空
        mappedLanguages.forEach { (code, name) ->
            Assert.assertFalse(
                "语言代码 $code 的显示名称不应为空字符串",
                name.isBlank()
            )
        }
    }

    /**
     * 测试getAsrLangMap方法，验证它能正确处理所有支持的语言
     */
    @Test
    fun test_get_asr_lang_map_handle_all_supported_languages_domestic() {
        should_get_asr_lang_map_handle_all_supported_languages(false)
    }
    @Test
    fun test_get_asr_lang_map_handle_all_supported_languages_export() {
        should_get_asr_lang_map_handle_all_supported_languages(true)
    }

    private fun should_get_asr_lang_map_handle_all_supported_languages(isEXP: Boolean) {
        // 获取区域支持的语言列表
        val supportedLanguages = LanguageUtil.getRegionSupportedLanguages(isEXP).toList()

        // 调用getAsrLangMap方法
        val langMap = LanguageUtil.getAsrLangMap(context, supportedLanguages)

        // 验证返回的映射表不为空
        Assert.assertFalse("语言映射表不应为空", langMap.isEmpty())

        // 验证映射表的大小与输入语言列表大小一致
        Assert.assertEquals(
            "映射表大小应与输入语言列表大小一致",
            supportedLanguages.size,
            langMap.size
        )

        // 验证每个语言代码都在映射表中
        supportedLanguages.forEach { languageCode ->
            Assert.assertTrue(
                "语言代码 $languageCode 应该在映射表中",
                langMap.containsKey(languageCode)
            )

            val displayName = langMap[languageCode]
            Assert.assertNotNull(
                "语言代码 $languageCode 的显示名称不应为null",
                displayName
            )

            Assert.assertFalse(
                "语言代码 $languageCode 的显示名称不应为空字符串",
                displayName!!.isBlank()
            )
        }

        println("=== getAsrLangMap测试结果 ===")
        println("输入语言数量: ${supportedLanguages.size}")
        println("输出映射数量: ${langMap.size}")
        langMap.forEach { (code, name) ->
            println("  $code -> $name")
        }
    }

    /**
     * 测试getLanguageEnglishName方法，验证能正确返回语言的英文名称
     */
    @Test
    fun should_get_language_english_name_correctly() {
        // 测试已知的语言代码
        val testCases = mapOf(
            "zh" to "Chinese (Simplified)",
            "zh-TW" to "Chinese (Traditional)",
            "zh-HK" to "Chinese (Traditional)",
            "en" to "English",
            "hi" to "Hindi",
            "es" to "Spanish (Spain)",
            "es-MX" to "Spanish (Mexico)",
            "it" to "Italian",
            "id" to "Indonesian",
            "th" to "Thai",
            "ar" to "Arabic",
            "vi" to "Vietnamese",
            "ms" to "Malay",
            "pt-BR" to "Portuguese (Brazil)",
            "ru" to "Russian",
            "fil" to "Filipino",
            "ja" to "Japanese",
            "fr" to "French",
            "tr" to "Turkish",
            "pl" to "Polish"
        )

        println("=== getLanguageEnglishName测试结果 ===")
        testCases.forEach { (code, expectedName) ->
            val actualName = LanguageUtil.getLanguageEnglishName(code)
            println("  $code -> $actualName")
            Assert.assertEquals(
                "语言代码 $code 的英文名称应为 $expectedName",
                expectedName,
                actualName
            )
        }

        // 测试不支持的语言代码
        val unknownCode = "unknown"
        val unknownResult = LanguageUtil.getLanguageEnglishName(unknownCode)
        Assert.assertEquals(
            "不支持的语言代码应返回原始代码",
            unknownCode,
            unknownResult
        )
        println("  $unknownCode -> $unknownResult (不支持的语言代码)")
    }

    /**
     * 测试sortLanguageList方法，验证语言列表排序功能
     */
    @Test
    fun should_sort_language_list_correctly() {
        // 测试包含中文和英文的完整列表
        val inputList = listOf("fr", "zh", "ja", "en", "es", "zh-TW", "it", "ar", "zh-HK")
        val sortedList = LanguageUtil.sortLanguageList(inputList)

        println("=== sortLanguageList测试结果 ===")
        println("输入列表: $inputList")
        println("排序后列表: $sortedList")

        // 验证中文简体排在第一位
        Assert.assertEquals("中文简体应排在第一位", "zh", sortedList[0])

        // 验证英文排在第二位
        Assert.assertEquals("英文应排在第二位", "en", sortedList[1])

        // 验证中文繁体（台湾）排在第三位
        Assert.assertEquals("中文繁体（台湾）应排在第三位", "zh-TW", sortedList[2])

        // 验证中文繁体（香港）排在第四位
        Assert.assertEquals("中文繁体（香港）应排在第四位", "zh-HK", sortedList[3])

        // 验证剩余语言按英文名称排序（Arabic, French, Italian, Japanese, Spanish）
        val remainingLanguages = sortedList.subList(4, sortedList.size)
        val expectedRemaining = listOf("ar", "fr", "it", "ja", "es") // 按英文名称A-Z排序

        println("剩余语言排序:")
        remainingLanguages.forEachIndexed { index, code ->
            val englishName = LanguageUtil.getLanguageEnglishName(code)
            println("  ${index + 5}. $code -> $englishName")
        }

        Assert.assertEquals("剩余语言应按英文名称A-Z排序", expectedRemaining, remainingLanguages)
    }

    /**
     * 测试sortLanguageList方法的边界情况
     */
    @Test
    fun should_handle_edge_cases_in_sort_language_list() {
        // 测试空列表
        val emptyList = emptyList<String>()
        val sortedEmpty = LanguageUtil.sortLanguageList(emptyList)
        Assert.assertTrue("空列表应返回空列表", sortedEmpty.isEmpty())

        // 测试只有一个元素的列表
        val singleList = listOf("fr")
        val sortedSingle = LanguageUtil.sortLanguageList(singleList)
        Assert.assertEquals("单元素列表应返回相同列表", singleList, sortedSingle)

        // 测试只有中文的列表
        val chineseOnlyList = listOf("zh-TW", "zh", "zh-HK")
        val sortedChinese = LanguageUtil.sortLanguageList(chineseOnlyList)
        val expectedChinese = listOf("zh", "zh-TW", "zh-HK")
        Assert.assertEquals("只有中文的列表应按简体、繁体台湾、繁体香港排序", expectedChinese, sortedChinese)

        // 测试没有中文和英文的列表
        val noChineseEnglishList = listOf("ja", "fr", "es", "it")
        val sortedNoChineseEnglish = LanguageUtil.sortLanguageList(noChineseEnglishList)
        val expectedNoChineseEnglish = listOf("fr", "it", "ja", "es") // French, Italian, Japanese, Spanish
        Assert.assertEquals("没有中英文的列表应按英文名称A-Z排序", expectedNoChineseEnglish, sortedNoChineseEnglish)

        println("=== 边界情况测试结果 ===")
        println("空列表: $emptyList -> $sortedEmpty")
        println("单元素: $singleList -> $sortedSingle")
        println("只有中文: $chineseOnlyList -> $sortedChinese")
        println("无中英文: $noChineseEnglishList -> $sortedNoChineseEnglish")
    }
}