/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ScreenUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class ScreenUtilTest {

    private var mContext: Context? = null
    private var mMockedBaseApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockedBaseApplication?.`when`<Any> { BaseApplication.getAppContext() }
            ?.thenReturn(mContext)
    }

    @After
    fun tearDown() {
        if (mMockedBaseApplication != null) {
            mMockedBaseApplication?.close()
            mMockedBaseApplication = null
        }
        mContext = null
    }

    @Test
    fun should_correct_when_checkInChildrenMode() {
        Assert.assertFalse(FeatureOption.checkInChildrenMode(mContext))
        Assert.assertEquals(ScreenUtil.screenHeight, mContext?.let { ScreenUtil.getRealHeight(it) })
    }

    @Test
    fun should_correct_when_getStatusBarHeight() {
        val height: Int = mContext?.let { ScreenUtil.getStatusBarHeight(it) } ?: 0
        Assert.assertEquals(height.toLong(), 24)
    }

    @Test
    fun should_correct_when_getRealScreenWidthContainSystemBars() {
        Mockito.mockStatic(BaseUtil::class.java)
        Mockito.`when`(BaseUtil.isAndroidROrLater).thenReturn(true)
        Mockito.`when`(BaseApplication.getAppContext()).thenReturn(mContext)
        ScreenUtil.getRealScreenWidthContainSystemBars()
    }

    @Test
    fun should_when_getWindowType() {
        val configuration = Configuration()
        configuration.screenWidthDp = 560
        var windowType = ScreenUtil.getWindowType(configuration)
        Assert.assertEquals(WindowType.SMALL, windowType)

        configuration.screenWidthDp = 640
        windowType = ScreenUtil.getWindowType(configuration)
        Assert.assertEquals(WindowType.MIDDLE, windowType)

        configuration.screenWidthDp = 900
        windowType = ScreenUtil.getWindowType(configuration)
        Assert.assertEquals(WindowType.LARGE, windowType)
    }

    @Test
    fun should_when_isSmallScreen() {
        val mockContext = Mockito.mock(Context::class.java)
        val resource = Mockito.mock(Resources::class.java)
        Mockito.`when`(mockContext.resources).thenReturn(resource)
        Mockito.`when`(resource.configuration)
            .thenReturn(Configuration().apply { screenWidthDp = 560 })
        Assert.assertTrue(ScreenUtil.isSmallScreen(mockContext))

        Mockito.`when`(resource.configuration)
            .thenReturn(Configuration().apply { screenWidthDp = 700 })
        Assert.assertFalse(ScreenUtil.isSmallScreen(mockContext))
    }
}