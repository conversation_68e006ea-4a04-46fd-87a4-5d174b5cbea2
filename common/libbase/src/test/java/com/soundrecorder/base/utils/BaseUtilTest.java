package com.soundrecorder.base.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.shadows.ShadowFeatureOption;
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.io.File;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S,
        shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class BaseUtilTest {

    private static final int INT_ZERO = 0;
    private static final int INT_ONE = 1;
    private static final int INT_TWO = 2;
    private static final int INT_THREE = 3;
    private static final int INT_FOUR = 4;
    private static final int INT_TEN = 10;
    private static final String TEST = "test";
    private static final String EMPTY_STRING = "";
    private static final String STRING_ONE_TWO_THREE = "123";
    private static final int INT_ONE_TWO_THREE = 123;
    private static final String LONG_STRING = "12345678910";
    private static final Long LONG_NUM = 12345678910L;
    private static final Long LONG_ONE = 1L;
    private Context mContext;
    private MockedStatic<AddonAdapterCompatUtil> mMockStaticAddonUtil;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = ApplicationProvider.getApplicationContext();
        mMockStaticAddonUtil = Mockito.mockStatic(AddonAdapterCompatUtil.class);
    }

    @After
    public void tearDown() {
        mContext = null;
        mMockStaticAddonUtil.close();
        mMockStaticAddonUtil = null;
    }

    @Test
    public void should_initValue_when_init() {
        Assert.assertTrue(BaseUtil.isAndroidQOrLater());
        Assert.assertTrue(BaseUtil.isAndroidROrLater());
        Assert.assertTrue(BaseUtil.isAndroidSOrLater());
        Assert.assertFalse(BaseUtil.isAndroidQ());
    }

    @Test
    public void should_returnList_when_getPageItems() {
        List mockList = mock(List.class);
        List pageItems0 = BaseUtil.getPageItems(mockList, INT_ZERO, INT_ZERO);
        assertTrue(pageItems0.isEmpty());

        doReturn(INT_ZERO, INT_TEN).when(mockList).size();
        List pageItems1 = BaseUtil.getPageItems(mockList, INT_FOUR, INT_FOUR);
        assertTrue(pageItems1.isEmpty());
        pageItems1 = BaseUtil.getPageItems(mockList, INT_FOUR, INT_FOUR);
        assertTrue(pageItems1.isEmpty());
        List pageItems2 = BaseUtil.getPageItems(mockList, INT_FOUR, INT_TWO);
        assertEquals(INT_TWO, pageItems2.size());

        doReturn(INT_TWO).when(mockList).size();
        List pageItems3 = BaseUtil.getPageItems(mockList, INT_FOUR, INT_TWO);
        assertEquals(INT_ZERO, pageItems3.size());
    }

    @Test
    @Ignore
    public void check_getConfigFromSystem() {
        Assert.assertEquals(BaseUtil.getConfigFromSystem(""), "");
    }

    @Test
    public void isApkInDebug(){
        Assert.assertTrue(BaseUtil.isApkInDebug(BaseApplication.getAppContext()));
    }

    @Test
    public void isExpRSA4(){
        Assert.assertFalse(BaseUtil.isExpRSA4());
    }

    @Test
    public void should_correct_when_getPhoneStorageFile() {
        mMockStaticAddonUtil.when(() -> AddonAdapterCompatUtil.getInternalSdDirectory(mContext)).thenReturn(null, new File("11"));
        Assert.assertNull(BaseUtil.getPhoneStorageFile(mContext));
        Assert.assertNotNull(BaseUtil.getPhoneStorageFile(mContext));
    }

    @Test
    public void should_correct_when_getPhoneStorageDir() {
        mMockStaticAddonUtil.when(() -> AddonAdapterCompatUtil.getInternalSdDirectory(mContext)).thenReturn(null, new File("11"));
        Assert.assertNull(BaseUtil.getPhoneStorageDir(mContext));
        Assert.assertNotNull(BaseUtil.getPhoneStorageDir(mContext));
    }

    @Test
    public void should_correct_when_getSDCardStorageDir() {
        mMockStaticAddonUtil.when(() -> AddonAdapterCompatUtil.getExternalSdDirectory(mContext)).thenReturn(null, new File("11"), null);
        mMockStaticAddonUtil.when(() -> AddonAdapterCompatUtil.getExternalStorageDirectory()).thenReturn(null, new File("11"));
        Assert.assertEquals("", BaseUtil.getSDCardStorageDir(mContext));
        Assert.assertNotEquals("", BaseUtil.getSDCardStorageDir(mContext));
        Assert.assertNotEquals("", BaseUtil.getSDCardStorageDir(mContext));
    }
}
