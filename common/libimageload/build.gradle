apply from:"../../common_build.gradle"

android {
    namespace "com.soundrecorder.imageload"
}

dependencies {
    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx
    implementation(libs.coil) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation(libs.okio)
    /*上面移除了OK，由于引入云同步ck，网络库使用内部taphttp，内含ok源码*/
    implementation(libs.heytap.nearx.http) {
        // 需移除 clientId，否则外销会有安规问题
        exclude group: 'com.heytap.baselib', module: 'clientId'
    }
    implementation project(':common:libbase')
    implementation project(':common:modulerouter')
}