/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveSampleWorker.java
 Description:
 Version: 1.0
 Date: 2020/1/7
 Author: liuyulong
 -----------Revision History-----------
 <author> <date> <version> <desc>
 liuyulong 2020/1/7 1.0 create
 */

package com.soundrecorder.recorderservice.controller.worker;

import androidx.annotation.NonNull;

import java.lang.ref.WeakReference;
import java.util.TimerTask;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.recorderservice.controller.RecorderController;
import com.soundrecorder.recorderservice.controller.model.RecordAmplitudeModel;
import com.soundrecorder.wavemark.wave.WaveViewUtil;
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource;

public class WaveSampleWorker extends TimerTask {

    private static final String TAG = "WaveSampleWorker";

    private WeakReference<RecorderController> mRefRecorderController;

    private volatile boolean mIsCancelled = false;

    public WaveSampleWorker(@NonNull RecorderController recorderController) {
        this.mRefRecorderController = new WeakReference<>(recorderController);
    }

    @Override
    public boolean cancel() {
        mIsCancelled = true;
        return super.cancel();
    }

    @Override
    public void run() {
        synchronized (this) {
            if (mIsCancelled) {
                DebugUtil.e(TAG, "wave sample worker is cancelled(check point 1)");
                return;
            }

            if (mRefRecorderController.get() == null) {
                DebugUtil.e(TAG, "record controller is null");
                return;
            }

            if (mRefRecorderController.get().getControllerObserver() == null) {
                DebugUtil.e(TAG, "controller observer is null");
                return;
            }

            RecordAmplitudeModel recordAmplitudeModel = mRefRecorderController.get().getRecorderAmplitudeModel();

            MaxAmplitudeSource maxAmplitudeSource = mRefRecorderController.get().getControllerObserver().getAmplitudeCallback();
            if (maxAmplitudeSource == null) {
                DebugUtil.e(TAG, "maxAmplitudeSource is null");
                return;
            }

            if ((maxAmplitudeSource.getTime() == recordAmplitudeModel.getCurrentTimeMillis())) {
                DebugUtil.e(TAG, "getCurrentTimeMillis is the same as latest time");
                return;
            }

            int amplitude = maxAmplitudeSource.getMaxAmplitude();
            long currentTimeMillis = maxAmplitudeSource.getTime();

            if (mIsCancelled) {
                DebugUtil.e(TAG, "wave sample worker is cancelled(check point 2)");
                return;
            }

            /*
             * 其他线程中可能有对[RecordAmplitudeModel.mAmplitudeList]的修改行为，如[RecorderWaveRecyclerView.fixAmplitude]。
             * 后续需要从整体架构逻辑上考虑优化。
             */
            synchronized (recordAmplitudeModel.getAmplitudeList()) {
                recordAmplitudeModel.setLatestAmplitude(amplitude);
                recordAmplitudeModel.addOneAmplitudeCache(amplitude);
                recordAmplitudeModel.setCurrentTimeMillis(currentTimeMillis);

                /*care this!, must fix amplitude size before fix time*/
                fixWaveAmplitudeSize(recordAmplitudeModel);
                fixAmplitudeCurrentTimeMillis(recordAmplitudeModel);
            }

            RecorderController recorderController = mRefRecorderController.get();
            if (recorderController != null) {
                recorderController.setRecorderAmplitudeModel(recordAmplitudeModel);
                if (recorderController.getWaveObserver() != null) {
                    recorderController.getWaveObserver().onUpdateWave();
                }
            }
        }
    }

    private void fixWaveAmplitudeSize(@NonNull RecordAmplitudeModel model) {
        //fix wave line
        long time = model.getCurrentTimeMillis();
        if ((time - model.getAmplitudeListSize() * model.getOneWaveLineTime())
                >= (model.getOneWaveLineTime() - model.getMsPerPx())) {
            model.getAmplitudeList().add((int) (Math.random() * model.getLatestAmplitude()));
            DebugUtil.d(TAG, "fix amplitude wave line,case 1 with less amplitude");
        } else if ((model.getAmplitudeListSize() * model.getOneWaveLineTime() - time)
                >= model.getMsPerPx()) {
            DebugUtil.d(TAG, "fix amplitude wave line,case 2 with more amplitude");
            try {
                if (model.getAmplitudeListSize() >= 1) {
                    model.getAmplitudeList().remove(model.getAmplitudeListSize() - 1);
                }
            } catch (Exception ignored) {
                DebugUtil.e(TAG, "IndexOutOfBoundsException : " + ignored.getMessage());
            }
        }
    }

    private void fixAmplitudeCurrentTimeMillis(@NonNull RecordAmplitudeModel model) {
        if (model.getCurrentTimeMillis() % WaveViewUtil.ONE_WAVE_VIEW_DURATION == 0) {
            //if the current time is a multiple of ONE_WAVE_VIEW_DURATION, the interface will splash.
            //I do not know why,here add one to mCurrentTimeMillis.
            model.setCurrentTimeMillis(model.getCurrentTimeMillis() + 1);
        }
    }

    public synchronized void release() {
        if (mRefRecorderController != null) {
            mRefRecorderController = null;
        }
    }
}
