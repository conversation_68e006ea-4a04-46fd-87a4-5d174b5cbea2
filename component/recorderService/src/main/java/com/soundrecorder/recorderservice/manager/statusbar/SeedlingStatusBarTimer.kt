/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingStatusBarTimer
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.os.CountDownTimer

class SeedlingStatusBarTimer(var timerCallback: () -> Unit) {

    companion object {
        private const val TIMER_DURATION = 1000L
        private const val TIMER_INTERVAL = 100L
    }
    private var timer: CountDownTimer? = null

    fun startTimer() {
        cancelTimer()

        timer = object : CountDownTimer(TIMER_DURATION, TIMER_INTERVAL) {
            override fun onTick(p0: Long) {
                //do nothing
            }

            override fun onFinish() {
                timerCallback.invoke()
            }
        }
        timer?.start()
    }

    fun cancelTimer() {
        timer?.cancel()
        timer = null
    }
}