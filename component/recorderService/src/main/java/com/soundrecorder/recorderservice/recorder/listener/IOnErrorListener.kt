/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: IOnErrorListener
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.recorder.listener

import android.media.MediaRecorder
import com.oplus.media.OplusRecorder
import com.oppo.media.OppoRecorder

abstract class IOnErrorListener : OppoRecorder.OnErrorListener, OplusRecorder.OnErrorListener, MediaRecorder.OnErrorListener {
    override fun onError(mr: OplusRecorder?, what: Int, extra: Int) {
        onError(what)
    }

    override fun onError(mr: OppoRecorder?, what: Int, extra: Int) {
        onError(what)
    }

    override fun onError(mr: MediaRecorder?, what: Int, extra: Int) {
        onError(what)
    }

    abstract fun onError(what: Int)
}