/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: MuteModeOperator
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.content.Context
import android.media.AudioManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.view.DetachAudioFocusChangeListener
import com.soundrecorder.recorderservice.manager.listener.MuteModeChangeListener

class MuteModeOperator(
    context: Context,
    var muteConfig: MuteConfig,
    var mAudioFocusChangeListener: AudioFocuseChangeListener? = null
) :
    MuteModeChangeListener {

    companion object {
        const val TAG = "MuteModeOperator"
    }

    private var mAudioManager: AudioManager? = null

    private var mUserChangeRingMode = false
    private var mMuteSetted = false

    init {
        mAudioManager = context.getSystemService(Context.AUDIO_SERVICE) as? AudioManager
    }

    private val mAudioFocusListener = DetachAudioFocusChangeListener.wrap { focusChange ->
        DebugUtil.d(TAG, "AudioFocus: changed $focusChange")
        mAudioFocusChangeListener?.onFocuseChanged(focusChange)
    }


    fun setMute(mute: Boolean) {
        DebugUtil.d(
            TAG,
            "setMute =$mute, muteConfig = $muteConfig, mUserChangeRingMode =$mUserChangeRingMode, mMuteSetted = $mMuteSetted"
        )
        if (mAudioManager == null) {
            DebugUtil.e(TAG, "setMute error, mAudioManager is null")
            return
        }
        if (!muteConfig.muteEnable) {
            return
        }
        if (muteConfig.needEnableAudioFocus) {
            if (mute) {
                mAudioManager?.requestAudioFocus(
                    mAudioFocusListener, AudioManager.STREAM_RING,
                    AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
                )
            } else {
                mAudioManager?.abandonAudioFocus(mAudioFocusListener)
            }
        }
        val current = mAudioManager?.ringerMode
        DebugUtil.d(
            TAG,
            "setMute current ringer mode =$current, mute == $mute, mUserChangeRingMode == $mUserChangeRingMode"
        )
        if (mute && current == AudioManager.RINGER_MODE_NORMAL && !mUserChangeRingMode) {
            mMuteSetted = true
            mAudioManager?.ringerMode = AudioManager.RINGER_MODE_VIBRATE
            BaseApplication.sNeedToNormalRingMode = true
        } else if (!mute) {
            if (current == AudioManager.RINGER_MODE_VIBRATE && mMuteSetted && !mUserChangeRingMode) {
                mMuteSetted = false
                mAudioManager?.ringerMode = AudioManager.RINGER_MODE_NORMAL
            }
            mMuteSetted = false
            BaseApplication.sNeedToNormalRingMode = false
        }
    }

    override fun onRingModeChanged() {
        val currentRingerMode = mAudioManager?.ringerMode
        DebugUtil.i(
            TAG,
            "onRingModeChanged currentRingerMode = $currentRingerMode, mMuteSetted = $mMuteSetted"
        )
        when (currentRingerMode) {
            AudioManager.RINGER_MODE_VIBRATE -> {
                /*When receiving vibrate broadcast, no mode has been set,
                         it is regarded as user setting.
                         Otherwise, it will not be handled.*/
                if (!mMuteSetted) {
                    mUserChangeRingMode = true
                    BaseApplication.sNeedToNormalRingMode = false
                }
            }

            AudioManager.RINGER_MODE_NORMAL -> {
                /*Both the app and the user may change this setting.
                      Receive normal broadcast, mode has been set mute, as user settings.*/
                if (mMuteSetted) {
                    mUserChangeRingMode = true
                    BaseApplication.sNeedToNormalRingMode = false
                }
            }

            AudioManager.RINGER_MODE_SILENT -> {
                //Receive silent broadcast, as user settings.
                mUserChangeRingMode = true
                BaseApplication.sNeedToNormalRingMode = false
            }
        }
    }


    data class MuteConfig(var muteEnable: Boolean = true, var needEnableAudioFocus: Boolean = false)


    interface AudioFocuseChangeListener {

        fun onFocuseChanged(audioFoucesChanged: Int)
    }
}