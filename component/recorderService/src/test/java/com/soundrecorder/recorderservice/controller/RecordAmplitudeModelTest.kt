/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordAmplitudeModelTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.controller

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.recorderservice.controller.model.RecordAmplitudeModel
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class RecordAmplitudeModelTest {

    @Test
    fun should_success_when_addAmplitude() {
        val amplitudeModel = RecordAmplitudeModel()
        amplitudeModel.addOneAmplitudeCache(1000)
        Assert.assertEquals(1000, amplitudeModel.latestAmplitude)
        Assert.assertNotEquals(0, amplitudeModel.amplitudeListSize)
    }

    @Test
    fun should_success_when_setCurrentTime() {
        val amplitudeModel = RecordAmplitudeModel()
        amplitudeModel.currentTimeMillis = 2000L
        Assert.assertEquals(2000L, amplitudeModel.currentTimeMillis)
    }

    @Test
    fun should_success_when_getWaveInfo() {
        val amplitudeModel = RecordAmplitudeModel()
        Assert.assertNotEquals(0, amplitudeModel.oneWaveLineTime)
        Assert.assertNotEquals(0, amplitudeModel.msPerPx)
    }
}