/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealTimeAsrParser
 * Description:
 * Version: 1.0
 * Date: 2025/4/16
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/4/16 1.0 create
 */

package com.soundrecorder.translate.asr.realtime

import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import com.oplus.ai.asrkit.data.response.AsrAudioAck
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.common.realtimeasr.AsrStatusConst
import com.soundrecorder.translate.asr.bean.AsrResult
import com.soundrecorder.translate.asr.listener.IRealTimeAsrListener

class RealTimeAsrParser {
    companion object {
        private const val TAG = "RealTimeAsrParser"

        // 配置文件路径
        private const val LANGUAGE_CONFIG_FILE = "realtime_asr_language_config.json"

        // JSON字段名
        private const val KEY_SUPPORT_LANGUAGES = "supportLanguages"
        private const val FIELD_DOMESTIC = "domestic"
        private const val FIELD_EXPORT = "export"

        /*中间态，连续*/
        const val ASR_RESULT_TYPE_INTERMEDIATE = "INTERMEDIATE"
        private const val ASR_RESULT_TYPE_FINAL = "FINAL"

        /*最终的结果句子*/
        const val ASR_RESULT_TYPE_VAD_FINAL = "VAD_FINAL"

        /*服务端主动断开链路标志*/
        const val BIZ_TYPE_SERVER_END_ASK = "SERVER_END_ASK"
        const val BIZ_TYPE_END_ASK = "END_ASK"
        const val BIZ_TYPE_START_ASK = "START_ASK"
        const val BIZ_TYPE_AUDIO = "AUDIO"

        /*数据未上传完成*/
        const val ERROR_DATA_UPLOAD = 3000703
        /*找不到channelId*/
        const val ERROR_NOT_FOUND_CHANNEL_ID = 1002

        /*未知错误*/
        const val ERROR_UNKNOWN = 3009999

        /*响应解密时发生异常*/
        const val ERROR_DECRYPT_ACK = 100100

        /*等待FINAL超时*/
        const val ERROR_WAIT_FINAL_TIMEOUT = 100101

        /*语音识别处理异常*/
        const val ERROR_PROCESS = 100102

        /*网络未初始化*/
        const val ERROR_NO_INIT = 100103

        /*网络返回异常*/
        const val ERROR_NET_FAILURE = 100104

        /*网络未连接*/
        const val ERROR_NET_DISCONNECT = AsrStatusConst.ERROR_NET_DISCONNECT

        /*网络已连接*/
        const val STATUS_NET_CONNECTED = 100201

        const val STATUS_RESULT_DATA = 100202

        /*initAsr成功*/
        const val STATUS_INIT_SUCCESS = 100203

        /*initAsr失败*/
        const val STATUS_INIT_ERROR = 100204

        /*接收到ASR END回调*/
        const val STATUS_ASR_COMPLETE_SUCCESS = 100205

        /*发送数据，channel找不到*/
        const val AUDIO_ERROR_CODE_CHANNEL_NOT_FOUND = 1002
    }

    // 存储服务器返回的speakerId到本地ID的映射
    private val speakerIdMap = mutableMapOf<String, Int>()
    private var nextSpeakerId = 1

    fun onAsr(channelId: String?, audioAck: AsrAudioAck, listener: IRealTimeAsrListener?) {
        when (audioAck.type) {
            ASR_RESULT_TYPE_INTERMEDIATE, ASR_RESULT_TYPE_VAD_FINAL -> {
                if (!audioAck.text.isNullOrBlank()) {
                    listener?.onAsrResult(
                        channelId = channelId,
                        AsrResult(
                            msgId = audioAck.msgId,
                            type = audioAck.type!!,
                            startOffset = audioAck.startOffset ?: 0,
                            endOffset = audioAck.endOffset ?: 0,
                            text = audioAck.text!!,
                            speakId = extractSpeakerId(audioAck.speakerId)
                        )
                    )
                }
            }
        }
    }

    fun onTranslationCfgSuccess(channelId: String?, data: String?, listener: IRealTimeAsrListener?, isInnerInInvoke: Boolean) {
        val parseData = extractSupportLanguage(data)
        listener?.onTranslationCfgSuccess(channelId, parseData, isInnerInInvoke)
    }

    /**
     * 提取讲话人ID
     * 1. 如果Input为null，则表示不支持讲话人功能，返回值为null
     * 2. 对于任何非null且非Unknown的字符串，根据首次出现顺序分配ID
     * 3. 如果是 Unknown，则表示正在处理，返回0
     */
    private fun extractSpeakerId(input: String?): Int? {
        return when (input) {
            null -> null  // 不支持讲话人功能
            "Unknown" -> 0  // 正在处理
            else -> speakerIdMap.getOrPut(input) { nextSpeakerId++ }  // 为每个唯一ID按出现顺序分配数字
        }
    }

    /**
     * 重置讲话人ID映射
     * 在释放资源或重新开始时调用
     */
    fun resetSpeakerIdMapping() {
        speakerIdMap.clear()
        nextSpeakerId = 1
    }

    /**
     * 解析支持的语言，并根据配置文件过滤
     */
    fun extractSupportLanguage(data: String?): Map<String, String> {
        try {
            if (data.isNullOrEmpty()) return emptyMap()

            // 解析ASR返回的支持语言
            val jsonObject = Gson().fromJson(data, JsonObject::class.java)
            val supportLanguagesObj = jsonObject.getAsJsonObject(KEY_SUPPORT_LANGUAGES)

            // 安全类型转换
            val type = object : TypeToken<Map<String, String>>() {}.type
            val asrSupportedLanguages: Map<String, String> = Gson().fromJson(supportLanguagesObj, type) ?: emptyMap()

            // 获取配置文件中的区域语言限制
            val regionLanguages = LanguageUtil.getRegionSupportedLanguages()

            // 如果配置为空，直接返回空Map
            if (regionLanguages.isEmpty()) {
                return emptyMap()
            }

            // 计算交集
            return asrSupportedLanguages.filterKeys { languageCode ->
                regionLanguages.contains(languageCode)
            }
        } catch (e: JsonSyntaxException) {
            return emptyMap()
        }
    }
}
