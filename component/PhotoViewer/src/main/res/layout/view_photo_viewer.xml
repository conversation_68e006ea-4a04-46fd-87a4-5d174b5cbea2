<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/backgroundView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:alpha="0"
        android:background="@android:color/black"
        tools:alpha="1" />

    <FrameLayout
        android:id="@+id/dismissContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/transitionImageContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:ignore="UselessParent">

            <ImageView
                android:id="@+id/transitionImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="left|top"
                tools:ignore="ContentDescription" />

        </FrameLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/imagesPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

    </FrameLayout>

    <ImageView
        android:id="@+id/centerTargetView"
        android:layout_width="1px"
        android:layout_height="1px"
        android:layout_gravity="center"
        android:importantForAccessibility="no"
        tools:ignore="ContentDescription"/>

</FrameLayout>