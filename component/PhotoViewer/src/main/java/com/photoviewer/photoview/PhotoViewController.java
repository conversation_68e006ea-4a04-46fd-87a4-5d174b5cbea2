package com.photoviewer.photoview;

import android.content.Context;
import android.graphics.Matrix;
import android.graphics.Matrix.ScaleToFit;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.Interpolator;
import android.widget.ImageView;
import android.widget.ImageView.ScaleType;
import android.widget.OverScroller;

import androidx.annotation.NonNull;
import androidx.transition.AutoTransition;
import androidx.transition.ChangeImageTransform;
import androidx.transition.TransitionManager;
import androidx.transition.TransitionSet;

import kotlin.Unit;

class PhotoViewController implements View.OnTouchListener, View.OnLayoutChangeListener {

    private static final float DEFAULT_MAX_SCALE = 3.0f;
    private static final float DEFAULT_MID_SCALE = 1.75f;
    private static final float DEFAULT_MIN_SCALE = 1.0f;
    private static final int DEFAULT_ZOOM_DURATION = 200;

    private static final int HORIZONTAL_EDGE_NONE = -1;
    private static final int HORIZONTAL_EDGE_LEFT = 0;
    private static final int HORIZONTAL_EDGE_RIGHT = 1;
    private static final int HORIZONTAL_EDGE_BOTH = 2;
    private static final int VERTICAL_EDGE_NONE = -1;
    private static final int VERTICAL_EDGE_TOP = 0;
    private static final int VERTICAL_EDGE_BOTTOM = 1;
    private static final int VERTICAL_EDGE_BOTH = 2;

    private final Interpolator mInterpolator = new AccelerateDecelerateInterpolator();
    private int mZoomDuration = DEFAULT_ZOOM_DURATION;
    private float mMinScale = DEFAULT_MIN_SCALE;
    private float mMidScale = DEFAULT_MID_SCALE;
    private float mMaxScale = DEFAULT_MAX_SCALE;

    private boolean mAllowParentInterceptOnEdge = true;

    private final PhotoView photoView;

    // Gesture Detectors
    private GestureDetector mGestureDetector;
    private ScaleGestureDetector mScaleDragDetector;

    // These are set so we don't keep allocating them on the heap
    private final Matrix mBaseMatrix = new Matrix();
    private final Matrix mDrawMatrix = new Matrix();
    private final Matrix mSuppMatrix = new Matrix();
    private final RectF mDisplayRect = new RectF();
    private final float[] mMatrixValues = new float[9];

    private FlingRunnable mCurrentFlingRunnable;
    private AnimatedZoomRunnable mAnimatedZoomRunnable;
    private int mHorizontalScrollEdge = HORIZONTAL_EDGE_BOTH;
    private int mVerticalScrollEdge = VERTICAL_EDGE_BOTH;

    private boolean mZoomEnabled = true;
    private ScaleType mScaleType = ScaleType.FIT_CENTER;
    private final RotationGestureDetector rotationGestureDetector = new RotationGestureDetector(rotation -> {
        setRotationTo(rotation);
        return Unit.INSTANCE;
    }, endRotation -> {
        doRotationEnd();
        return Unit.INSTANCE;
    });

    private void doRotationEnd() {
        ViewGroup parent = (ViewGroup) photoView.getParent();
        TransitionSet transitionSet = new TransitionSet()
                .addTransition(new AutoTransition())
                .addTransition(new ChangeImageTransform());
        TransitionManager.beginDelayedTransition(parent, transitionSet);
        update();
    }

    public PhotoViewController(PhotoView imageView) {
        photoView = imageView;
        imageView.setOnTouchListener(this);
        imageView.addOnLayoutChangeListener(this);
        if (imageView.isInEditMode()) {
            return;
        }
        ScaleGestureDetector.OnScaleGestureListener mScaleListener = new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                if (rotationGestureDetector.isInProgress()) {
                    return true;
                }
                float scaleFactor = detector.getScaleFactor();
                if (Float.isNaN(scaleFactor) || Float.isInfinite(scaleFactor)) {
                    return false;
                }
                if (scaleFactor >= 0) {
                    PhotoViewController.this.doScale(scaleFactor, detector.getFocusX(), detector.getFocusY());
                }
                return true;
            }
        };
        mScaleDragDetector = new ScaleGestureDetector(imageView.getContext(), mScaleListener);
        mGestureDetector = new GestureDetector(imageView.getContext(), new GestureDetector.SimpleOnGestureListener() {

            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float dx, float dy) {
                ViewParent parent = photoView.getParent();
                if (rotationGestureDetector.isInProgress()) {
                    parent.requestDisallowInterceptTouchEvent(true);
                    return true;
                }
                boolean intercept = true;
                if (mAllowParentInterceptOnEdge && !mScaleDragDetector.isInProgress()) {
                    float positiveDx = Math.abs(dx);
                    float positiveDy = Math.abs(dy);
                    boolean dragHorizontal = positiveDx > positiveDy;
                    boolean dragVertical = positiveDy > positiveDx;
                    boolean isHorizontalEdgeBoth = mHorizontalScrollEdge == HORIZONTAL_EDGE_BOTH && photoView.getParentScrollDirection() == PhotoView.HORIZONTAL;
                    boolean isHorizontalEdgeLeft = mHorizontalScrollEdge == HORIZONTAL_EDGE_LEFT && dx < 0 && dragHorizontal;
                    boolean isHorizontalEdgeRight = mHorizontalScrollEdge == HORIZONTAL_EDGE_RIGHT && dx > 0 && dragHorizontal;
                    boolean isVerticalEdgeBoth = mHorizontalScrollEdge == VERTICAL_EDGE_BOTH && photoView.getParentScrollDirection() == PhotoView.VERTICAL;
                    boolean isVerticalEdgeTop = mVerticalScrollEdge == VERTICAL_EDGE_TOP && dy < 0 && dragVertical;
                    boolean isVerticalEdgeBottom = mVerticalScrollEdge == VERTICAL_EDGE_BOTTOM && dy > 0 && dragVertical;
                    if (getScale() >= mMinScale) {
                        if (isHorizontalEdgeBoth || isHorizontalEdgeLeft || isHorizontalEdgeRight || isVerticalEdgeBoth || isVerticalEdgeTop || isVerticalEdgeBottom) {
                            intercept = false;
                        }
                    }
                }
                parent.requestDisallowInterceptTouchEvent(intercept);
                mSuppMatrix.postTranslate(-dx, -dy);
                checkAndDisplayMatrix();
                return true;
            }

            @Override
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                mCurrentFlingRunnable = new FlingRunnable(photoView.getContext());
                mCurrentFlingRunnable.fling(getImageViewWidth(photoView), getImageViewHeight(photoView), -(int) velocityX, -(int) velocityY);
                photoView.postOnAnimation(mCurrentFlingRunnable);
                return true;
            }

            @Override
            public boolean onDoubleTap(MotionEvent ev) {
                try {
                    float scale = getScale();
                    float x = ev.getX();
                    float y = ev.getY();
                    if (scale < getMediumScale()) {
                        setScale(getMediumScale(), x, y, true);
                    } else if (scale >= getMediumScale() && scale < getMaximumScale()) {
                        setScale(getMaximumScale(), x, y, true);
                    } else {
                        setScale(getMinimumScale(), x, y, true);
                    }
                } catch (Exception ignored) {
                }
                return true;
            }

            @Override
            public boolean onDown(MotionEvent e) {
                return super.onDown(e);
            }

            @Override
            public boolean onSingleTapUp(MotionEvent e) {
                return super.onSingleTapUp(e);
            }
        });
    }

    public void setOnDoubleTapListener(GestureDetector.OnDoubleTapListener newOnDoubleTapListener) {
        this.mGestureDetector.setOnDoubleTapListener(newOnDoubleTapListener);
    }

    @Deprecated
    public boolean isZoomEnabled() {
        return mZoomEnabled;
    }

    public RectF getDisplayRect() {
        checkMatrixBounds();
        return getDisplayRect(getDrawMatrix());
    }

    public boolean setDisplayMatrix(Matrix finalMatrix) {
        if (finalMatrix == null) {
            throw new IllegalArgumentException("Matrix cannot be null");
        }
        if (photoView.getDrawable() == null) {
            return false;
        }
        mSuppMatrix.set(finalMatrix);
        checkAndDisplayMatrix();
        return true;
    }

    public void setRotationTo(float degrees) {
        mSuppMatrix.setRotate(degrees % 360, photoView.getWidth() / 2f, photoView.getHeight() / 2f);
        checkAndDisplayMatrix();
    }

    public float getMinimumScale() {
        return mMinScale;
    }

    public float getMediumScale() {
        return mMidScale;
    }

    public float getMaximumScale() {
        return mMaxScale;
    }

    public float getScale() {
        return (float) Math.sqrt((float) Math.pow(getValue(mSuppMatrix, Matrix.MSCALE_X), 2) + (float) Math.pow
                (getValue(mSuppMatrix, Matrix.MSKEW_Y), 2));
    }

    public ScaleType getScaleType() {
        return mScaleType;
    }

    @Override
    public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int
            oldRight, int oldBottom) {
        // Update our base matrix, as the bounds have changed
        if (left != oldLeft || top != oldTop || right != oldRight || bottom != oldBottom) {
            updateBaseMatrix(photoView.getDrawable());
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent ev) {
        boolean handled = false;
        if (mZoomEnabled && photoView.getDrawable() != null) {
            switch (ev.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    ViewParent parent = v.getParent();
                    // First, disable the Parent from intercepting the touch
                    // event
                    if (parent != null) {
                        parent.requestDisallowInterceptTouchEvent(true);
                    }
                    // If we're flinging, and the user presses down, cancel
                    // fling
                    cancelFling();
                    break;
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    // If the user has zoomed less than min scale, zoom back
                    // to min scale
                    if (mScaleDragDetector != null) {
                        float focusX = mScaleDragDetector.getFocusX();
                        float focusY = mScaleDragDetector.getFocusY();
                        RectF rect = getDisplayRect();
                        if (rect != null) {
                            if (getScale() < mMinScale) {
                                cancelAnimatedZoom();
                                mAnimatedZoomRunnable = new AnimatedZoomRunnable(getScale(), mMinScale, focusX, focusY);
                                photoView.postOnAnimation(mAnimatedZoomRunnable);
                                handled = true;
                            } else if (getScale() > mMaxScale) {
                                cancelAnimatedZoom();
                                mAnimatedZoomRunnable = new AnimatedZoomRunnable(getScale(), mMaxScale, focusX, focusY);
                                photoView.postOnAnimation(mAnimatedZoomRunnable);
                                handled = true;
                            }
                        }
                    }
                    break;
            }
            // Try the Scale/Drag detector
            if (getScale() <= mMinScale || rotationGestureDetector.isInProgress()) {
                rotationGestureDetector.onTouchEvent(v, ev);
            }
            if (mScaleDragDetector != null) {
                handled = mScaleDragDetector.onTouchEvent(ev);
            }
            // Check to see if the user double tapped
            if (mGestureDetector != null && mGestureDetector.onTouchEvent(ev)) {
                handled = true;
            }
        }
        return handled || rotationGestureDetector.isInProgress();
    }

    private void doScale(float scaleFactor, float focusX, float focusY) {
        float curScale = getScale();
        if ((curScale < mMaxScale * 2) && (scaleFactor > 1) || (curScale > mMinScale / 2) && (scaleFactor < 1)) {
            mSuppMatrix.postScale(scaleFactor, scaleFactor, focusX, focusY);
            checkAndDisplayMatrix();
        }
    }

    public void setAllowParentInterceptOnEdge(boolean allow) {
        mAllowParentInterceptOnEdge = allow;
    }

    public void setMinimumScale(float minimumScale) {
        mMinScale = minimumScale;
    }

    public void setMediumScale(float mediumScale) {
        mMidScale = mediumScale;
    }

    public void setMaximumScale(float maximumScale) {
        mMaxScale = maximumScale;
    }

    public void setScaleLevels(float minimumScale, float mediumScale, float maximumScale) {
        mMinScale = minimumScale;
        mMidScale = mediumScale;
        mMaxScale = maximumScale;
    }

    public void setScale(float scale) {
        setScale(scale, false);
    }

    public void setScale(float scale, boolean animate) {
        setScale(scale, photoView.getRight() / 2f, photoView.getBottom() / 2f, animate);
    }

    public void setScale(float scale, float focalX, float focalY, boolean animate) {
        // Check to see if the scale is within bounds
        if (scale < mMinScale || scale > mMaxScale) {
            throw new IllegalArgumentException("Scale must be within the range of minScale and maxScale");
        }
        if (animate) {
            cancelAnimatedZoom();
            mAnimatedZoomRunnable = new AnimatedZoomRunnable(getScale(), scale, focalX, focalY);
            photoView.postOnAnimation(mAnimatedZoomRunnable);
        } else {
            mSuppMatrix.setScale(scale, scale, focalX, focalY);
            checkAndDisplayMatrix();
        }
    }

    public void setScaleType(@NonNull ScaleType scaleType) {
        if (scaleType != ImageView.ScaleType.MATRIX && scaleType != mScaleType) {
            mScaleType = scaleType;
            update();
        }
    }

    public boolean isZoomable() {
        return mZoomEnabled;
    }

    public void setZoomable(boolean zoomable) {
        mZoomEnabled = zoomable;
        update();
    }

    public void update() {
        updateBaseMatrix(photoView.getDrawable());
    }

    /**
     * Get the display matrix
     *
     * @param matrix target matrix to copy to
     */
    public void getDisplayMatrix(Matrix matrix) {
        matrix.set(getDrawMatrix());
    }

    /**
     * Get the current support matrix
     */
    public void getSuppMatrix(Matrix matrix) {
        matrix.set(mSuppMatrix);
    }

    private Matrix getDrawMatrix() {
        mDrawMatrix.set(mBaseMatrix);
        mDrawMatrix.postConcat(mSuppMatrix);
        return mDrawMatrix;
    }

    public Matrix getImageMatrix() {
        return mDrawMatrix;
    }

    public void setZoomTransitionDuration(int milliseconds) {
        this.mZoomDuration = milliseconds;
    }

    /**
     * Helper method that 'unpacks' a Matrix and returns the required value
     *
     * @param matrix     Matrix to unpack
     * @param whichValue Which value from Matrix.M* to return
     * @return returned value
     */
    private float getValue(Matrix matrix, int whichValue) {
        matrix.getValues(mMatrixValues);
        return mMatrixValues[whichValue];
    }

    /**
     * Resets the Matrix back to FIT_CENTER, and then displays its contents
     */
    private void resetMatrix() {
        mSuppMatrix.reset();
        mSuppMatrix.setRotate(rotationGestureDetector.getLastEndRotation());
        checkAndDisplayMatrix();
    }

    private void setImageViewMatrix(Matrix matrix) {
        photoView.setImageMatrix(matrix);
    }

    /**
     * Helper method that simply checks the Matrix, and then displays the result
     */
    private void checkAndDisplayMatrix() {
        if (checkMatrixBounds()) {
            setImageViewMatrix(getDrawMatrix());
        }
    }

    /**
     * Helper method that maps the supplied Matrix to the current Drawable
     *
     * @param matrix - Matrix to map Drawable against
     * @return RectF - Displayed Rectangle
     */
    private RectF getDisplayRect(Matrix matrix) {
        Drawable d = photoView.getDrawable();
        if (d != null) {
            mDisplayRect.set(0, 0, d.getIntrinsicWidth(),
                    d.getIntrinsicHeight());
            matrix.mapRect(mDisplayRect);
            return mDisplayRect;
        }
        return null;
    }

    /**
     * Calculate Matrix for FIT_CENTER
     *
     * @param drawable - Drawable being displayed
     */
    private void updateBaseMatrix(Drawable drawable) {
        if (drawable == null) {
            return;
        }
        final float viewWidth = getImageViewWidth(photoView);
        final float viewHeight = getImageViewHeight(photoView);
        final int drawableWidth = drawable.getIntrinsicWidth();
        final int drawableHeight = drawable.getIntrinsicHeight();
        mBaseMatrix.reset();
        float rotation = rotationGestureDetector.getLastEndRotation();
//        mBaseMatrix.preRotate(rotation, drawableWidth / 2f, drawableHeight / 2f);
        final float widthScale = viewWidth / drawableWidth;
        final float heightScale = viewHeight / drawableHeight;
        if (mScaleType == ScaleType.CENTER) {
            mBaseMatrix.postTranslate((viewWidth - drawableWidth) / 2F,
                    (viewHeight - drawableHeight) / 2F);

        } else if (mScaleType == ScaleType.CENTER_CROP) {
            float scale = Math.max(widthScale, heightScale);
            mBaseMatrix.postScale(scale, scale);
            mBaseMatrix.postTranslate((viewWidth - drawableWidth * scale) / 2F,
                    (viewHeight - drawableHeight * scale) / 2F);

        } else if (mScaleType == ScaleType.CENTER_INSIDE) {
            float scale = Math.min(1.0f, Math.min(widthScale, heightScale));
            mBaseMatrix.postScale(scale, scale);
            mBaseMatrix.postTranslate((viewWidth - drawableWidth * scale) / 2F,
                    (viewHeight - drawableHeight * scale) / 2F);

        } else {
            RectF mTempSrc = new RectF(0, 0, drawableWidth, drawableHeight);
            RectF mTempDst = new RectF(0, 0, viewWidth, viewHeight);
            if ((((int) rotation) % 180) != 0) {
                mTempSrc = new RectF(0, 0, drawableHeight * 1f, drawableWidth * 1f);
            }
            switch (mScaleType) {
                case FIT_CENTER:
                    mBaseMatrix.setRectToRect(mTempSrc, mTempDst, ScaleToFit.CENTER);
                    break;
                case FIT_START:
                    mBaseMatrix.setRectToRect(mTempSrc, mTempDst, ScaleToFit.START);
                    break;
                case FIT_END:
                    mBaseMatrix.setRectToRect(mTempSrc, mTempDst, ScaleToFit.END);
                    break;
                case FIT_XY:
                    mBaseMatrix.setRectToRect(mTempSrc, mTempDst, ScaleToFit.FILL);
                    break;
                default:
                    break;
            }
        }
        resetMatrix();
    }

    private boolean checkMatrixBounds() {
        final RectF rect = getDisplayRect(getDrawMatrix());
        if (rect == null) {
            return false;
        }
        final float height = rect.height(), width = rect.width();
        float deltaX = 0, deltaY = 0;
        final int viewHeight = getImageViewHeight(photoView);
        if (height <= viewHeight) {
            switch (mScaleType) {
                case FIT_START:
                    deltaY = -rect.top;
                    break;
                case FIT_END:
                    deltaY = viewHeight - height - rect.top;
                    break;
                default:
                    deltaY = (viewHeight - height) / 2 - rect.top;
                    break;
            }
            mVerticalScrollEdge = VERTICAL_EDGE_BOTH;
        } else if (rect.top > 0) {
            mVerticalScrollEdge = VERTICAL_EDGE_TOP;
            deltaY = -rect.top;
        } else if (rect.bottom < viewHeight) {
            mVerticalScrollEdge = VERTICAL_EDGE_BOTTOM;
            deltaY = viewHeight - rect.bottom;
        } else {
            mVerticalScrollEdge = VERTICAL_EDGE_NONE;
        }
        final int viewWidth = getImageViewWidth(photoView);
        if (width <= viewWidth) {
//            switch (mScaleType) {
//                case FIT_START:
//                    deltaX = -rect.left;
//                    break;
//                case FIT_END:
//                    deltaX = viewWidth - width - rect.left;
//                    break;
//                default:
//                    deltaX = (viewWidth - width) / 2 - rect.left;
//                    break;
//            }
            //FIT_START,FIT_END水平居中显示
            deltaX = (viewWidth - width) / 2 - rect.left;
            mHorizontalScrollEdge = HORIZONTAL_EDGE_BOTH;
        } else if (rect.left > 0) {
            mHorizontalScrollEdge = HORIZONTAL_EDGE_LEFT;
            deltaX = -rect.left;
        } else if (rect.right < viewWidth) {
            deltaX = viewWidth - rect.right;
            mHorizontalScrollEdge = HORIZONTAL_EDGE_RIGHT;
        } else {
            mHorizontalScrollEdge = HORIZONTAL_EDGE_NONE;
        }
        // Finally actually translate the matrix
        mSuppMatrix.postTranslate(deltaX, deltaY);
        return true;
    }

    private int getImageViewWidth(ImageView imageView) {
        return imageView.getWidth() - imageView.getPaddingLeft() - imageView.getPaddingRight();
    }

    private int getImageViewHeight(ImageView imageView) {
        return imageView.getHeight() - imageView.getPaddingTop() - imageView.getPaddingBottom();
    }


    private void cancelFling() {
        if (mCurrentFlingRunnable != null) {
            mCurrentFlingRunnable.cancel();
            photoView.removeCallbacks(mCurrentFlingRunnable);
            mCurrentFlingRunnable = null;
        }
    }

    private void cancelAnimatedZoom() {
        if (mAnimatedZoomRunnable != null) {
            mAnimatedZoomRunnable.cancel();
            photoView.removeCallbacks(mAnimatedZoomRunnable);
            mAnimatedZoomRunnable = null;
        }
    }

    public void cancelFlingAndZoom() {
        cancelFling();
        cancelAnimatedZoom();
    }

    private class AnimatedZoomRunnable implements Runnable {

        private final float mFocalX, mFocalY;
        private final long mStartTime;
        private final float mZoomStart, mZoomEnd;
        private boolean isCanceled = false;

        public AnimatedZoomRunnable(final float currentZoom, final float targetZoom, final float focalX, final float focalY) {
            mFocalX = focalX;
            mFocalY = focalY;
            mStartTime = System.currentTimeMillis();
            mZoomStart = currentZoom;
            mZoomEnd = targetZoom;
        }

        public void cancel() {
            isCanceled = true;
        }

        @Override
        public void run() {
            if (isCanceled) {
                return;
            }
            float t = interpolate();
            float scale = mZoomStart + t * (mZoomEnd - mZoomStart);
            float deltaScale = scale / getScale();
            doScale(deltaScale, mFocalX, mFocalY);
            // We haven't hit our target scale yet, so post ourselves again
            if (t < 1f) {
                photoView.postOnAnimation(this);
            }
        }

        private float interpolate() {
            float t = 1f * (System.currentTimeMillis() - mStartTime) / mZoomDuration;
            t = Math.min(1f, t);
            t = mInterpolator.getInterpolation(t);
            return t;
        }
    }

    private class FlingRunnable implements Runnable {

        private final OverScroller mScroller;
        private int mCurrentX, mCurrentY;

        public FlingRunnable(Context context) {
            mScroller = new OverScroller(context);
        }

        public void cancel() {
            mScroller.forceFinished(true);
        }

        public void fling(int viewWidth, int viewHeight, int velocityX, int velocityY) {
            final RectF rect = getDisplayRect();
            if (rect == null) {
                return;
            }
            final int startX = Math.round(-rect.left);
            final int minX, maxX, minY, maxY;
            if (viewWidth < rect.width()) {
                minX = 0;
                maxX = Math.round(rect.width() - viewWidth);
            } else {
                minX = maxX = startX;
            }
            final int startY = Math.round(-rect.top);
            if (viewHeight < rect.height()) {
                minY = 0;
                maxY = Math.round(rect.height() - viewHeight);
            } else {
                minY = maxY = startY;
            }
            mCurrentX = startX;
            mCurrentY = startY;
            // If we actually can move, fling the scroller
            if (startX != maxX || startY != maxY) {
                mScroller.fling(startX, startY, velocityX, velocityY, minX,
                        maxX, minY, maxY, 0, 0);
            }
        }

        @Override
        public void run() {
            if (mScroller.isFinished()) {
                return; // remaining post that should not be handled
            }
            if (mScroller.computeScrollOffset()) {
                final int newX = mScroller.getCurrX();
                final int newY = mScroller.getCurrY();
                mSuppMatrix.postTranslate(mCurrentX - newX, mCurrentY - newY);
                checkAndDisplayMatrix();
                mCurrentX = newX;
                mCurrentY = newY;
                // Post On animation
                photoView.postOnAnimation(this);
            }
        }
    }
}
