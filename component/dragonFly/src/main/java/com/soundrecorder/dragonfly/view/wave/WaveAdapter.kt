/********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2018/11/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.soundrecorder.dragonfly.view.wave

import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.getOneWaveWidth

internal class WaveAdapter(private val iWaveItemViewDelegate: IWaveItemViewDelegate) : RecyclerView.Adapter<WaveViewHolder>() {
    override fun getItemViewType(position: Int): Int {
        return when (position) {
            0 -> VIEW_TYPE_HEADER
            itemCount - 1 -> VIEW_TYPE_FOOTER
            else -> VIEW_TYPE_WAVE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WaveViewHolder {
        val width: Int = when (viewType) {
            VIEW_TYPE_HEADER -> iWaveItemViewDelegate.halfWidth()
            VIEW_TYPE_FOOTER -> iWaveItemViewDelegate.halfWidth()
            else -> parent.context.getOneWaveWidth().toInt()
        }
        return WaveViewHolder(iWaveItemViewDelegate.createNewItemView(parent).apply {
            layoutParams = LayoutParams(width, LayoutParams.MATCH_PARENT)
        })
    }

    override fun onBindViewHolder(holder: WaveViewHolder, position: Int) {
        iWaveItemViewDelegate.onBindItemView(holder.waveView, position)
        holder.waveView.setCurViewIndex(position)
        holder.waveView.postInvalidate()
    }

    override fun getItemCount(): Int = Int.MAX_VALUE

    companion object {
        const val VIEW_TYPE_HEADER = 0
        const val VIEW_TYPE_WAVE = 1
        const val VIEW_TYPE_FOOTER = 2
    }
}