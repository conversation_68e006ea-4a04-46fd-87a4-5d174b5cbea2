/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardEngineViewTest
 Description:
 Version: 1.0
 Date: 2022/11/23
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.dragonfly

import android.content.Context
import android.graphics.Color
import android.os.Build
import android.view.View
import android.widget.FrameLayout
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.google.gson.Gson
import com.soundrecorder.dragonfly.bean.AppCardData
import com.soundrecorder.dragonfly.bean.RecorderState
import org.json.JSONObject
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.spy
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [])
class AppCardEngineViewTest {
    @Test
    fun createViewTest() {
        val appCardEngineView = spy(AppCardEngineView())
        val ctx = spy(ApplicationProvider.getApplicationContext() as Context)
        val view = appCardEngineView.createView(ctx)
        Assert.assertNotNull(view)
    }

    @Test
    fun customApplyListDataTest() {
        val appCardEngineView = spy(AppCardEngineView())
        val ctx = spy(ApplicationProvider.getApplicationContext() as Context)
        appCardEngineView.customApplyListData(ctx, View(ctx), FrameLayout(ctx))
    }

    @Test
    fun customParseFromListDataTest() {
        val appCardEngineView = spy(AppCardEngineView())
        val ctx = spy(ApplicationProvider.getApplicationContext() as Context)
        appCardEngineView.customParseFromListData(ctx, JSONObject())
    }

    @Test
    fun customParseFromJsonTest() {
        val appCardEngineView = spy(AppCardEngineView())
        val ctx = spy(ApplicationProvider.getApplicationContext() as Context)
        appCardEngineView.customParseFromJson(ctx, JSONObject())
        Assert.assertNull(Whitebox.getInternalState(appCardEngineView, "cardData"))
        val appCardData = AppCardData(
            packageName = "",
            widgetCode = "",
            recordState = RecorderState.INIT,
            saveFileState = -1,
            timeText = "",
            timeTextColor = Color.WHITE,
            isFakeBoldText = true,
            timeDes = "",
            stateText = "",
            recorderName = "",
            stateTextColor = -1,
            fileName = "",
            markSrc = -1,
            recordStateSrc = -1,
            saveFileSrc = -1,
            markRippleSrc = -1,
            stateRippleSrc = -1,
            saveRippleSrc = -1,
            ampsSize = 0,
            lastAmps = ArrayList(),
            cardColor = Color.parseColor("#FAFAFA"),
            cardWaveColor = Color.parseColor("#D9000000"),
            cardDashWaveColor = Color.parseColor("#D9666666")
        )
        appCardEngineView.customParseFromJson(ctx, JSONObject().apply {
            put("data", Gson().toJson(appCardData))
        })
        Assert.assertNotNull(Whitebox.getInternalState(appCardEngineView, "cardData"))
    }

    @Test
    fun setViewParamsTest() {
        val appCardEngineView = spy(AppCardEngineView())
        val ctx = spy(ApplicationProvider.getApplicationContext() as Context)
        val appCardData = AppCardData(
            packageName = "",
            widgetCode = "",
            recordState = RecorderState.INIT,
            saveFileState = -1,
            timeText = "",
            timeTextColor = Color.WHITE,
            isFakeBoldText = true,
            timeDes = "",
            stateText = "",
            recorderName = "",
            stateTextColor = -1,
            fileName = "",
            markSrc = -1,
            recordStateSrc = -1,
            saveFileSrc = -1,
            markRippleSrc = -1,
            stateRippleSrc = -1,
            saveRippleSrc = -1,
            ampsSize = 0,
            lastAmps = emptyList(),
            cardColor = Color.parseColor("#FAFAFA"),
            cardWaveColor = Color.parseColor("#D9000000"),
            cardDashWaveColor = Color.parseColor("#D9666666")
        )
        appCardEngineView.customParseFromJson(ctx, JSONObject().put("data", Gson().toJson(appCardData)))
        appCardEngineView.setViewParams(ctx, View(ctx), FrameLayout(ctx))
    }

    @Test
    fun onInVisibleTest() {
        val appCardEngineView = spy(AppCardEngineView())
        val ctx = spy(ApplicationProvider.getApplicationContext() as Context)
        appCardEngineView.onInVisible(View(ctx))
        appCardEngineView.onInVisible(null)
    }

    @Test
    fun onReleaseTest() {
        val appCardEngineView = spy(AppCardEngineView())
        val ctx = spy(ApplicationProvider.getApplicationContext() as Context)
        appCardEngineView.onRelease(View(ctx))
        appCardEngineView.onRelease(null)
    }

    @Test
    fun onVisibleTest() {
        val appCardEngineView = spy(AppCardEngineView())
        val ctx = spy(ApplicationProvider.getApplicationContext() as Context)
        appCardEngineView.onVisible(View(ctx))
        appCardEngineView.onVisible(null)
    }
}