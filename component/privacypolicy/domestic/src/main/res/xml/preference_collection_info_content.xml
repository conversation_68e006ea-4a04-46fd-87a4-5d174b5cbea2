<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="@string/app_name_main">

    <androidx.preference.Preference
        android:layout="@layout/preference_header"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.soundrecorder.privacypolicy.CollectionInfoHeaderPreference
            android:key="pref_information_header"
            android:layout="@layout/layout_collection_info_header"
            android:selectable="false" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="pref_category_personal_info_content"
        app:top_margin_type="small">

<!--        <com.coui.appcompat.preference.COUIMenuPreference-->
<!--            android:key="key_pref_collection_content_time"-->
<!--            android:title="@string/personal_info_collection_content_time"-->
<!--            android:summary="@string/personal_info_collection_content_time_subtitle"-->
<!--            app:couiAssignment="@string/collection_info_content_time_day7_value" />-->

        <com.coui.appcompat.preference.COUIPreference
            android:key="key_pref_collection_content_purpose"
            android:title="@string/collection_info_content_purpose"
            android:summary="@string/collection_info_content_purpose_subtitle"/>

        <com.coui.appcompat.preference.COUIPreference
            android:key="key_pref_collection_content_usage_scenarios"
            android:title="@string/collection_info_content_usage_scenarios"
            android:summary="@string/collection_info_content_usage_scenarios_subtitle"/>

        <com.coui.appcompat.preference.COUIPreference
            android:key="key_pref_collection_content_collection_count"
            android:title="@string/collection_info_content_collection_count"
            android:summary="@string/collection_info_content_collection_count_subtitle"/>

        <com.coui.appcompat.preference.COUIPreference
            android:key="key_pref_collection_content_content_info"
            android:title="@string/collection_info_content_info"
            android:summary="@string/collection_info_content_info_subtitle"/>

    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <Preference
        android:key="autoclick_footer"
        android:layout="@layout/info_description_preference_footer"
        android:selectable="false"
        android:summary="@string/collection_info_instruction_footer_des" />

</androidx.preference.PreferenceScreen>