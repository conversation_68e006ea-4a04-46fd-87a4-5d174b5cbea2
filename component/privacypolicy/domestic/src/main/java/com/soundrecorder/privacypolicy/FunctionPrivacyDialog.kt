/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FunctionPrivacyDialog.kt
 ** Description : FunctionPrivacyDialog.kt
 ** Version     : 1.0
 ** Date        : 2025/06/04
 ** Author      : <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/06/04     1.0      create
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.KeyEvent
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.statement.COUIStatementClickableSpan
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.StatusBarUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_CLOUD_SYNC
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_FEEDBACK
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SMART_SHORTHAND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SUMMARY_MIND
import com.soundrecorder.modulerouter.utils.Injector

class FunctionPrivacyDialog(private val funcType: Int) {
    companion object {
        private const val TAG = "FunctionPrivacyDialog"
    }

    private var functionDialog: COUIBottomSheetDialog? = null
    private val settingAction by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    fun showFunctionPrivacyDialog(
        activity: FragmentActivity,
        confirmCallback: (() -> Unit)?,
        cancelCallback: (() -> Unit)? = null
    ): COUIBottomSheetDialog? {
        if (functionDialog?.isShowing == true) {
            DebugUtil.w("showFunctionPrivacyDialog", "Privacy Dialog already showing")
            return functionDialog
        }

        val title = activity.resources.getString(R.string.privacy_policy_convert_permission_notice_title)
        val messageId = when (funcType) {
            FUNC_TYPE_SMART_SHORTHAND -> R.string.privacy_policy_smart_generation_permission_notice
            FUNC_TYPE_SUMMARY_MIND -> R.string.privacy_policy_summary_permission_notice
            FUNC_TYPE_CLOUD_SYNC -> R.string.privacy_policy_cloud_sync_permission_notice
            FUNC_TYPE_FEEDBACK -> R.string.privacy_policy_feedback_permission_notice
            else -> {
                DebugUtil.e(TAG, "doShowDialogFunctionInfo invalid funcType: $funcType")
                return null
            }
        }

        val message = activity.resources.getString(messageId)
        val protocolText = createSpan(
            activity,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = activity.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = activity.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        functionDialog = createDialog(
            activity = activity,
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                PermissionUtils.setFuncTypePermission(BaseApplication.getAppContext(), funcType, true)
                releaseDialog()
                confirmCallback?.invoke()
            },
            clickCancel = {
                releaseDialog()
                cancelCallback?.invoke()
            }
        )
        return functionDialog?.apply {
            StatusBarUtil.setDialogStatusBarTransparentAndBlackFont(activity, this, false)
            show()
        }
    }

    @Suppress("SpreadOperator")
    private fun createSpan(activity: FragmentActivity, message: Int, vararg links: Int): SpannableStringBuilder {
        val args = links.map { activity.resources.getString(it) }
        val value = activity.resources.getString(message, *args.toTypedArray())
        val builder = SpannableStringBuilder(value)
        args.forEach { link ->
            val start = value.indexOf(link)
            val end = start + link.length
            if (start >= 0) {
                builder.setSpan(
                    object : COUIStatementClickableSpan(activity) {
                        override fun onClick(widget: View) {
                            settingAction?.launchRecordPrivacy(activity, com.soundrecorder.common.R.string.privacy_policy_settings_policy_key)
                        }
                    },
                    start,
                    end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
        return builder
    }

    @Suppress("LongParameterList")
    private fun createDialog(
        activity: FragmentActivity,
        title: String,
        message: CharSequence,
        protocolTxt: CharSequence,
        ok: String,
        cancel: String,
        clickOk: () -> Unit,
        clickCancel: () -> Unit,
        cancelable: Boolean = true
    ): COUIBottomSheetDialog {
        val userStatementDialog = COUIUserStatementDialog(activity).apply {
            setIsShowInMaxHeight(false)
            setCanceledOnTouchOutside(cancelable)
            hideDragView()
            statement = message
            titleText = title
            bottomButtonText = ok
            exitButtonText = cancel
            protocolText = protocolTxt
            setCancelable(cancelable)
            behavior.isDraggable = false
            dragableLinearLayout?.dragView?.visibility = View.INVISIBLE

            if (cancelable) {
                setOnCancelListener { clickCancel.invoke() }
            }
        }

        userStatementDialog.onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
            override fun onBottomButtonClick() {
                userStatementDialog.dismiss()
                clickOk.invoke()
            }

            override fun onExitButtonClick() {
                userStatementDialog.dismiss()
                clickCancel.invoke()
            }
        }

        userStatementDialog.setOnKeyListener { _, keyCode, event ->
            val isBack = keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP
            if (isBack && userStatementDialog.isShowing) {
                userStatementDialog.dismiss()
                true
            } else false
        }
        return userStatementDialog
    }

    fun releaseDialog() {
        if (functionDialog?.isShowing == true) {
            functionDialog?.dismiss()
        }
        functionDialog = null
    }
}