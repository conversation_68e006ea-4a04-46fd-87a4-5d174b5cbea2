apply from:"../../common_flavor_build.gradle"

android {

    buildTypes {
        debug {
            /*https://record-pp-cn.wanyol.com/index.html*/
            buildConfigField "String", "HOST", "\"`||x{2''zmkgzl%xx%kf\""
            buildConfigField "String", "PRIVACY_POLICY_URL", "\"`||x{2''zmkgzl%xx%kf&\u007Fifqgd&kge'aflmp&`|ed\""
        }
        release {
            /*https://record-pp-cn.allawntech.com/index.html*/
            buildConfigField "String", "HOST", "\"`||x{2''zmkgzl%xx%kf\""
            buildConfigField "String", "PRIVACY_POLICY_URL", "\"`||x{2''zmkgzl%xx%kf&iddi\u007Ff|mk`&kge'aflmp&`|ed\""
        }
    }
    buildFeatures {
        buildConfig true
    }
    namespace "com.soundrecorder.privacypolicy"
}

dependencies {
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
    implementation project(':component:summary')
    compileOnly(libs.kotlinx.coroutines)
    compileOnly libs.androidx.transition.ktx
    compileOnly libs.androidx.appcompat
    implementation libs.androidx.core.ktx
    compileOnly(libs.viewpager2)
    api libs.androidx.lifecycle.runtime
    compileOnly libs.androidx.lifecycle.livedata
//    compileOnly libs.androidx.lifecycle.viewmodel
    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation libs.oplus.coui.toolbar
    implementation libs.oplus.coui.preference
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.rotateview
    implementation libs.oplus.coui.statement
    implementation libs.oplus.coui.panel
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.snackbar
    implementation libs.oplus.coui.clickablespan
    implementation libs.oplus.coui.emptyview
    implementation libs.oplus.coui.scrollview
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.indicator
    implementation libs.oplus.coui.scroll

    // Koin for Android
    implementation(libs.koin)
    testImplementation libs.oplus.addon
    testImplementation libs.oplus.support.adapter

    implementation libs.tagsoup
}
