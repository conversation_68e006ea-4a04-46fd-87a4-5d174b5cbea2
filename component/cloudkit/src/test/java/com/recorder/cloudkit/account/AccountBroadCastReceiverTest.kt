package com.recorder.cloudkit.account

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.recorder.cloudkit.push.CloudPushAgent
import com.recorder.cloudkit.shadows.ShadowCOUIVersionUtil
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.recorder.cloudkit.sync.CloudSynStateHelper
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowCOUIVersionUtil::class])
class AccountBroadCastReceiverTest {
    private val ACTION_ACCOUNT_LOGOUT = "com.heytap.usercenter.account_logout"
    private val KEY_ACCOUNT_LOGOUT_DATA_CLEAN = "com.heytap.usercenter.clean_data"

    private var mContext: Context? = null
    private var mMockApplication: MockedStatic<BaseApplication>? = null
    private var mCenterReceiver: AccountBroadcastReceiver? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockApplication!!.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)
        mCenterReceiver = AccountBroadcastReceiver()
    }

    @After
    fun release() {
        mMockApplication?.close()
        mMockApplication = null
        mContext = null
    }

    @Test
    fun should_clearUserData_when_onReceiveLoginOut() {
        AccountBroadcastReceiver.register(mContext!!)

        val mockObj: MockedStatic<CloudPushAgent> = Mockito.mockStatic(
            CloudPushAgent::class.java)
        val mockLoginStateObj: MockedStatic<CloudSynStateHelper> = Mockito.mockStatic(CloudSynStateHelper::class.java)
        mockLoginStateObj.`when`<Boolean> { CloudSynStateHelper.isLoginFromCache() }.thenReturn(false)

        val intent = Intent(ACTION_ACCOUNT_LOGOUT).apply {
            this.putExtra(KEY_ACCOUNT_LOGOUT_DATA_CLEAN, false)
        }

        mCenterReceiver?.onReceive(mContext, intent)
        mockObj.verify { CloudPushAgent.unregister() }
        mockObj.close()
        mockLoginStateObj.close()
    }
}