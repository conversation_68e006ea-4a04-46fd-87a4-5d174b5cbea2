package com.recorder.cloudkit.sync.bean.constant

object RecordSyncState {
    const val RECOVERY_SCAN_MEDIA = -1
    const val RECOVERY_GET_CONFIG = 0
    const val RECOVERY_META_DATA_START = 1
    const val RECOVERY_META_DATA_SUCCESS = 2
    const val RECOVERY_META_DATA_FAIL = 3
    const val RECOVERY_FILE_START = 4
    const val RECOVERY_FILE_SUCCESS = 5
    const val RECOVERY_FILE_FAIL = 6
    const val RECOVERY_COMPLETED = 8

    const val BACKUP_META_DATA_START = 11
    const val BACKUP_META_DATA_SUCCESS = 12
    const val BACKUP_META_DATA_FAIL = 13
    const val BACKUP_FILE_START = 14
    const val BACKUP_FILE_SUCCESS = 15
    const val BACKUP_FILE_FAIL = 16
    const val BACKUP_COMPLETED = 18
    const val CLOUD_HAS_CONFIG = 19
}