<?xml version="1.0" encoding="UTF-8"?>

<resources>
    <string name="sync_record">112981_Cloud sync for recordings</string>
    <string name="closed_record_not_automatic_cloud">911793_If you turn this off, your recordings will no longer be automatically uploaded to the cloud.</string>
    <string name="allow_mobile_data_syn_of_settings">911794_Turn off Auto sync</string>
    <string name="automatic_syn_cloud_convenient_to_see_of_settings">911795_Automatically back up recordings to the cloud to make them available on all your devices.</string>
    <string name="automatic_syn_of_settings">911796_Auto sync</string>
    <string name="about_record_of_settings">911839_About</string>
    <string name="cloudkit_querying">914404_Querying cloud data…</string>
    <string name="cloudkit_syncing">914405_Syncing data…</string>
    <string name="cloudkit_syncing_complete">914406_Data synced to cloud. %s</string>
    <string name="network_anomaly_sync_pause">914412_Network error. Sync paused.</string>
    <string name="network_anomaly">914413_Network error. Sync not started.</string>
    <string name="reconnecting">914414_Retrying…</string>
    <string name="reconnect">914415_Try again</string>
    <string name="recorder_cloud">914416_Cloud services for Recorder</string>
    <string name="open_save_power_mode_sync_pause">914422_Power saving mode is on. Sync paused.</string>
    <string name="lower_power">914423_Sync paused as battery level is lower than %s. Please charge your device.</string>
    <string name="cloud_space_not_limit">914425_Cloud storage full. Sync paused.</string>
    <string name="upgrade_cloud_space">914426_Upgrade storage</string>
    <string name="clear_storage">914427_Clean up storage</string>
    <string name="server_error_sync_not_start">914431_Service error. Sync not started.</string>
    <string name="server_error_sync_pause">914432_Service error. Sync paused.</string>
    <string name="server_sync_peak_period">914435_Servers are busy. Please try again later.</string>
    <string name="local_storage_full_sync_pause">914436_Insufficient storage. Sync paused.</string>
    <string name="local_storage_full_sync_not_start">914437_Insufficient storage. Sync not started.</string>
    <string name="sync_connect_wlan">914439_Connect to Wi-Fi</string>
    <string name="query_data_failed">914440_Failed to access cloud data.</string>
    <string name="know">914475_Got it</string>
    <string name="go_on">914476_Resume syncing</string>
    <string name="turn_off">914477_Turn off</string>
    <string name="network_error_sync_pause">914478_No network connection. Sync paused.</string>
    <string name="lower_power_unchar">915939_Battery level lower than %s. Sync paused.</string>
    <string name="device_temperature_high_tips">915940_Device temperature too high. Sync paused.</string>
    <string name="network_error_not_sync">915943_No Wi-Fi connection. Sync paused.</string>
    <string name="data_archived">917469_Data is archived and temporarily unavailable. It will be recovered within 24 hours.</string>
    <string name="cloudkit_automatic_settings_export">921320_Automatically upload recordings to the cloud so you can view and manage them from all your devices. Cloud sync for Recorder is provided by HeyTap. For details, refer to the %s.</string>
    <string name="cloudkit_privacy_statement">921321_HeyTap Cloud Privacy Notice</string>
    <string name="allow_mobile_data_syn_new">921322_Sync over mobile data</string>
    <string name="cloudkit_global_device_disable_content">1060995_The cloud service is not supported in the current region. Please use another device.</string>
    <string name="cloudkit_global_account_disable_content">1060996_The cloud service is not supported in the region where the current account was registered. Please switch to another account.</string>
    <string name="cloudkit_server_error">1062620_Service busy. Try again later.</string>
    <string name="cloudkit_net_error">1062621_Network error. Check your network settings and try again.</string>
    <string name="cloudkit_switch_is_opening">1063310_Turning on…</string>
</resources>
