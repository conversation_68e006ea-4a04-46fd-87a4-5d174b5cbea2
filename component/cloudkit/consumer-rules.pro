# cloudkit start
-keep class com.recorder.cloudcompat.**{ *; }
-keep class com.recorder.cloudcompat.service.**{ *; }
-keep class com.recorder.cloudkit.**{ *; }
-keep class com.recorder.cloudkit.account.**{ *; }
-keep class com.recorder.cloudkit.push.**{ *; }
-keep class com.recorder.cloudkit.sync.**{ *; }
-keep class com.recorder.cloudkit.ui.**{ *; }
-keep class com.recorder.cloudkit.utils.**{ *; }

-keep class com.nearme.aidl.** { *; }
-keep class * implements com.platform.usercenter.annotation.NoProguard {
  public *;
}
-keep class com.heytap.usercenter.accountsdk.BuildConfig { *; }
-keep class com.platform.usercenter.annotation.Keep
-keep @com.platform.usercenter.annotation.Keep class * {*;}
-keep class com.heytap.uccompiler.annotation.interceptor.** { *; }
-keep class com.accountbase.** { *; }
-keep class com.platform.usercenter.basic.annotation.Keep
-keep @com.platform.usercenter.basic.annotation.Keep class * {*;}

#stdId
-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep class android.content.pm.**{*;}
# cloudkit end

# push start
-keep public class * extends android.app.Service
-keep class com.heytap.msp.** { *;}
# 加上这个慧眼扫描会对 push引入的 加密模式使用错误风险 中危 豁免
-keep class com.heytap.mcssdk.** { *;}
# push end