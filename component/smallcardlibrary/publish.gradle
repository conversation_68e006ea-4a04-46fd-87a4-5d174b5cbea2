apply plugin: 'maven-publish'

buildscript {
    ext {
        lib_groupId = "com.coloros.soundrecorder"
        lib_artifactId = "SmallCard"
        //release版本没有publish权限,需要申请评估
        lib_version = "1.0.0-SNAPSHOT"
    }
}

// Because the components are created only during the afterEvaluate phase, you must
// configure your publications using the afterEvaluate() lifecycle method.
afterEvaluate {
    publishing {
        publications {
            // Creates a Maven publication called "release".
            release(MavenPublication) {
                from components.release
                // You can then customize attributes of the publication as shown below.
                groupId = "$lib_groupId"
                artifactId = "$lib_artifactId"
                version = "$lib_version"
            }
        }
        repositories {
            maven {
                allowInsecureProtocol = true
                def mavenSnapshotsUrl = "http://maven.scm.adc.com:8081/nexus/content/repositories/snapshots/"
                def mavenReleasesUrl = "http://maven.scm.adc.com:8081/nexus/content/repositories/releases/"
                url = "$lib_version".endsWith('SNAPSHOT') ? mavenSnapshotsUrl : mavenReleasesUrl
                credentials {
                    username sonatypeUsername
                    password sonatypePassword
                }
            }
        }
    }
}
