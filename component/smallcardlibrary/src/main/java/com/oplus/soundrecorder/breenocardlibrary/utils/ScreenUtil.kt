/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ScreenUtil
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.utils

import android.app.UiModeManager
import android.content.Context
import android.util.TypedValue
import androidx.annotation.DimenRes

internal object ScreenUtil {

    private const val SCALE_VALUE = 0.5f

    @JvmStatic
    fun Context.dp2px(dp: Int): Float {
        val scale = resources.displayMetrics.density
        return (dp * scale + SCALE_VALUE)
    }

    @JvmStatic
    fun Context.px2dp(px: Int): Float {
        val scale = resources.displayMetrics.density
        return (px / scale + SCALE_VALUE)
    }

    @JvmStatic
    fun Context.isNightMode(): Boolean {
        val uiModeManager = getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        return uiModeManager.nightMode == UiModeManager.MODE_NIGHT_YES
    }

    @JvmStatic
    fun Context.getFloatValue(@DimenRes dimenResId: Int): Float {
        val ratioTypedValue = TypedValue()
        resources.getValue(dimenResId, ratioTypedValue, true)
        return ratioTypedValue.float
    }
}