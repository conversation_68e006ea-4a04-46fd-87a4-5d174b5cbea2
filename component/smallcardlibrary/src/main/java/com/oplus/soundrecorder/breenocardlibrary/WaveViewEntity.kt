/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveViewEntity
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.oplus.smartengine.annotation.CustomEntityTag
import com.oplus.smartenginecustomlib.IEngineView
import com.oplus.soundrecorder.breenocardlibrary.bean.RecorderState
import com.oplus.soundrecorder.breenocardlibrary.bean.SmallCardData
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.doAction
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.gson
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.log
import com.oplus.soundrecorder.breenocardlibrary.views.SmallCardLayout
import com.oplus.soundrecorder.breenocardlibrary.views.WaveView
import org.json.JSONObject

@CustomEntityTag("WaveViewEntity")
@Suppress("TooGenericExceptionCaught")
class WaveViewEntity : IEngineView() {

    private var isRecording = false
    private var cardData: SmallCardData? = null
    private var widgetCode = ""

    override fun createView(context: Context): View? {
        return try {
            return SmallCardLayout(context).apply {
                "createView>>>>>> WaveViewEntity = ${this@WaveViewEntity} , view = $this ".log()
            }
        } catch (e: Exception) {
            e.log()
            null
        }
    }

    override fun customApplyListData(context: Context, view: View, parent: ViewGroup?) {
    }

    override fun customParseFromJson(context: Context, jsonObject: JSONObject) {
        try {
            val recordDataStr = jsonObject.getString("recordData")
            val data = gson.fromJson(recordDataStr, SmallCardData::class.java)
            if (widgetCode.isEmpty() && data.widgetCode.isNotEmpty()) {
                widgetCode = data.widgetCode
            }
            isRecording = (data.recordState == RecorderState.RECORDING)
            cardData = data
        } catch (e: Exception) {
            isRecording = false
            e.log()
        }
    }

    override fun customParseFromListData(context: Context, jsonObject: JSONObject) {
    }

    override fun onInVisible(view: View?) {
        "onInVisible>>>>>> $view".log()
        val smallCardLayout = view as? SmallCardLayout ?: return
        AppCardUtils.runBackground {
            smallCardLayout.context.doAction("onInVisible", widgetCode)
        }
        smallCardLayout.findViewById<WaveView>(R.id.wv_record)?.stopDrawAmp()
        smallCardLayout.onInVisible()
    }

    override fun onRelease(view: View?) {
        "onRelease>>>>>> $view".log()
        val smallCardLayout = view as? SmallCardLayout ?: return
        AppCardUtils.runBackground {
            smallCardLayout.context.doAction("onInVisible", widgetCode)
        }
        smallCardLayout.findViewById<WaveView>(R.id.wv_record)?.stopDrawAmp()
        smallCardLayout.onInVisible()
    }

    override fun onVisible(view: View?) {
        "onVisible>>>>>> $view".log()
        val smallCardLayout = view as? SmallCardLayout ?: return
        AppCardUtils.runBackground {
            //卡片通知录音应用
            smallCardLayout.context?.doAction("onVisible", widgetCode)
        }
        if (isRecording) {
            smallCardLayout.findViewById<WaveView>(R.id.wv_record)?.startDrawAmp()
        }
        smallCardLayout.onVisible()
    }

    /**
     * 动态更新录制时间字体大小，最低不能小于默认的16dp，卡片默认宽高为154dp
     * 默认16dp，在平板上按照宽高等比例进行放大，选取宽高比例中小比例进行放大
     * 比如宽600,高400，则放大后的字体大小为  (16 / 154) * 400
     * 避免调整桌面布局后放大字体导致卡片宽高不一致，字体显示不全
     */

    override fun setViewParams(context: Context, view: View, parent: ViewGroup?) {
        "setViewParams>>>>>>".log()
        val data = cardData ?: return
        val smallCardLayout = view as? SmallCardLayout ?: return
        if (!view.isVisible) return
        smallCardLayout.refreshData(data)
    }
}