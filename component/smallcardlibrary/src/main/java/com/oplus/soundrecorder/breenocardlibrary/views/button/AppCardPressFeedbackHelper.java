package com.oplus.soundrecorder.breenocardlibrary.views.button;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.view.View;
import android.view.animation.Interpolator;
import android.view.animation.PathInterpolator;

public class AppCardPressFeedbackHelper {
    private static final String SCALE_HOLDER = "scaleHolder";
    private static final String ALPHA_HOLDER = "alphaHolder";
    private static final String BRIGHTNESS_HOLDER = "brightnessHolder";
    private static final long DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION = 200L;
    private static final long DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION = 340L;
    private static final float DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE = 0.92f;
    private static final float DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE = 1f;
    private static final float DEFAULT_ALPHA_START_VALUE = 0f;
    private static final float DEFAULT_ALPHA_END_VALUE = 1f;
    private static final float DEFAULT_BRIGHTNESS_START_VALUE = 1f;
    private static final float DEFAULT_BRIGHTNESS_END_VALUE = 0.8f;
    private final PathInterpolator PRESS_FEEDBACK_INTERPOLATOR = new AppCardMoveEaseInterpolator();
    private final PathInterpolator RELEASE_FEEDBACK_INTERPOLATOR = new AppCardInEaseInterpolator();
    private final View view;
    private ValueAnimator mScaleAnimator;
    private float mCurrentScale = DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE;
    private float mCurrentAlpha = DEFAULT_ALPHA_START_VALUE;
    private float mCurrentBrightness = DEFAULT_BRIGHTNESS_START_VALUE;

    public AppCardPressFeedbackHelper(View v) {
        view = v;
    }

    @SuppressLint("Recycle")
    public void executeFeedbackAnimator(final boolean isPressed) {
        long duration = isPressed ? DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION : DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION;
        Interpolator interpolator = isPressed ? PRESS_FEEDBACK_INTERPOLATOR : RELEASE_FEEDBACK_INTERPOLATOR;
        cancelAnimator();
        PropertyValuesHolder scaleValuesHolder;
        PropertyValuesHolder alphaValuesHolder;
        PropertyValuesHolder brightValuesHolder;
        if (isPressed) {
            scaleValuesHolder = PropertyValuesHolder.ofFloat(SCALE_HOLDER, mCurrentScale, DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE);
            alphaValuesHolder = PropertyValuesHolder.ofFloat(ALPHA_HOLDER, mCurrentAlpha, DEFAULT_ALPHA_END_VALUE);
            brightValuesHolder = PropertyValuesHolder.ofFloat(BRIGHTNESS_HOLDER, mCurrentBrightness, DEFAULT_BRIGHTNESS_END_VALUE);
        } else {
            scaleValuesHolder = PropertyValuesHolder.ofFloat(SCALE_HOLDER, mCurrentScale, DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE);
            alphaValuesHolder = PropertyValuesHolder.ofFloat(ALPHA_HOLDER, mCurrentAlpha, DEFAULT_ALPHA_START_VALUE);
            brightValuesHolder = PropertyValuesHolder.ofFloat(BRIGHTNESS_HOLDER, mCurrentBrightness, DEFAULT_BRIGHTNESS_START_VALUE);
        }
        mScaleAnimator = ValueAnimator.ofPropertyValuesHolder(scaleValuesHolder, alphaValuesHolder, brightValuesHolder);
        mScaleAnimator.setDuration(duration);
        mScaleAnimator.setInterpolator(interpolator);
        mScaleAnimator.addUpdateListener(animation -> {
            Float scale = (Float) animation.getAnimatedValue(SCALE_HOLDER);
            Float alpha = (Float) animation.getAnimatedValue(ALPHA_HOLDER);
            Float bright = (Float) animation.getAnimatedValue(BRIGHTNESS_HOLDER);
            if (scale != null) {
                mCurrentScale = scale;
            }
            if (alpha != null) {
                mCurrentAlpha = alpha;
            }
            if (bright != null) {
                mCurrentBrightness = bright;
            }
            setScale(mCurrentScale, view);
        });
        mScaleAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (!isPressed) {
                    reset();
                }
            }
        });
        mScaleAnimator.start();
    }

    public float getAlpha() {
        return mCurrentAlpha;
    }

    public float getBrightness() {
        return mCurrentBrightness;
    }

    private void setScale(float scale, View view) {
        boolean invalidate = false;
        if (view.getScaleX() != scale) {
            view.setScaleX(scale);
            invalidate = true;
        }
        if (view.getScaleY() != scale) {
            view.setScaleY(scale);
            invalidate = true;
        }
        if (invalidate) {
            view.invalidate();
        }
    }

    private void setAlpha(float alpha, View view) {
        if (alpha != view.getAlpha()) {
            view.setAlpha(alpha);
        }

    }

    public void reset() {
        cancelAnimator();
        mCurrentScale = DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE;
        mCurrentAlpha = DEFAULT_ALPHA_START_VALUE;
        mCurrentBrightness = DEFAULT_BRIGHTNESS_START_VALUE;
        setScale(mCurrentScale, view);
    }

    private void cancelAnimator() {
        if (mScaleAnimator != null) {
            mScaleAnimator.removeAllUpdateListeners();
            mScaleAnimator.removeAllListeners();
            if (mScaleAnimator.isRunning()) {
                mScaleAnimator.cancel();
            }
        }
    }
}
