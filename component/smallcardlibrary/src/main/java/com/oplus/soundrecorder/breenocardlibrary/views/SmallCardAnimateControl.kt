/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SmallCardAnimateControl
 * Description:
 * Version: 1.0
 * Date: 2023/11/14
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/11/14 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views

import android.os.SystemClock
import android.view.View
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.transition.ChangeBounds
import androidx.transition.Fade
import androidx.transition.TransitionManager
import androidx.transition.TransitionSet
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.log
import com.oplus.soundrecorder.breenocardlibrary.views.button.AppCardMoveEaseInterpolator

class SmallCardAnimateControl {
    companion object {
        const val DURATION_200 = 200L
        const val DURATION_500 = 500L
        const val FLOAT_0_33 = 0.33F
        const val FLOAT_0_67 = 0.67F
    }

    private val alphaInterpolator by lazy { PathInterpolator(FLOAT_0_33, 0.0f, FLOAT_0_67, 1.0f) }
    private val transInterpolator by lazy { AppCardMoveEaseInterpolator() }
    private var doShowAnimationTime = -1L

    fun showMarkAndSaveFileView(
        should: Boolean = true,
        rootView: ViewGroup,
        markButton: View,
        saveButton: View,
        funRun: (() -> Unit)
    ) {
        if (checkAnimatorRunning(doShowAnimationTime)) {
            return
        }
        if (markButton.alpha != 0f) return
        funRun.invoke()
        "showMarkAndSaveFileView ,should = $should,doShowAnimationTime=$doShowAnimationTime".log()
        if (should) {
            doShowAnimationTime = SystemClock.elapsedRealtime()
            TransitionManager.beginDelayedTransition(rootView, TransitionSet().apply {
                addTransition(Fade(Fade.IN).apply {
                    duration = DURATION_200
                    interpolator = alphaInterpolator
                })
                addTransition(ChangeBounds().apply {
                    interpolator = transInterpolator
                    duration = DURATION_500
                })
            })
        }
        markButton.alpha = 1f
        markButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
            startToStart = ConstraintLayout.LayoutParams.PARENT_ID
            endToEnd = ConstraintLayout.LayoutParams.UNSET
        }
        saveButton.alpha = 1f
        saveButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
            endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
            startToStart = ConstraintLayout.LayoutParams.UNSET
        }
    }

    private fun checkAnimatorRunning(time: Long): Boolean {
        return SystemClock.elapsedRealtime() - time <= DURATION_500
    }
}