/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.share.normal.doc

import android.util.Xml
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import org.xmlpull.v1.XmlSerializer
import java.io.FileOutputStream
import java.io.IOException
import java.lang.Exception

class ExportDocXmlComposer(
    val fileOutputStream: FileOutputStream,
    defaultSerializer: XmlSerializer = Xml.newSerializer()
) {
    companion object {
        private const val TAG = "ExportDocXmlComposer"
        private const val WPS_MARK_DOCUMENT = "w:document"
        private const val WPS_MARK_BODY = "w:body"
        private const val WPS_MARK_P = "w:p"
        private const val WPS_MARK_PPR = "w:pPr"
        private const val WPS_MARK_STYLE = "w:pStyle"
        private const val WPS_MARK_VAL = "w:val"
        private const val WPS_MARK_T = "w:t"
        private const val WPS_MARK_R = "w:r"
        private const val WPS_MARK_B = "w:b"
        private const val WPS_MARK_RPR = "w:rPr"
        private const val WPS_MARK_ID = "w:id"
        private const val WPS_MARK_NAME = "w:name"
        private const val WPS_MARK_BOOK_START = "w:bookmarkStart"
        private const val WPS_MARK_BOOK_END = "w:bookmarkEnd"
        private const val WPS_MARK_VANISH = "w:vanish"
        private const val WPS_COLOR = "w:color"
        private const val WPS_SZ = "w:sz"
        val sSpeakerColorArray =
            arrayOf(
                "FD8326",
                "FFC800",
                "0AD270",
                "0A7FFB",
                "BA5CD6",
                "F42A6D",
                "8FA9AF",
                "26BBD9",
                "6A40BF",
                "009899"
            )
    }

    private var mXmlSerializer = defaultSerializer
    private var id = 0L
    fun startCompose(): Boolean {
        var result = false
        try {
            mXmlSerializer?.apply {
                setOutput(fileOutputStream, "UTF-8")
                startDocument("UTF-8", true)
                startTag(null, WPS_MARK_DOCUMENT)
                startTag(null, WPS_MARK_BODY)
                result = true
            }
        } catch (e: IOException) {
            DebugUtil.e(TAG, "startCompose IOException error", e)
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "startCompose IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "startCompose IllegalStateException error", e)
        }
        return result
    }

    fun addExportTitle(title: String) {
        mXmlSerializer?.apply {
            try {
                startTag(null, WPS_MARK_P)
                startTag(null, WPS_MARK_PPR)
                startTag(null, WPS_MARK_STYLE)
                attribute(null, WPS_MARK_VAL, "Title_Message")
                endTag(null, WPS_MARK_STYLE)
                endTag(null, WPS_MARK_PPR)
                startTag(null, WPS_MARK_R)
                startTag(null, WPS_MARK_T)
                text(title)
                endTag(null, WPS_MARK_T)
                endTag(null, WPS_MARK_R)
                endTag(null, WPS_MARK_P)
                startTag(null, WPS_MARK_P)
                startTag(null, WPS_MARK_PPR)
                startTag(null, WPS_MARK_STYLE)
                attribute(null, WPS_MARK_VAL, "Title_Message")
                endTag(null, WPS_MARK_STYLE)
                endTag(null, WPS_MARK_PPR)
                endTag(null, WPS_MARK_P)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "addExportTitle error", e)
            }
        }
    }

    fun addSubTitle(subTitle: String, hasMark: Boolean = false, isShowSpeaker: Boolean = true) {
        mXmlSerializer?.apply {
            try {
                startTag(null, WPS_MARK_P)
                startTag(null, WPS_MARK_PPR)
                startTag(null, WPS_MARK_STYLE)
                attribute(null, WPS_MARK_VAL, "Subtitle_Message")
                endTag(null, WPS_MARK_STYLE)
                endTag(null, WPS_MARK_PPR)
                if (hasMark) {
                    startTag(null, WPS_MARK_BOOK_START)
                    attribute(null, WPS_MARK_NAME, "speaker")
                    attribute(null, WPS_MARK_ID, "$id")
                    endTag(null, WPS_MARK_BOOK_START)
                }
                startTag(null, WPS_MARK_R)
                if (hasMark && !isShowSpeaker) {
                    startTag(null, WPS_MARK_RPR)
                    startTag(null, WPS_MARK_VANISH)
                    endTag(null, WPS_MARK_VANISH)
                    endTag(null, WPS_MARK_RPR)
                }
                startTag(null, WPS_MARK_T)
                text(subTitle)
                endTag(null, WPS_MARK_T)
                endTag(null, WPS_MARK_R)
                if (hasMark) {
                    startTag(null, WPS_MARK_BOOK_END)
                    attribute(null, WPS_MARK_ID, "${id++}")
                    endTag(null, WPS_MARK_BOOK_END)
                }
                endTag(null, WPS_MARK_P)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "addSubTitle error", e)
            }
        }
    }

    fun addBlankLine() {
        mXmlSerializer?.apply {
            try {
                startTag(null, WPS_MARK_P)
                startTag(null, WPS_MARK_PPR)
                startTag(null, WPS_MARK_STYLE)
                attribute(null, WPS_MARK_VAL, "Subtitle_Message")
                endTag(null, WPS_MARK_STYLE)
                endTag(null, WPS_MARK_PPR)
                endTag(null, WPS_MARK_P)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "addSubTitle error", e)
            }
        }
    }

    fun addConvertVad(
        convertContentItem: ConvertContentItem,
        roleNameHashMap: LinkedHashMap<String, String>,
        isShowSpeaker: Boolean = true
    ) {
        mXmlSerializer?.apply {
            try {
                startTag(null, WPS_MARK_P)
                startTag(null, WPS_MARK_PPR)
                endTag(null, WPS_MARK_PPR)
                if (convertContentItem.roleName?.isNotEmpty() == true) {
                    startTag(null, WPS_MARK_PPR)
                    startTag(null, WPS_MARK_STYLE)
                    attribute(null, WPS_MARK_VAL, "Property_Message")
                    endTag(null, WPS_MARK_STYLE)
                    endTag(null, WPS_MARK_PPR)
                    //add ●
                    startTag(null, WPS_MARK_BOOK_START)
                    attribute(null, WPS_MARK_NAME, "speaker_${convertContentItem.roleId}")
                    attribute(null, WPS_MARK_ID, "$id")
                    endTag(null, WPS_MARK_BOOK_START)
                    startTag(null, WPS_MARK_R)
                    startTag(null, WPS_MARK_RPR)
                    if (!isShowSpeaker) {
                        startTag(null, WPS_MARK_VANISH)
                        endTag(null, WPS_MARK_VANISH)
                    }
                    startTag(null, WPS_COLOR)
                    attribute(null, WPS_MARK_VAL, roleNameHashMap[convertContentItem.roleName] ?: sSpeakerColorArray[0])
                    endTag(null, WPS_COLOR)
                    startTag(null, WPS_SZ)
                    attribute(null, WPS_MARK_VAL, "20")
                    endTag(null, WPS_SZ)
                    endTag(null, WPS_MARK_RPR)
                    startTag(null, WPS_MARK_T)
                    text("● ")
                    endTag(null, WPS_MARK_T)
                    endTag(null, WPS_MARK_R)
                    startTag(null, WPS_MARK_BOOK_END)
                    attribute(null, WPS_MARK_ID, "${id++}")
                    endTag(null, WPS_MARK_BOOK_END)

                    //add speaker
                    startTag(null, WPS_MARK_BOOK_START)
                    attribute(null, WPS_MARK_NAME, "speaker_${convertContentItem.roleId}")
                    attribute(null, WPS_MARK_ID, "$id")
                    endTag(null, WPS_MARK_BOOK_START)
                    startTag(null, WPS_MARK_R)
                    startTag(null, WPS_MARK_RPR)
                    if (!isShowSpeaker) {
                        startTag(null, WPS_MARK_VANISH)
                        endTag(null, WPS_MARK_VANISH)
                    }
                    startTag(null, WPS_MARK_B)
                    endTag(null, WPS_MARK_B)
                    endTag(null, WPS_MARK_RPR)
                    startTag(null, WPS_MARK_T)
                    text("${convertContentItem.roleName}  ")
                    endTag(null, WPS_MARK_T)
                    endTag(null, WPS_MARK_R)
                    startTag(null, WPS_MARK_BOOK_END)
                    attribute(null, WPS_MARK_ID, "${id++}")
                    endTag(null, WPS_MARK_BOOK_END)
                }
                startTag(null, WPS_MARK_BOOK_START)
                attribute(null, WPS_MARK_NAME, "time_$id")
                attribute(null, WPS_MARK_ID, "$id")
                endTag(null, WPS_MARK_BOOK_START)
                startTag(null, WPS_MARK_R)
                startTag(null, WPS_MARK_T)
                text("${convertContentItem.startTime.durationInMsFormatTimeExclusive()}")
                endTag(null, WPS_MARK_T)
                endTag(null, WPS_MARK_R)
                startTag(null, WPS_MARK_BOOK_END)
                attribute(null, WPS_MARK_ID, "${id++}")
                endTag(null, WPS_MARK_BOOK_END)

                endTag(null, WPS_MARK_P)

                startTag(null, WPS_MARK_P)
                startTag(null, WPS_MARK_PPR)
                startTag(null, WPS_MARK_STYLE)
                attribute(null, WPS_MARK_VAL, "Content_Message")
                endTag(null, WPS_MARK_STYLE)
                endTag(null, WPS_MARK_PPR)
                startTag(null, WPS_MARK_R)
                startTag(null, WPS_MARK_T)
                text(convertContentItem.textContent)
                endTag(null, WPS_MARK_T)
                endTag(null, WPS_MARK_R)
                endTag(null, WPS_MARK_P)

                startTag(null, WPS_MARK_P)
                startTag(null, WPS_MARK_PPR)
                startTag(null, WPS_MARK_STYLE)
                attribute(null, WPS_MARK_VAL, "Content_Message")
                endTag(null, WPS_MARK_STYLE)
                endTag(null, WPS_MARK_PPR)
                endTag(null, WPS_MARK_P)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "addConvertVad error", e)
            }
        }
    }

    fun endCompose(): Boolean {
        var result = false
        mXmlSerializer?.apply {
            try {
                endTag(null, WPS_MARK_BODY)
                endTag(null, WPS_MARK_DOCUMENT)
                endDocument()
                result = true
            } catch (e: IllegalArgumentException) {
                DebugUtil.e(TAG, "endCompose IllegalArgumentException error", e)
            } catch (e: IllegalStateException) {
                DebugUtil.e(TAG, "endCompose IllegalStateException error", e)
            } catch (e: IOException) {
                DebugUtil.e(TAG, "endCompose IOException error", e)
            }
        }
        return result
    }

    fun flush() {
        mXmlSerializer?.flush()
    }
}