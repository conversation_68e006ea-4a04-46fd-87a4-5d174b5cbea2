/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - AudioFileUploader.kt
 ** Description: AudioFileUploader.
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/10    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.manager

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.ShareStatisticsUtil
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.share.normal.link.bean.ActionResult
import com.soundrecorder.share.normal.link.manager.listener.IFileUploaderListener
import com.soundrecorder.share.normal.link.utils.AesUtils
import com.soundrecorder.share.normal.link.utils.AesUtils.AES_KEY_LENGTH
import com.soundrecorder.share.normal.link.utils.ConstantUtils
import com.soundrecorder.share.normal.link.utils.HttpUtils
import com.soundrecorder.share.normal.link.utils.ThreadUtil
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.time.Instant

class AudioFileUploader : IFileUploader {

    companion object {
        private const val TAG = "AudioFileUploader"
        private const val UPLOAD_RECORD_FILE_COMPLETE_CODE = 300          // 录音文件上传成功码
        private const val UPLOAD_RECORD_FILE_ERROR_CODE = -300            // 录音文件上传失败码
        private const val UPLOAD_RECORD_FILE_REQUEST_ERROR_CODE = -301    // 录音文件上传请求失败码
        private const val UPLOAD_RECORD_FILE_PROGRESS_CODE = 400          // 心跳开始的进程码
        private const val FILE_UPLOAD_REPORTING_STATUS_ERROR_CODE = -400  // 心跳错误码
        private const val UPLOAD_COMPLETE_VERIFY_PROGRESS_CODE = 500      // 录音文件审核请求成功码
        private const val UPLOAD_COMPLETE_VERIFY_ERROR_CODE = -500        // 录音文件审核请求失败码
        private const val INTERVAL_TIME = 2 * 60 * 1000L
    }
    private var mFileUploaderListenerSet: MutableSet<IFileUploaderListener> = HashSet()
    private var mHeartbeatMap = mutableMapOf<String, Runnable>()                // fileId,心跳
    private var mUploadRecordFileProgressCodeMap = mutableMapOf<String, Int>()  // fileId,心跳进程码

    override fun startUpload(file: File, url: String, fileId: String, shareId: String, recordId: Long) {
        DebugUtil.d(TAG, "startUpload fileId=$fileId")
        uploadRecordFile(file, url, fileId)
        addHeartbeat(fileId)
    }

    override fun registerListener(listener: IFileUploaderListener) {
        mFileUploaderListenerSet.add(listener)
    }

    override fun unregisterListener(listener: IFileUploaderListener) {
        mFileUploaderListenerSet.remove(listener)
    }

    private fun uploadProgress(progress: Int) {
        mFileUploaderListenerSet.forEach {
            it.onUploadProgress(progress)
        }
    }

    private fun uploadComplete() {
        mFileUploaderListenerSet.forEach {
            it.onUploadComplete()
        }
    }

    private fun uploadError(error: Int) {
        mFileUploaderListenerSet.forEach {
            it.onUploadError(error)
        }
    }

    private fun uploadRecordFile(audioFile: File, preSignedUrl: String, fileId: String) {
        DebugUtil.d(TAG, "uploadRecordFile")
        val fileUploadStartTime = Instant.now().epochSecond
        val requestBody = RequestBody.create(RecordConstant.MIMETYPE_MP3.toMediaTypeOrNull(), audioFile)
        HttpUtils.sendAsynPutRequest(preSignedUrl, requestBody, object : okhttp3.Callback {
            override fun onResponse(call: Call, response: Response) {
                ShareStatisticsUtil.addLinkShareFileUploadTimeEvent(Instant.now().epochSecond - fileUploadStartTime)
                removeHeartbeat(fileId)
                if (response.isSuccessful) {
                    DebugUtil.d(TAG, "uploadRecordFile,The audio file upload successfully")
                    uploadProgress(UPLOAD_RECORD_FILE_COMPLETE_CODE)
                    uploadComplete()
                    // 录音文件内容安全审核
                    LinkShareManager.instance.getGlobalDomainAndDoCallBack {
                        uploadCompleteVerify(fileId)
                    }
                } else {
                    DebugUtil.e(TAG, "uploadRecordFile,The audio file upload failed")
                    uploadError(UPLOAD_RECORD_FILE_ERROR_CODE)
                }
            }

            override fun onFailure(call: Call, e: IOException) {
                DebugUtil.e(TAG, "uploadRecordFile,onFailure javaClass=${e.javaClass} ${e.message}")
                ShareStatisticsUtil.addLinkShareFileUploadTimeEvent(Instant.now().epochSecond - fileUploadStartTime)
                removeHeartbeat(fileId)
                uploadError(UPLOAD_RECORD_FILE_REQUEST_ERROR_CODE)
            }
        })
    }

    private fun addHeartbeat(fileId: String) {
        mUploadRecordFileProgressCodeMap[fileId] = UPLOAD_RECORD_FILE_PROGRESS_CODE
        LinkShareManager.instance.getGlobalDomainAndDoCallBack {
            fileUploadReportingStatus(fileId)
        }
        val fileUploadReportingStatus = Runnable {
            LinkShareManager.instance.getGlobalDomainAndDoCallBack {
                fileUploadReportingStatus(fileId)
            }
            mHeartbeatMap[fileId]?.let { ThreadUtil.mIOHandler.postDelayed(it, INTERVAL_TIME) }
        }
        mHeartbeatMap[fileId] = fileUploadReportingStatus
        mHeartbeatMap[fileId]?.let {
            ThreadUtil.mIOHandler.postDelayed(it, INTERVAL_TIME)
        }
    }

    private fun removeHeartbeat(fileId: String) {
        mHeartbeatMap[fileId]?.let { ThreadUtil.mIOHandler.removeCallbacks(it) }
        mHeartbeatMap.remove(fileId)
        mUploadRecordFileProgressCodeMap.remove(fileId)
    }

    fun getCurrentUploadingAudioFileCount(): Int {
        return mHeartbeatMap.size
    }

    /**
     * 文件上传上报状态(心跳)
     * */
    private fun fileUploadReportingStatus(fileId: String) {
        DebugUtil.d(TAG, "fileUploadReportingStatus")
        val domainName = LinkShareManager.instance.getDomainName()
        val baseUrl = ConstantUtils.HTTP + domainName
        val map = mutableMapOf<String, String>()
        map[ConstantUtils.FILE_ID] = fileId
        val requestBody = Gson().toJson(map)
        val aesKey = AesUtils.randomToken(AES_KEY_LENGTH)
        HttpUtils.sendAsynPostEncryptedRequest(baseUrl, ConstantUtils.REPORT_STATUS_PATH, requestBody, aesKey, object : Callback {
                override fun onResponse(call: Call, response: Response) {
                    val responseData = response.body?.string()
                    val decryptedRealResponseBody = HttpUtils.getDecryptedRealResponseBody(responseData, aesKey, "fileUploadReportingStatus")
                    val typeToken = object : TypeToken<ActionResult<String>>() {}.type
                    val decryptedActionResult = Gson().fromJson<ActionResult<String>>(decryptedRealResponseBody, typeToken)
                    if (decryptedActionResult.code == 0) {
                        mUploadRecordFileProgressCodeMap[fileId]?.let {
                            uploadProgress(it)
                            mUploadRecordFileProgressCodeMap[fileId] = it + 1
                        }
                    } else {
                        uploadError(decryptedActionResult.code)
                    }
                }

                override fun onFailure(call: Call, e: IOException) {
                    DebugUtil.e(TAG, "fileUploadReportingStatus,onFailure javaClass=${e.javaClass} ${e.message}")
                    uploadError(FILE_UPLOAD_REPORTING_STATUS_ERROR_CODE)
                }
            })
    }

    /**
     * 文件内容安全审核
    * */
    private fun uploadCompleteVerify(fileId: String) {
        DebugUtil.d(TAG, "uploadCompleteVerify")
        val domainName = LinkShareManager.instance.getDomainName()
        val baseUrl = ConstantUtils.HTTP + domainName
        val map = mutableMapOf<String, String>()
        map[ConstantUtils.FILE_ID] = fileId
        val requestBody = Gson().toJson(map)
        val aesKey = AesUtils.randomToken(AES_KEY_LENGTH)
        HttpUtils.sendAsynPostEncryptedRequest(baseUrl, ConstantUtils.COMPLETE_VERIFY_PATH, requestBody, aesKey, object : Callback {
                override fun onResponse(call: Call, response: Response) {
                    val responseData = response.body?.string()
                    val decryptedRealResponseBody = HttpUtils.getDecryptedRealResponseBody(responseData, aesKey, "uploadCompleteVerify")
                    val typeToken = object : TypeToken<ActionResult<String>>() {}.type
                    val actionResult = Gson().fromJson<ActionResult<String>>(decryptedRealResponseBody, typeToken)
                    if (actionResult.code == 0) {
                        uploadProgress(UPLOAD_COMPLETE_VERIFY_PROGRESS_CODE)
                    } else {
                        uploadError(actionResult.code)
                    }
                }

                override fun onFailure(call: Call, e: IOException) {
                    DebugUtil.e(TAG, "uploadCompleteVerify, onFailure javaClass=${e.javaClass} ${e.message}")
                    uploadError(UPLOAD_COMPLETE_VERIFY_ERROR_CODE)
                }
            })
    }
}