/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonNotification
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.common

import android.annotation.SuppressLint
import android.app.Notification
import android.app.Notification.Action
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import androidx.lifecycle.Observer
import com.soundrecorder.base.ext.currentInMsFormatTimeExclusive
import com.soundrecorder.base.ext.currentInMsFormatTimeTalkBack
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.ext.registerReceiverCompat
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.utils.AppCardUtils.addContinueFlag
import com.soundrecorder.common.utils.AppCardUtils.launchDisplay
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.common.utils.FoldStateLiveData
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.R
import com.soundrecorder.notification.base.BaseNotification
import com.soundrecorder.notification.base.convertToCommonSuitableFontSize
import java.util.*

abstract class CommonNotification(groupId: Int, notificationId: Int) :
    BaseNotification(groupId, notificationId) {

    companion object {
        private const val SHOW_MARK_DURATION = 1500L
        const val OPEN_MINI_RECORDER = "oplus_mini_app_intent"
    }

    override var logTag: String
        get() = "CommonNotification"
        set(_) {}

    private var timer: Timer? = null
    private var timerTask: TimerTask? = null
    private var showMarkPoint: Boolean = false

    private val playNameObserver = Observer<String> {
//        DebugUtil.d(logTag, "playNameObserver: playName == $it, refreshState = $refreshState")
        if (refreshState == REFRESH_STATE_ENABLED) {
            DebugUtil.d(logTag, "playNameObserver: playName == $it, id=$this")
            refreshNotification()
        }
    }

    private val curTimeObserver = Observer<Long> {
        if (refreshState == REFRESH_STATE_ENABLED && notification != null) {
            //DebugUtil.d(logTag, "curTimeObserver: currentTime == $it, id=$this")
            refreshNotification()
        }
    }

    private val playStatusObserver = Observer<Int> {
        /*DebugUtil.d(
            logTag,
            "playStatusObserver: currentPlayStatus = $it, refreshState = $refreshState"
        )*/
        if (refreshState == REFRESH_STATE_ENABLED && notification != null) {
            DebugUtil.d(logTag, "playStatusObserver: currentPlayStatus = $it, id=$this")
            val playTime = notificationModel?.curTime?.value ?: 0L
            val durationTime = notificationModel?.playDuration ?: 0L
            if (durationTime - playTime < SHOW_MARK_DURATION) {
                showNotificationInUI()
            }
            refreshNotification()
        }
    }

    private val isBtnDisabledObserver = Observer<Boolean> {
        /*DebugUtil.d(
            logTag,
            "isBtnDisabledObserver: isBtnDisabled = $it, refreshState = $refreshState"
        )*/
        if (refreshState == REFRESH_STATE_ENABLED) {
            DebugUtil.d(logTag, "isBtnDisabledObserver: isBtnDisabled = $it, id=$this")
            refreshNotification()
        }
    }

    private val isMarkEnabledObserver = Observer<Boolean> {
        val markEnableChanged = notificationModel?.isMarkEnabledChanged() ?: false
        val markPointChanged = notificationModel?.isMarkPointChanged() ?: false
        /*DebugUtil.d(
            logTag,
            "isMarkEnabledObserver: isMarkEnabled = $it, markPoint = ${notificationModel?.markPoint?.value}, lastMarkPoint = ${notificationModel?.lastMarkPoint}, markPointChanged = $markPointChanged, markEnableChanged = $markEnableChanged, refreshState = $refreshState"
        )*/
        if ((refreshState != REFRESH_STATE_ENABLED) || (!markEnableChanged && !markPointChanged)) {
            return@Observer
        }

        DebugUtil.d(
            logTag,
            "isMarkEnabledObserver: isMarkEnabled = $markEnableChanged, markPoint = $markPointChanged"
        )
        if (markPointChanged) {
            showMarkPoint = true
            notificationModel?.saveCurrentMarkPoint()
            refreshNotificationAfterTime()
        }
        notificationModel?.saveCurrentMarkEnabled()
        val playTime = notificationModel?.curTime?.value ?: 0L
        val durationTime = notificationModel?.playDuration ?: 0L
        DebugUtil.d(
            logTag,
            "isMarkEnabledObserver: durationTime = $durationTime, playTime = $playTime"
        )
        if (durationTime - playTime < SHOW_MARK_DURATION) {
            showNotificationInUI()
        }
        refreshNotification()
    }

    private val saveStateObserver = Observer<Int> {
        refreshNotification()
    }

    private val foldStateObserver = Observer<Boolean> {
        sendNotification(true)
    }

    private val foldStateLiveData by lazy {
        FoldStateLiveData()
    }

    private fun refreshNotificationAfterTime() {
        stopTimer()

        timerTask = object : TimerTask() {
            override fun run() {
                //重置标记的标志位
                showMarkPoint = false
                refreshNotification()
            }
        }.also {
            timer = Timer()
            timer?.schedule(it, SHOW_MARK_DURATION)
        }
    }

    private fun stopTimer() {
        timerTask?.cancel()
        timerTask = null

        timer?.let {
            it.cancel()
            it.purge()
        }
        timer = null
    }

    override fun observeData() {
        doInMainThread {
            DebugUtil.i(logTag, "observeData")
            screenStateLiveData.observeForever(screenStateObserver)
            notificationModel?.playName?.observeForever(playNameObserver)
            notificationModel?.curTime?.observeForever(curTimeObserver)
            notificationModel?.playStatus?.observeForever(playStatusObserver)
            notificationModel?.isBtnDisabled?.observeForever(isBtnDisabledObserver)
            notificationModel?.isMarkEnabled?.observeForever(isMarkEnabledObserver)
            notificationModel?.saveState?.observeForever(saveStateObserver)
            defaultContext.registerReceiver(normalEventReceiver, getNormalEventFilter())
            defaultContext.registerReceiverCompat(notificationDeleteEventReceiver, getNotificationDeleteEventFilter(), Context.RECEIVER_NOT_EXPORTED)
            if (FeatureOption.isHasSupportDragonfly()) {
                foldStateLiveData.observeForever(foldStateObserver)
            }
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun onRelease() {
        stopTimer()
        showMarkPoint = false
        doInMainThread {
            DebugUtil.i(logTag, "onRelease")
            screenStateLiveData.removeObserver(screenStateObserver)
            notificationModel?.playName?.removeObserver(playNameObserver)
            notificationModel?.curTime?.removeObserver(curTimeObserver)
            notificationModel?.playStatus?.removeObserver(playStatusObserver)
            notificationModel?.isBtnDisabled?.removeObserver(isBtnDisabledObserver)
            notificationModel?.isMarkEnabled?.removeObserver(isMarkEnabledObserver)
            notificationModel?.saveState?.removeObserver(saveStateObserver)
            try {
                defaultContext.unregisterReceiver(normalEventReceiver)
                defaultContext.unregisterReceiver(notificationDeleteEventReceiver)
            } catch (e: Exception) {
                DebugUtil.w(logTag, "onRelease, unRegisterReceiver")
            }
            if (FeatureOption.isHasSupportDragonfly()) {
                foldStateLiveData.removeObserver(foldStateObserver)
            }
            releaseHandler()
        }
    }

    override fun getLayoutId(): Int {
        return if (BaseUtil.isAndroidSOrLater) {
            R.layout.layout_notification_fast_play
        } else {
            R.layout.layout_notification_fast_play_below_12
        }
    }

    override fun getFoldLayoutId(): Int? {
        return if (BaseUtil.isAndroidSOrLater) {
            R.layout.layout_notification_fast_play_fold
        } else {
           null
        }
    }

    override fun setRemoteViewData(remoteViews: RemoteViews) {
        //标题
        val titlePair = getContentTitle()
        setTitleOrContentText(R.id.title_tv, titlePair.first, titlePair.second)
        //内容
        val contentPair = getContentText()
        setTitleOrContentText(R.id.timestamp_tv, contentPair.first, contentPair.second)
        if (!BaseUtil.isAndroidSOrLater) {
            convertToCommonSuitableFontSize(
                defaultContext,
                remoteViews,
                R.id.timestamp_tv,
                com.soundrecorder.common.R.dimen.sp14
            )
        }
        //播放按钮
        setPlayBtnStatus()
        setPlayBtnClick()
    }

    /**
     * 初始化通知
     */
    @Suppress("DEPRECATION")
    @SuppressLint("WrongConstant")
    override fun initNotification() {
        val builder = getNotificationBuilder()
        notification = builder.setStyle(Notification.DecoratedCustomViewStyle()).apply {
            //此判断包括蜻蜓和火烈鸟，注意适配
            if (FeatureOption.isHasSupportDragonfly() && FoldStateLiveData.hasFoldingClose()) {
                initNotificationBySystem(this)
                getOtherDisplayContentIntent()?.let {
                    setContentIntent(it)
                }
            } else {
                initNotificationByRemoteView(this)
                val jumpIntent = getJumpIntent()?.addContinueFlag()
                if ((notificationModel?.canJumpIntent == true) && (jumpIntent != null)) {
                    PendingIntent.getActivity(
                        defaultContext,
                        PENDING_INTENT_REQUEST_CODE,
                        jumpIntent,
                        PendingIntent.FLAG_IMMUTABLE,
                        DisplayUtils.mainId.launchDisplay()
                    ).let {
                        setContentIntent(it)
                    }
                }
            }
            setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
            setVisibility(Notification.VISIBILITY_PUBLIC)
            setDeleteIntent(getNotificationDeletePendingIntent())
            setOnlyAlertOnce(true)
        }.build()
    }

    /**
     * 外屏内容的pendingIntent
     */
    open fun getOtherDisplayContentIntent(): PendingIntent? = null

    private fun initNotificationByRemoteView(builder: Notification.Builder) {
        builder.apply {
            if (isRemoteViewsInitialized()) {
                if (foldRemoteViews != null) {
                    setCustomContentView(foldRemoteViews) // 折叠样式
                    setCustomBigContentView(remoteViews) // 展开样式
                    setCustomHeadsUpContentView(remoteViews) // 横幅样式
                } else {
                    setCustomContentView(remoteViews)
                }
            }
        }
    }

    private fun initNotificationBySystem(builder: Notification.Builder) {
        builder.apply {
            val contentTitle = getContentTitle()
            val contentText = getContentText()
            if (contentTitle.first.isNotEmpty()) {
                setContentTitle(contentTitle.first)
            }
            if (contentText.first.isNotEmpty()) {
                setContentText(contentText.first)
            }
            getMarkButtonAction()?.let {
                addAction(it) // 添加标记成功后，副屏通知卡片内容显示“00:0x 已标记”
                val lastMarkPoint = notificationModel?.lastMarkPoint ?: -1
                if (showMarkPoint && (lastMarkPoint >= 0)) {
                    setContentText(
                        defaultContext.getString(
                            R.string.notification_marked, TimeUtils.getFormatTimeExclusiveMill(lastMarkPoint)))
                }
            }
            getPlayButtonAction()?.let {
                addAction(it)
            }
            getSaveButtonAction()?.let {
                addAction(it)
            }
        }
    }

    open fun getMarkButtonAction(): Action? = null

    open fun getPlayButtonAction(): Action? {
        return Action.Builder(
//            Icon.createWithResource(defaultContext, CommonNotificationResourceHelper.getPlayButtonImageResId(isPlaying())),
            null,
            CommonNotificationResourceHelper.getPlayButtonText(defaultContext, isPlaying()),
            getPlayButtonPendingIntent())
            .build()
    }

    open fun getSaveButtonAction(): Action? {
        return null
    }

    override fun getContentTitle(): Pair<String, String> {
        return (notificationModel?.playName?.value ?: "").run { Pair(this, this) }
    }

    override fun getContentText(): Pair<String, String> {
        val playTime = notificationModel?.curTime?.value ?: 0L
        val durationTime = notificationModel?.playDuration ?: 0L
        val curTime = if (playTime > durationTime) {
            durationTime
        } else {
            playTime
        }
        val content = StringBuilder(curTime.currentInMsFormatTimeExclusive(durationTime))
            .append(" / ").append(durationTime.durationInMsFormatTimeExclusive(true)).toString()
        val contentDec = StringBuilder(curTime.currentInMsFormatTimeTalkBack(defaultContext, durationTime))
            .append(" / ").append(TimeUtils.getDurationHint(defaultContext, durationTime, true)).toString()
        return Pair(content, contentDec)
    }

    protected open fun setPlayBtnStatus() {
        val isPlayingState = isPlaying()
        if (BaseUtil.isAndroidSOrLater) {
            val playText = CommonNotificationResourceHelper.getPlayButtonText(defaultContext, isPlayingState)
            setRemoteViewText(R.id.play_btn, playText, playText)
        } else {
            remoteViews.setImageViewResource(
                R.id.play_btn,
                CommonNotificationResourceHelper.getPlayButtonImageResId(isPlayingState)
            )
            remoteViews.setContentDescription(
                R.id.play_btn,
                CommonNotificationResourceHelper.getPlayButtonText(defaultContext, isPlayingState)
            )
        }
    }

    private fun setPlayBtnClick() {
        remoteViews.setOnClickPendingIntent(R.id.play_btn_area, getPlayButtonPendingIntent())
        remoteViews.setBoolean(R.id.play_btn, "setEnabled", isBtnEnabled())
    }

    protected fun setMarkContent() {
        val lastMarkPoint = notificationModel?.lastMarkPoint ?: -1
        if (showMarkPoint && (lastMarkPoint >= 0)) {
            setTitleOrContentVisible(R.id.timestamp_tv, false)
            setTitleOrContentVisible(R.id.mark_tv, true)
            val markText = defaultContext.getString(
                R.string.notification_marked,
                TimeUtils.getFormatTimeExclusiveMill(lastMarkPoint)
            )
            val markTextTalk = defaultContext.getString(
                R.string.notification_marked,
                TimeUtils.getFormatContentDescriptionTimeByMillisecond(
                    defaultContext, lastMarkPoint
                )
            )
            setTitleOrContentText(R.id.mark_tv, markText, markTextTalk)

            if (!BaseUtil.isAndroidSOrLater) {
                convertToCommonSuitableFontSize(
                    defaultContext,
                    remoteViews,
                    R.id.mark_tv,
                    com.soundrecorder.common.R.dimen.sp14
                )
            }
        } else {
            setTitleOrContentVisible(R.id.timestamp_tv, true)
            setTitleOrContentVisible(R.id.mark_tv, false)
        }
    }

    protected fun setMarkBtnStatus() {
        //DebugUtil.d(logTag, "setMarkBtnStatus: isMarkEnabled = ${notificationModel?.isMarkEnabled?.value}, isBtnDisabled = ${notificationModel?.isBtnDisabled?.value}")
        if (!BaseUtil.isAndroidSOrLater) {
            remoteViews.setImageViewResource(
                R.id.mark_btn, CommonNotificationResourceHelper.getMarkButtonResId()
            )
        }
        if (notificationModel?.isRecycle == true) {
            remoteViews.setViewVisible(R.id.mark_btn_area, false)
            return
        }
        remoteViews.setOnClickPendingIntent(R.id.mark_btn_area, getMarkButtonPendingIntent())
        remoteViews.setBoolean(R.id.mark_btn, "setEnabled", isMarkButtonEnable())
    }

    protected fun setSaveBtnStatus() {
        DebugUtil.d(
            logTag,
            "setSaveBtnStatus: isSaving = ${notificationModel?.saveState?.value}"
        )
        remoteViews.setOnClickPendingIntent(R.id.save_btn_area, getSaveButtonPendingIntent())
        remoteViews.setBoolean(R.id.save_btn, "setEnabled", isSaveButtonEnable())
    }

    open fun getMarkButtonPendingIntent(): PendingIntent? {
        return if (isMarkButtonEnable()) {
            val markIntent = Intent()
            markIntent.action = NotificationUtils.MARK_CHANGED_ACTION
            markIntent.`package` = BaseUtil.getPackageName()
            markIntent.putExtra(NotificationUtils.KEY_NOTIFICATION_TYPE, getNotificationId())
            PendingIntent.getBroadcast(
                defaultContext, System.currentTimeMillis().toInt(), markIntent, PendingIntent.FLAG_IMMUTABLE)
        } else {
            null
        }
    }

    open fun isMarkButtonEnable(): Boolean =
        isBtnEnabled() && (notificationModel?.isMarkEnabled?.value != false)

    open fun getPlayButtonPendingIntent(): PendingIntent? {
        return if (isBtnEnabled()) {
            val playIntent = Intent()
            playIntent.action = NotificationUtils.PLAY_STATUS_CHANGED_ACTION
            playIntent.`package` = BaseUtil.getPackageName()
            playIntent.putExtra(NotificationUtils.KEY_NOTIFICATION_TYPE, getNotificationId())
            PendingIntent.getBroadcast(
                defaultContext, System.currentTimeMillis().toInt(), playIntent, PendingIntent.FLAG_IMMUTABLE)
        } else {
            null
        }
    }

    open fun getNotificationDeletePendingIntent(): PendingIntent? {
        val intent = Intent()
        intent.action = NotificationUtils.NOTIFICATION_DELETE_ACTION
        intent.`package` = BaseUtil.getPackageName()
        intent.putExtra(NotificationUtils.KEY_NOTIFICATION_TYPE, getNotificationId())
        return PendingIntent.getBroadcast(
            defaultContext, System.currentTimeMillis().toInt(), intent, PendingIntent.FLAG_IMMUTABLE)
    }

    protected open fun isBtnEnabled(): Boolean = notificationModel?.isBtnDisabled?.value != true

    open fun getSaveButtonPendingIntent(): PendingIntent? {
        return if (isSaveButtonEnable()) {
            val markIntent = Intent()
            markIntent.action = NotificationUtils.SAVE_CHANGED_ACTION
            markIntent.`package` = BaseUtil.getPackageName()
            markIntent.putExtra(NotificationUtils.KEY_NOTIFICATION_TYPE, getNotificationId())
            PendingIntent.getBroadcast(
                defaultContext, System.currentTimeMillis().toInt(), markIntent, PendingIntent.FLAG_IMMUTABLE)
        } else {
            null
        }
    }

    open fun isSaveButtonEnable(): Boolean = true
}