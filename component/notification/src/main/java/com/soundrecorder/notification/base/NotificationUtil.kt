/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NotificationUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.base

import android.app.NotificationManager
import android.content.Context
import android.util.TypedValue
import android.widget.RemoteViews
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.textutil.COUIChangeTextUtil.G3
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.DensityHelper

private fun convertToSuitableFontSize(context: Context, remoteViews: RemoteViews, tvId: Int, dimenRes: Int, level: Int) {
    remoteViews.setTextViewTextSize(
        tvId, TypedValue.COMPLEX_UNIT_PX,
        COUIChangeTextUtil.getSuitableFontSize(
            DensityHelper.getDefaultConfigDimension(dimenRes).toFloat(),
            context.resources.configuration.fontScale,
            level
        )
    )
}

fun convertToCommonSuitableFontSize(context: Context, remoteViews: RemoteViews, tvId: Int, dimenRes: Int) {
    convertToSuitableFontSize(context, remoteViews, tvId, dimenRes, G3)
}

@Suppress("TooGenericExceptionCaught")
fun cancelAllNotificationByForce() {
    try {
        val notificationMgr = BaseApplication.getAppContext()
            .getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationMgr.cancelAll()
    } catch (e: Exception) {
        DebugUtil.e("NotificationUtil", "cancel other notifications Exception: $e")
    }
}