/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ThirdPartyFastPlayNotificationTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.third

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.R
import com.soundrecorder.notification.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class ThirdPartyFastPlayNotificationTest {

    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        context = null
    }

    @Test
    fun should_equals_when_getOldChannelId() {
        val notification = ThirdPartyFastPlayNotification(
            NotificationUtils.NOTIFICATION_PLAY_ID,
            10
        )
        Assert.assertEquals(NotificationUtils.BROWSFILE_OLD_CID, notification.getOldChannelId())
    }

    @Test
    fun should_equals_when_getChannelId() {
        val notification = ThirdPartyFastPlayNotification(
            NotificationUtils.NOTIFICATION_PLAY_ID,
            10
        )
        Assert.assertEquals(NotificationUtils.BROWSFILE_CID, notification.getChannelId())
    }

    @Test
    fun should_equals_when_getChannelName() {
        val notification = ThirdPartyFastPlayNotification(
            NotificationUtils.NOTIFICATION_PLAY_ID,
            10
        )
        Assert.assertEquals(context?.resources?.getString(R.string.play_shortcut_channel_name), notification.getChannelName())
    }

    @Test
    fun should_return_null_when_getJumpIntent() {
        val notification = ThirdPartyFastPlayNotification(
            NotificationUtils.NOTIFICATION_PLAY_ID,
            10
        )
        val jumpIntent = notification.getJumpIntent()
        Assert.assertNull(jumpIntent)
    }
}