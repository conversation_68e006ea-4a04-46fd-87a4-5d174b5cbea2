package com.recorder.move

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.heytap.backup.sdk.common.host.BREngineConfig
import com.heytap.backup.sdk.component.BRPluginHandler
import com.recorder.move.shadows.ShadowBaseUtils
import com.recorder.move.shadows.ShadowFeatureOption
import com.recorder.move.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.databean.Record
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config


@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowBaseUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class RecorderRestorePluginTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun should_returnFalse_when_Changed() {
        val recorderBackupPlugin = RecorderRestorePlugin()
        val mIsCancel = Whitebox.getInternalState<Boolean>(recorderBackupPlugin, "mScanFinished")
        val brPluginHandler = Mockito.mock(BRPluginHandler::class.java)
        val config = BREngineConfig()
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config)
        Assert.assertFalse(mIsCancel)
        recorderBackupPlugin.onBackup(null)
        Assert.assertFalse(mIsCancel)
        recorderBackupPlugin.onCancel(null)
        Assert.assertFalse(mIsCancel)
        recorderBackupPlugin.onContinue(null)
        Assert.assertFalse(mIsCancel)
    }

    @Test
    @Throws(Exception::class)
    fun should_size_when_queryLocalRecorder() {
        val recorderBackupPlugin = RecorderRestorePlugin()
        val brPluginHandler = Mockito.mock(BRPluginHandler::class.java)
        val config = BREngineConfig()
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config)
        val records = Whitebox.invokeMethod<List<Record>>(recorderBackupPlugin, "queryLocalRecorder")
        Assert.assertEquals(records.size.toLong(), 0)
    }

    @Test
    @Throws(Exception::class)
    fun should_size_when_containRecord() {
        val recorderBackupPlugin = RecorderRestorePlugin()
        val brPluginHandler = Mockito.mock(BRPluginHandler::class.java)
        val config = BREngineConfig()
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config)
        val record = Whitebox.invokeMethod<Int>(recorderBackupPlugin, "getSameRecordFromLocalList",
            Record())
        Assert.assertEquals(record, null)
    }

    @Test
    @Throws(Exception::class)
    fun should_size_when_containMarkDataBean() {
        val recorderBackupPlugin = RecorderRestorePlugin()
        val brPluginHandler = Mockito.mock(BRPluginHandler::class.java)
        val config = BREngineConfig()
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config)
        val record = Whitebox.invokeMethod<Int>(recorderBackupPlugin, "containMarkDataBean", MarkDataBean(1L, 1))
        Assert.assertEquals(record.toLong(), 0)
    }

    @Test
    @Throws(Exception::class)
    fun should_size_when_getXmlInfo() {
        val recorderBackupPlugin = Mockito.mock(RecorderRestorePlugin::class.java)
        val brPluginHandler = Mockito.mock(BRPluginHandler::class.java)
        val config = BREngineConfig()
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config)
        val record = Whitebox.invokeMethod<String>(recorderBackupPlugin, "getXmlInfo", "test")
        Assert.assertNull(record)
    }

    @Test
    @Throws(Exception::class)
    fun should_size_when_restorePictureMark() {
        val recorderBackupPlugin = Mockito.mock(RecorderRestorePlugin::class.java)
        val brPluginHandler = Mockito.mock(BRPluginHandler::class.java)
        val config = BREngineConfig()
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config)
        Whitebox.invokeMethod<Any>(recorderBackupPlugin, "restorePictureMark")
        Assert.assertNull(Whitebox.getInternalState(recorderBackupPlugin, "mPictureMarkPath"))
    }

    @Test
    @Throws(Exception::class)
    fun should_size_when_restoreConvertRecord() {
        val recorderBackupPlugin = Mockito.mock(RecorderRestorePlugin::class.java)
        val brPluginHandler = Mockito.mock(BRPluginHandler::class.java)
        val config = BREngineConfig()
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config)
        Whitebox.invokeMethod<Any>(recorderBackupPlugin, "restoreConvertRecord")
        Assert.assertNull(Whitebox.getInternalState(recorderBackupPlugin, "mConvertRecordOps"))
    }

    @Test
    @Throws(Exception::class)
    fun should_size_when_isUUIDExist() {
        val recorderBackupPlugin = RecorderRestorePlugin()
        val brPluginHandler = Mockito.mock(BRPluginHandler::class.java)
        val config = BREngineConfig()
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config)
        val mScanFinished = Whitebox.invokeMethod<Boolean>(recorderBackupPlugin, "isUUIDExist", "test")
        Assert.assertFalse(mScanFinished)
    }

    @After
    fun tearDown() {
        mContext = null
    }
}
