/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : RecorderBackupPlugin
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, chenlipeng ********, create
 ***********************************************************/
// OPLUS Java File Skip Rule:TooGenericExceptionCaught
package com.recorder.move;

import static com.recorder.move.spvalue.SharedPreferenceComposer.SHARED_PREFERENCE_XML_NAME;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.coloros.backup.sdk.v2.common.utils.ApplicationFileInfo;
import com.heytap.backup.sdk.common.host.BREngineConfig;
import com.heytap.backup.sdk.component.BRPluginHandler;
import com.heytap.backup.sdk.component.plugin.BackupPlugin;
import com.heytap.backup.sdk.host.listener.ProgressHelper;
import com.recorder.move.spvalue.SharedPreferenceComposer;
import com.recorder.move.spvalue.SmartSharedPreferenceComposer;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.common.databean.GroupInfo;
import com.soundrecorder.common.databean.KeyWord;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.UploadRecord;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.db.CursorHelper;
import com.soundrecorder.common.db.GroupInfoDbUtil;
import com.soundrecorder.common.db.KeyWordDbUtils;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.soundrecorder.common.db.UploadDbUtil;
import com.soundrecorder.common.share.ShareUtil;
import com.soundrecorder.common.utils.ConvertDbUtil;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;

/**
 * Created by ******** on 2019/5/5.
 */

public class RecorderBackupPlugin extends BackupPlugin {
    public static final String RECORDER_FOLDER = "Recorder";
    public static final String RECORDER_BACKUP_XML = "recorder_backup.xml";
    public static final String CONVERT_RECORD_BACKUP_XML = "convert_record_backup.xml";
    public static final String UPLOAD_BACKUP_XML = "upload_backup.xml";
    public static final String GROUP_INFO_BACKUP_XML = "group_info_backup.xml";
    public static final String PICTURE_MARK_XML = "picture_mark.xml";
    public static final String KEY_WORD_XML = "key_word.xml";
    public static final String AMP_FOLDER = "amp";
    public static final String CONVERT_FOLDER = "convert";
    public static final String PHONE_CLONE = "PhoneClone";
    public static final String BACKUP_RESTORE = "BackupRestore";

    private static final int FILE_AMP = 0;
    private static final int FILE_CONVERT = 1;
    private static final int FILE_PICTURE_MARK = 2;

    private String TAG = "RecorderBackupPlugin";
    private ArrayList<ApplicationFileInfo> mOutInfoList;

    private Cursor mRecorderCusor = null;
    private RecorderXmlComposer mRecorderXMl = null;
    private Context mContext;
    private String mRecorderBackupPath;
    private String mRecorderBackupXmlPath;
    private String mConvertRecordBackupXmlPath;
    private String mUploadRecordBackupXmlPath;
    private String mGroupInfoBackupXmlPath;
    private String mPictureMarkBackupXmlPath;
    private String mKeyWordXmlPath;
    private String mSharedPreferenceXmlPath;

    private String mSmartSharedPreferenceXmlPath;
    private boolean mIsCancel;
    private int mCompletedCount = 0;
    private int mMaxCount = 0;
    private BREngineConfig mBREngineConfig;
    private ConvertRecordXmlComposer mConvertRecordXMl = null;
    private UploadRecordXmlComposer mUploadRecordXmlComposer = null;
    private GroupInfoXmlComposer mGroupInfoXmlComposer = null;
    private PictureMarkXmlComposer mPictureMarkXmlComposer = null;
    private BaseXmlComposer<KeyWord> mKeyWordComposer = null;
    private BaseXmlComposer<Unit> mSpComposer = null;

    private BaseXmlComposer<Unit> mSmartSpComposer = null;
    // key 为mediaId , value为对应的录音文件路径
    private final Map<Long,String> mMediaMap = new HashMap<>();

    @Override
    public Bundle onPreview(Bundle bundle) {
        Bundle preview = new Bundle();
        if (mMaxCount == 0) {
            mMaxCount = getMaxCount();
        }
        DebugUtil.d(TAG, "move onPreview mMaxCount = " + mMaxCount);
        ProgressHelper.putMaxCount(preview, mMaxCount);

        String source = getBREngineConfig().getSource();
        long size;

        //搬家场景
        if (PHONE_CLONE.equals(source)) {
            size = getBackupDataSize() + getFolderSize();
            DebugUtil.i(TAG, "PHONE_CLONE size:" + size);
        } else if (BACKUP_RESTORE.equals(source)) {
            //本地备份恢复场景
            size = getBackupDataSize();
            DebugUtil.i(TAG, "BACKUP_RESTORE size:" + size);
        } else {
            //其他场景：如整机云备份，其他场景可以看做是本地备份
            size = getBackupDataSize();
            DebugUtil.i(TAG, "other size:" + size);
        }
        ProgressHelper.putPreviewDataSize(preview, size);
        return preview;
    }

    private long getBackupDataSize() {
        long size = 0;
        long xmlSize = estimateRecordBackupSize() + BackupFileUtil.INSTANCE.estimateConvertRecordSize()
                + BackupFileUtil.INSTANCE.estimateUploadSize()
                + BackupFileUtil.INSTANCE.estimatePictureMarkSize()
                + BackupFileUtil.INSTANCE.estimateKeywordSize()
                + BackupFileUtil.INSTANCE.estimateGroupInfoSize();
        DebugUtil.i(TAG, "all xml size:" + xmlSize);

        long ampFileSize = getExtraFileSize(getBackupDataFiles(null), FILE_AMP);
        long convertFileSize = getExtraFileSize(getBackupDataFiles(DatabaseConstant.PATH_CONVERT), FILE_CONVERT);
        long pictureMarkFileSize = getExtraFileSize(getBackupDataFiles(PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME),FILE_PICTURE_MARK);
        DebugUtil.i(TAG, "BackupFile size, ampFileSize:" + ampFileSize + " convertFileSize:" + convertFileSize + " pictureMarkFileSize:" + pictureMarkFileSize);
        size += xmlSize + ampFileSize + convertFileSize + pictureMarkFileSize;
        return size;
    }

    private long getExtraFileSize(File[] files, int type) {
        if (files == null) {
            return 0;
        }
        long size = 0;
        for (File file : files) {
            switch (type) {
                case FILE_AMP:
                    if ((file != null) && file.isFile() && file.getName().endsWith(Constants.AMP_FILE_SUFFIX)) {
                        size += file.length();
                    }
                    break;
                case FILE_CONVERT:
                case FILE_PICTURE_MARK:
                    if ((file != null) && file.isFile()) {
                        size += file.length();
                    }
                    break;
            }
        }
        return size;
    }

    private long estimateRecordBackupSize() {
        long size = BackupFileUtil.DEFAULT_SIZE;
        Record record = parseCursor(mRecorderCusor);
        if (record != null) {
            long itemSize = (DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED.length() + Long.toString(record.getDateModied()).length()) * 35L;
            size += mMaxCount * itemSize;
        }
        DebugUtil.i(TAG, "RecordBackup size:" + size);
        return size;
    }

    @Override
    public void onCreate(Context context, BRPluginHandler brPluginHandler, BREngineConfig config) {
        DebugUtil.d(TAG, "onCreate");
        mContext = context;
        mIsCancel = false;
        mBREngineConfig = config;
        super.onCreate(context, brPluginHandler, config);
    }

    @Override
    public Bundle onPrepare(Bundle bundle) {
        DebugUtil.d(TAG, "onPrepare");
        if (mMaxCount == 0) {
            mMaxCount = getMaxCount();
        }
        DebugUtil.d(TAG, "onPrepare mMaxCount = " + mMaxCount);
        if (mMaxCount > 0) {
            mRecorderXMl = new RecorderXmlComposer();
            mRecorderXMl.startCompose();
        }
        mConvertRecordXMl = new ConvertRecordXmlComposer();
        mConvertRecordXMl.startCompose();
        mUploadRecordXmlComposer = new UploadRecordXmlComposer();
        mUploadRecordXmlComposer.startCompose();
        mGroupInfoXmlComposer = new GroupInfoXmlComposer();
        mGroupInfoXmlComposer.startCompose();
        mPictureMarkXmlComposer = new PictureMarkXmlComposer();
        mPictureMarkXmlComposer.startCompose();
        mKeyWordComposer = new KeyWordComposer();
        mKeyWordComposer.startCompose();
        mSpComposer = new SharedPreferenceComposer();
        mSpComposer.startCompose();
        if (!BaseUtil.isRealme()) {
            mSmartSpComposer = new SmartSharedPreferenceComposer();
            mSmartSpComposer.startCompose();
        }
        mRecorderBackupPath = mBREngineConfig.getBackupRootPath() + File.separator + RECORDER_FOLDER;
        mRecorderBackupXmlPath = mRecorderBackupPath + File.separator + RECORDER_BACKUP_XML;
        mConvertRecordBackupXmlPath = mRecorderBackupPath + File.separator + CONVERT_RECORD_BACKUP_XML;
        mUploadRecordBackupXmlPath = mRecorderBackupPath + File.separator + UPLOAD_BACKUP_XML;
        mGroupInfoBackupXmlPath = mRecorderBackupPath + File.separator + GROUP_INFO_BACKUP_XML;
        mPictureMarkBackupXmlPath = mRecorderBackupPath + File.separator + PICTURE_MARK_XML;
        mKeyWordXmlPath = mRecorderBackupPath + File.separator + KEY_WORD_XML;
        mSharedPreferenceXmlPath = mRecorderBackupPath + File.separator + SHARED_PREFERENCE_XML_NAME;
        mSmartSharedPreferenceXmlPath = mRecorderBackupPath + File.separator + MoveUtils.getSmartXmlName();
        Bundle prepare = new Bundle();
        ProgressHelper.putMaxCount(prepare, mMaxCount);
        return prepare;
    }

    @Override
    public void onBackup(Bundle bundle) {
        DebugUtil.d(TAG, "onBackup");
        if (!mIsCancel) {
            if (mMaxCount != 0) {
                Bundle progress = new Bundle();
                do {
                    Record data = parseCursor(mRecorderCusor);
                    if (data != null) {
                        mRecorderXMl.addOneRecorderRecord(data);
                        mCompletedCount++;
                        ProgressHelper.putMaxCount(progress, mMaxCount);
                        ProgressHelper.putCompletedCount(progress, mCompletedCount);
                    }
                } while (mRecorderCusor.moveToNext());
                mRecorderXMl.endCompose();
                genConvertRecordXML();
                genUploadXml();
                genGroupInfoXml();
                genPictureMarkXml();
                genKeyWordXml();
            }
            backUpSharedPreferenceXml();
            mOutInfoList = new ArrayList<>();
            File internalSdDirFile = BaseUtil.getPhoneStorageFile(mContext);

            if (internalSdDirFile != null) {
                //搬家帮录音 搬Recordings 目录下音频文件
                ApplicationFileInfo recordingsFileInfo = getMoveRecordingsDirInfo(internalSdDirFile);
                mOutInfoList.add(recordingsFileInfo);
                //搬家帮录音 搬一加 Record 目录下音频文件
                if (BaseUtil.isOnePlus()) {
                    ApplicationFileInfo recordFileInfo = getMoveOPRecordDirInfo(internalSdDirFile);
                    mOutInfoList.add(recordFileInfo);
                }
                //搬家帮录音 搬OPPO Share/Recordings 目录下音频文件
                ApplicationFileInfo oShareFileInfo = getOShareRecordingsDirInfo(internalSdDirFile);
                mOutInfoList.add(oShareFileInfo);
            }
        }
        backupConvertRecordFile();
        backupAmpFile();
        backupPictureMarkFile();
    }

    @Override
    public void onPause(Bundle bundle) {
        DebugUtil.d(TAG, "onPause");
    }

    @Override
    public void onContinue(Bundle bundle) {
        DebugUtil.d(TAG, "onContinue");
        mIsCancel = false;
    }

    @Override
    public void onCancel(Bundle bundle) {
        DebugUtil.d(TAG, "onCancel");
        mIsCancel = true;
    }

    @Override
    public Bundle onDestroy(Bundle bundle) {
        DebugUtil.d(TAG, "onDestroy");
        /*only writeToFile return false ,writeRecord and writeConvert will be false.
         * writeRecord and writeConvert will be true ,With or without data
         * */
        boolean writeRecord = true;
        boolean writeConvert = true;
        boolean writeUpload = true;
        if (mRecorderXMl != null) {
            String recordXmlInfo = mRecorderXMl.getXmlInfo();
            if ((mCompletedCount > 0) && (recordXmlInfo != null)) {
                writeRecord = writeToFile(mRecorderBackupXmlPath, recordXmlInfo.getBytes(StandardCharsets.UTF_8));
            } else {
                DebugUtil.w(TAG, "recordXmlInfo is null. recorder_backup.xml not created.");
            }
        }
        if (mConvertRecordXMl != null) {
            String convertRecordXml = mConvertRecordXMl.getXmlInfo();
            if (convertRecordXml != null) {
                writeConvert = writeToFile(mConvertRecordBackupXmlPath, convertRecordXml.getBytes(StandardCharsets.UTF_8));
            }
        }
        if (mUploadRecordXmlComposer != null) {
            String uploadRecordXml = mUploadRecordXmlComposer.getXmlInfo();
            if (uploadRecordXml != null) {
                writeUpload = writeToFile(mUploadRecordBackupXmlPath, uploadRecordXml.getBytes(StandardCharsets.UTF_8));
            }
            DebugUtil.i(TAG, "uploadxml write to file : " + writeUpload);
        }
        if (mGroupInfoXmlComposer != null) {
            String groupInfoXml = mGroupInfoXmlComposer.getXmlInfo();
            if (groupInfoXml != null) {
                writeUpload = writeToFile(mGroupInfoBackupXmlPath, groupInfoXml.getBytes(StandardCharsets.UTF_8));
            }
            DebugUtil.i(TAG, "groupInfo write to file : " + writeUpload);
        }
        if (mPictureMarkXmlComposer != null) {
            String uploadRecordXml = mPictureMarkXmlComposer.getXmlInfo();
            if (uploadRecordXml != null) {
                writeUpload = writeToFile(mPictureMarkBackupXmlPath, uploadRecordXml.getBytes(StandardCharsets.UTF_8));
            }
            DebugUtil.i(TAG, "pictureMarkXml write to file : " + writeUpload);
        }
        if (mKeyWordComposer != null) {
            String xmlInfo = mKeyWordComposer.getXmlInfo();
            boolean writeResult = false;
            if (xmlInfo != null) {
                writeResult = writeToFile(mKeyWordXmlPath, xmlInfo.getBytes(StandardCharsets.UTF_8));
            }
            DebugUtil.i(TAG, "keyWordXml write to file : " + writeResult);
        }
        if (mSpComposer != null) {
            String xmlInfo = mSpComposer.getXmlInfo();
            boolean writeResult = false;
            if (xmlInfo != null) {
                writeResult = writeToFile(mSharedPreferenceXmlPath, xmlInfo.getBytes(StandardCharsets.UTF_8));
            }
            DebugUtil.i(TAG, "mSpComposer write to file : " + writeResult);
        }
        if (mSmartSpComposer != null) {
            String xmlInfo = mSmartSpComposer.getXmlInfo();
            boolean writeResult = false;
            if (xmlInfo != null) {
                writeResult = writeToFile(mSmartSharedPreferenceXmlPath, xmlInfo.getBytes(StandardCharsets.UTF_8));
            }
            DebugUtil.i(TAG, "mSmartSpComposer write to file : " + writeResult);
        }
        if (mRecorderCusor != null) {
            mRecorderCusor.close();
            mRecorderCusor = null;
        }
        Bundle result = new Bundle();
        if (mIsCancel) {
            ProgressHelper.putBRResult(result, ProgressHelper.BR_RESULT_CANCEL);
        } else {
            ProgressHelper.putBRResult(result, (writeRecord && writeConvert) ? ProgressHelper.BR_RESULT_SUCCESS : ProgressHelper.BR_RESULT_FAILED);
        }
        ProgressHelper.putMaxCount(result, mMaxCount);
        ProgressHelper.putCompletedCount(result, mCompletedCount);

        if (mOutInfoList != null) {
            result.putParcelableArrayList(ApplicationFileInfo.TAG, mOutInfoList);
        }
        return result;
    }


    /**
     * 搬oppo录音的音频文件
     * @param internalSdDirFile
     * @return
     */
    private ApplicationFileInfo getMoveRecordingsDirInfo(File internalSdDirFile){
        ApplicationFileInfo applicationFileInfo = new ApplicationFileInfo();
        String recordPath = null;
        if (BaseUtil.isAndroidQOrLater()) {
            recordPath = Environment.DIRECTORY_MUSIC + File.separator + Constants.RECORDINGS;
        } else {
            recordPath = Constants.RECORDINGS;
        }
        applicationFileInfo.mDataFileSrc = internalSdDirFile.getAbsolutePath() + File.separator + recordPath;

        if (BaseUtil.isAndroidQOrLater()) {
            applicationFileInfo.mDataFileSplit = internalSdDirFile.getAbsolutePath() + File.separator + Environment.DIRECTORY_MUSIC + File.separator + Constants.RECORDINGS;
        } else {
            applicationFileInfo.mDataFileSplit = internalSdDirFile.getAbsolutePath() + File.separator + Constants.RECORDINGS;
        }

        if (!oldVersionSmallQ(mBREngineConfig)) {
            applicationFileInfo.mDataReplace = internalSdDirFile.getAbsolutePath() + File.separator + Environment.DIRECTORY_MUSIC + File.separator + Constants.RECORDINGS;
        } else {
            applicationFileInfo.mDataReplace = internalSdDirFile.getAbsolutePath() + File.separator + Constants.RECORDINGS;
        }
        return applicationFileInfo;
    }

    /**
     * 做了一加融合版本后搬一加外销下的音频文件
     * @param internalSdDirFile
     * @return
     */
    private ApplicationFileInfo getMoveOPRecordDirInfo(File internalSdDirFile){
        ApplicationFileInfo applicationFileInfo = new ApplicationFileInfo();
        String recordPath = null;
        if (BaseUtil.isAndroidQOrLater()) {
            recordPath = Environment.DIRECTORY_MUSIC + File.separator + RecordModeConstant.OP_RECORD;
        } else {
            recordPath = RecordModeConstant.OP_RECORD;
        }
        applicationFileInfo.mDataFileSrc = internalSdDirFile.getAbsolutePath() + File.separator + recordPath;

        if (BaseUtil.isAndroidQOrLater()) {
            applicationFileInfo.mDataFileSplit = internalSdDirFile.getAbsolutePath() + File.separator
                    + Environment.DIRECTORY_MUSIC + File.separator + RecordModeConstant.OP_RECORD;
        } else {
            applicationFileInfo.mDataFileSplit = internalSdDirFile.getAbsolutePath() + File.separator + RecordModeConstant.OP_RECORD;
        }

        if (!oldVersionSmallQ(mBREngineConfig)) {
            applicationFileInfo.mDataReplace = internalSdDirFile.getAbsolutePath() + File.separator
                    + Environment.DIRECTORY_MUSIC + File.separator + RecordModeConstant.OP_RECORD;
        } else {
            applicationFileInfo.mDataReplace = internalSdDirFile.getAbsolutePath() + File.separator + RecordModeConstant.OP_RECORD;
        }
        return applicationFileInfo;
    }

    /**
     * 搬OPPO互传的录音音频文件
     * 文件的原始目录: mDataFileSrc
     * 文件搬到新机上的目标存放目录 = mDataFileSrc.replace(mDataFileSplit,mDataReplace)
     *
     * @param internalSdDirFile
     * @return
     */
    private ApplicationFileInfo getOShareRecordingsDirInfo(File internalSdDirFile) {
        ApplicationFileInfo applicationFileInfo = new ApplicationFileInfo();
        // 设置源文件目录 rootPath/Music/OPPO Share/Recordings
        applicationFileInfo.mDataFileSrc = internalSdDirFile.getAbsolutePath() + File.separator
                + Environment.DIRECTORY_MUSIC + File.separator + ShareUtil.INSTANCE.getDirOShare();
        /* 文件搬到新机上的目标存放目录 = mDataFileSrc.replace(mDataFileSplit,mDataReplace)
         *  OPPO Share/Recordings */
        applicationFileInfo.mDataFileSplit = ShareUtil.INSTANCE.getDirOShare();
        // OPPO Share/Recordings
        applicationFileInfo.mDataReplace = ShareUtil.INSTANCE.getDirOShare();
        return applicationFileInfo;
    }

    private int getMaxCount() {
        if (mRecorderCusor == null) {
            ContentResolver resolver = mContext.getContentResolver();
            try {
                mRecorderCusor = resolver.query(DatabaseConstant.RecordUri.RECORD_CONTENT_URI,
                        DatabaseConstant.getRecordProjection(), null, null, null);
            } catch (Exception e) {
                DebugUtil.e(TAG, "getMaxCount query recorder exception:" + e.toString());
            }
        }

        int maxCount = 0;
        if (mRecorderCusor != null) {
            mRecorderCusor.moveToFirst();
            maxCount = mRecorderCusor.getCount();
        } else {
            DebugUtil.e(TAG, "getMaxCount - mRecorderCursor is null");
        }
        return maxCount;
    }

    private Record parseCursor(Cursor cursor) {
        Record value = null;
        try {
            if (cursor != null) {
                value = new Record(cursor, Record.TYPE_FROM_RECORD);
            }
        } catch (Exception e) {
            DebugUtil.w(TAG, "parseCursor error: " + e, false);
        }
        return value;
    }

    private boolean writeToFile(String fileName, byte[] buf) {
        FileDescriptor fd = getFileDescriptor(fileName);
        if (fd == null) {
            DebugUtil.d(TAG, "writeToFile fileDescriptor is null");
            return false;
        }
        FileOutputStream outStream = null;
        try {
            outStream = new FileOutputStream(fd);
            outStream.write(buf, 0, buf.length);
            outStream.flush();
        } catch (IOException e) {
            DebugUtil.e(TAG, "writeToFile has a exception", e);
            return false;
        } finally {
            if (outStream != null) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "writeToFile close exception", e);
                }
            }
        }
        return true;
    }

    private long getFolderSize() {
        List<Long> list = new ArrayList<>();
        Cursor cursor = null;

        try {
            ContentResolver resolver = mContext.getContentResolver();
            cursor = resolver.query(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, new String[]{MediaStore.Audio.Media._ID}, CursorHelper.getAllRecordContainCallWhereClause(mContext),
                    CursorHelper.getsAcceptableAudioTypes(), null);
            if (cursor != null) {
                int idIndex = cursor.getColumnIndex(MediaStore.Audio.Media._ID);
                while (cursor.moveToNext()) {
                    if (idIndex != -1) {
                        list.add(cursor.getLong(idIndex));
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        long size = 0;
        if (list.size() == 0) {
            return size;
        }
        for (int i = 0; i < list.size(); i++) {
            size += FileUtils.getFileSize(MediaDBUtils.genUri(list.get(i)));
        }
        DebugUtil.i(TAG, "record folder size:" + size);
        return size;
    }

    private boolean oldVersionSmallQ(BREngineConfig brEngineConfig) {
        return brEngineConfig.getOldPhoneOSVersion() < Constants.COLOR_OS_VERSION_Q;
    }

    private void backupAmpFile() {
        File[] files = getBackupDataFiles(null);
        if (files == null) {
            return;
        }
        for (File file : files) {
            if ((file != null) && file.isFile() && file.getName().endsWith(Constants.AMP_FILE_SUFFIX)) {
                String destPath = mRecorderBackupPath + File.separator + AMP_FOLDER + File.separator + file.getName();
                MoveUtils.copyOverwriteFileForBackup(this, file, destPath);
                DebugUtil.i(TAG, "backupAmpFile:" + file.getName());
            }
        }
    }

    private File[] getBackupDataFiles(String path) {
        File dir = mContext.getFilesDir();
        if (path != null) {
            dir = new File(dir, path);
        }
        File dirSDCard = BaseUtil.getPhoneStorageFile(mContext);
        File[] files = dir.listFiles();
        if ((files == null) || (dirSDCard == null)) {
            return null;
        }
        return files;
    }

    private void backupConvertRecordFile() {
        File[] files = getBackupDataFiles(DatabaseConstant.PATH_CONVERT);
        if (files == null) {
            return;
        }
        for (File file : files) {
            if ((file != null) && file.isFile()) {
                String destPath = mRecorderBackupPath + File.separator + CONVERT_FOLDER + File.separator + file.getName();
                MoveUtils.copyOverwriteFileForBackup(this, file, destPath);
            }
        }
    }

    private void backupPictureMarkFile() {
        DebugUtil.i(TAG, "backupPictureMarkFile: start");
        File[] files = getBackupDataFiles(PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME);
        if (files == null) {
            return;
        }
        for (File file : files) {
            if ((file != null) && file.isFile()) {
                String destPath = mRecorderBackupPath + File.separator + PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME + File.separator + file.getName();
                MoveUtils.copyOverwriteFileForBackup(this, file, destPath);
                DebugUtil.i(TAG, "backupPictureMarkFile: destpath= "+ destPath);

            }
        }
    }

    private void genConvertRecordXML() {
        List<ConvertRecord> data = ConvertDbUtil.selectAll();
        for (ConvertRecord record : data) {
            long recordId = record.getRecordId();
            if (!FileUtils.isFileExist(MediaDBUtils.genUri(recordId))) {
                if (TextUtils.isEmpty(record.getConvertTextfilePath())) {
                    DebugUtil.v(TAG, "record convertTextFile is null with id:" + recordId);
                    continue;
                }
                File file = new File(record.getConvertTextfilePath());
                boolean delete = file.delete();
                int deleteResult = -1;
                if (delete) {
                    deleteResult = ConvertDbUtil.deleteByRecordId(recordId);
                }
                DebugUtil.v(TAG, "genConvertRecordXML  delete:" + delete + "   path:" + file.getName() + "  deleteResult:" + deleteResult);
                continue;
            }
            // 保存录音文件的mediaId 和 path
            mMediaMap.put(recordId,record.getMediaPath());
            record.setRecordId(Constants.DEFAULT_MOVE_RECORD_ID);
            mConvertRecordXMl.addConvertRecord(record);
        }
        DebugUtil.d(TAG,"genConvertRecordXML mediaMap:"+ mMediaMap);
        mConvertRecordXMl.endCompose();
    }

    private void genUploadXml() {
        List<UploadRecord> data = UploadDbUtil.getAllUploadRecords(BaseApplication.getAppContext());
        if (data.size() > 0) {
            for (UploadRecord record : data) {
                mUploadRecordXmlComposer.addUploadRecord(record);
            }
        }
        mUploadRecordXmlComposer.endCompose();
    }

    private void genGroupInfoXml() {
        List<GroupInfo> data = GroupInfoDbUtil.getAllGroupInfoList(BaseApplication.getAppContext());
        if (data.size() > 0) {
            for (GroupInfo record : data) {
                mGroupInfoXmlComposer.addGroupInfo(record);
            }
        }
        mGroupInfoXmlComposer.endCompose();
    }

    private void genPictureMarkXml() {
        List<MarkDataBean> markDataBeanList = PictureMarkDbUtils.queryAllPictureMarks();
        if (markDataBeanList.size()>0){
            for (MarkDataBean markDataBean:markDataBeanList) {
                mPictureMarkXmlComposer.addPictureMark(markDataBean);
            }
        }
        mPictureMarkXmlComposer.endCompose();
    }

    private void genKeyWordXml() {
        List<KeyWord> keyWords = KeyWordDbUtils.queryAllKeyWords();
        int count = 0;
        for (KeyWord keyWord : keyWords) {
            String mediaPath = mMediaMap.get(keyWord.getRecordId());
            if (TextUtils.isEmpty(mediaPath)) { // 根据recordId 看对应的录音文件是否存在
                continue;
            }
            count++;
            // 如果存在,则将recordId 和mediaPath 一一对应起来
            keyWord.setMediaPath(mediaPath);
            mKeyWordComposer.addData(keyWord);
        }
        DebugUtil.e(TAG, "genKeyWordXml total:" + keyWords.size() + " 有效：" + count);
        mKeyWordComposer.endCompose();
    }

    private void backUpSharedPreferenceXml() {
        if (mSpComposer != null) {
            mSpComposer.addData(Unit.INSTANCE);
            mSpComposer.endCompose();
            DebugUtil.i(TAG, "backUpSharedPreferenceXml");
        }
        if (mSmartSpComposer != null) {
            mSmartSpComposer.addData(Unit.INSTANCE);
            mSmartSpComposer.endCompose();
            DebugUtil.i(TAG, "backUpSmartPreferenceXml");
        }
    }
}
