apply from: "../../common_flavor_build.gradle"
android {
    namespace 'com.soundrecorder.convertservice'
}

android {
    buildFeatures {
        buildConfig true
    }
    buildTypes {
        debug {
            buildConfigField "String", "signture", "\"#E DvE&DtC'\u0014-\u0019/M.J,M}\u0019.K~\u001Fy\u001D/JzN\""
            buildConfigField "String", "flytekUrl", '"z\\u000Ez\\nyClC4C4\\u001Ab\\u0004}\\bfH+Ej\\u000Ea\\u0002-]2^7T-\\u0002r\\u0000i\\u001F~\\u001DdJ\\"V;W"'
            //config signing type

            buildConfigField "String", "enbackUrl", "\"7a0e7a0a79436c432256394d60137604721765482b456b0a660a6b1c720663006846254a27087e4f\""
            buildConfigField "String", "sec", "\"2a4e77432743731024122a4e7a4e781e26472217744725117446751370142515\""
            buildConfigField "String", "pub", "\"5f165f1d543e7f3173147f0e660d64231a6d5d1f5e0f4a0c4d0c430041102869246d24662542094a0b5a1f5e6c3c4f0370482b1a680e7619284122660461345a3b59695e1a570160241c762e784019286e086f2b793e0e3972440563194c0a491a4d0471136b1d5a1e643c0b6d035b377e4b0e5c39610f63172171365561235b11703c73065c6d5b095c3d6d28650c45374f1b51033b7e197b1060105c047e2c451527431972451566256910576f21685a394f2217650922670a623571400c7a1d4a077e1753264271027714583566236a0d394f3a4f03440a7f3002532157653047107f195e6e45725929115b7439402e476c1443750f4c34600a46084432512360364e16217330410676027a1b7c3f083d0a6e20591b50386f227817254e79085004314201420e7d0a650e655002771670247e0a72204477426d5b2d74115e0c5a1270064b117a49007303625513410b734220751e7c314b6420650970387d1e5319743e6f3a793f4e3b700930086f29710534034f077f147c3e4c024f2c6512430a4e0f5e1f5d\""
        }

        release {
            buildConfigField "String", "signture", "\"#E DvE&DtC'\u0014-\u0019/M.J,M}\u0019.K~\u001Fy\u001D/JzN\""
            buildConfigField "String", "flytekUrl", '"z\\u000Ez\\nyClC4C4\\u001Ab\\u0004}\\bfH+Ej\\u000Ea\\u0002-]2^7T-\\u0002r\\u0000i\\u001F~\\u001DdJ\\"V;W"'

            buildConfigField "String", "enbackUrl", "\"7a0e7a0a79436c432256394d60137604721765482b456b0a660a6b1c720663006846254a27087e4f\""
            buildConfigField "String", "sec", "\"2a4e77432743731024122a4e7a4e781e26472217744725117446751370142515\""
            buildConfigField "String", "pub", "\"5f165f1d543e7f3173147f0e660d64231a6d5d1f5e0f4a0c4d0c430041102869246d24662542094a0b5a1f5e6c3c4f0370482b1a680e7619284122660461345a3b59695e1a570160241c762e784019286e086f2b793e0e3972440563194c0a491a4d0471136b1d5a1e643c0b6d035b377e4b0e5c39610f63172171365561235b11703c73065c6d5b095c3d6d28650c45374f1b51033b7e197b1060105c047e2c451527431972451566256910576f21685a394f2217650922670a623571400c7a1d4a077e1753264271027714583566236a0d394f3a4f03440a7f3002532157653047107f195e6e45725929115b7439402e476c1443750f4c34600a46084432512360364e16217330410676027a1b7c3f083d0a6e20591b50386f227817254e79085004314201420e7d0a650e655002771670247e0a72204477426d5b2d74115e0c5a1270064b117a49007303625513410b734220751e7c314b6420650970387d1e5319743e6f3a793f4e3b700930086f29710534034f077f147c3e4c024f2c6512430a4e0f5e1f5d\""
        }
    }

    namespace "com.soundrecorder.convertservice"
}

dependencies {
    implementation libs.androidx.support
    implementation libs.androidx.appcompat
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.extensions
    // fragment
    implementation libs.androidx.fragment.ktx
    implementation libs.gson

    implementation(libs.heytap.nearx.http)

    // Koin for Android
    implementation(libs.koin)
    //ai-sdk
    implementation libs.ai.unified.summary

    // Room
    kapt libs.androidx.room.compiler
    implementation libs.androidx.room.runtime

    implementation project(':common:RecorderLogBase')
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
    implementation project(':component:translate')
}