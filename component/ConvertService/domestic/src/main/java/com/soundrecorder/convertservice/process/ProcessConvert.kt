/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ProcessConvert
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.process

import android.accounts.NetworkErrorException
import android.os.Handler
import android.os.Looper
import com.google.gson.JsonParseException
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.common.db.CollectionInfoDbUtils
import com.soundrecorder.common.db.KeyWordDbUtils
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE
import com.soundrecorder.common.utils.ConvertDbUtil.SHOW_SWITH_TRUE
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.convertservice.bean.BeanConvert
import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.convertservice.bean.Constant
import com.soundrecorder.convertservice.convert.IConvertCallback
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.convertservice.okhttphelper.OkhttpHelperImpl
import com.soundrecorder.convertservice.security.EncryptException
import com.soundrecorder.modulerouter.BrowseFileAction
import java.lang.Integer.max
import kotlin.math.ceil

class ProcessConvert(
    override val nextProcess: IBackgroundProcess?,
    override var convertCallback: IConvertCallback?,
    private val convertAiTitle: Boolean
) : IBackgroundProcess {

    companion object {
        private const val TAG: String = "ProcessConvert"
        // 开启分词打分接口
        private const val EXTRACT_KEY_WORD_ON = 1
        // 关闭分词打分接口
        private const val EXTRACT_KEY_WORD_OFF = 0
    }

    private val mOkhttpHelper = OkhttpHelperImpl()

    @Volatile
    var mIsAbort = false

    var duration = 0L

    override fun process(req: ConvertRecord): Boolean {
        /***********   entry process convert  *********/
        if (!entryConvertStatus(req)) {
            setStatus(req, ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, Code.UPLOADSTATUS_ISNOT_SUC, "uploadStatus not success")
            deleteDirtyData(req)
            return false
        }

        /************** abort when entry process convert***************/
        if (mIsAbort) {
            setStatus(req, ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC)
            DebugUtil.e(TAG, " abort when entry process convert!")
            return true
        }


        duration = getRecordDuration(req)
        if (duration <= 0) {
            DebugUtil.e(TAG, " duration wrong!")
            setStatus(req, ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, Code.DURATION_WRONG, "duration<=0,duration=$duration")
            deleteDirtyData(req)
            return false
        }

        if (isNeedAddTask(req)) {
            val isAddTaskSuc = addTask(req)
            if (!isAddTaskSuc) {
                DebugUtil.e(TAG, "addTask fail")
                return false
            }
        } else {
            DebugUtil.i(TAG, "No need add task,req.taskId = ${req.taskId}")
        }

        val isQueryTaskSuc = queryTask(req)
        DebugUtil.d(TAG, "queryTask isQueryTaskSuc:$isQueryTaskSuc")
        when (isQueryTaskSuc) {
            QueryStatus.QUERY_SUC -> {
                return true
            }
            QueryStatus.QUERY_ABORT -> {
                val isAbortTaskSuc = abortTask(req)
                DebugUtil.i(TAG, "isAbortTaskSuc $isAbortTaskSuc")
                return if (!isAbortTaskSuc) {
                    DebugUtil.e(TAG, "abortTask fail")
                    false
                } else {
                    true
                }
            }
            QueryStatus.QUERY_FAIL -> {
                DebugUtil.e(TAG, "queryTask fail")
                return false
            }
        }
    }

    private fun checkIsDirtyDataAddTask(req: ConvertRecord?): Boolean {
        if (req == null) {
            return true
        }
        if (req.onlyId.isNullOrEmpty()) {
            return true
        }
        if (req.uploadAllUrl.isNullOrEmpty()) {
            return true
        }
        return false
    }


    private fun checkIsDirtyDataQueryTask(req: ConvertRecord?): Boolean {
        if (req == null) {
            return true
        }
        if (req.onlyId.isNullOrEmpty()) {
            return true
        }
        if (req.taskId.isNullOrEmpty()) {
            return true
        }
        return false
    }


    private fun deleteDirtyData(req: ConvertRecord) {
        DebugUtil.e(TAG, "deleteDirtyData")
        ConvertDbUtil.deleteByRecordId(req.recordId)
    }


    private fun addTask(req: ConvertRecord): Boolean {
        DebugUtil.i(TAG, "=====  AddTask")

        val isDirtyData = checkIsDirtyDataAddTask(req)
        if (isDirtyData) {
            DebugUtil.e(TAG, "objectURL is null!")
            deleteDirtyData(req)
            setStatus(req, ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, Code.OBJECTURL_IS_NULL, "addTask isDirtyData")
            return false
        }

        try {
            val addResult = mOkhttpHelper.addTask(
                    req.onlyId!!,
                    req.uploadAllUrl!!,
                    "0",
                    true,
                    duration,
                    false,
                    getRoleTypeOn(),
                    getRoleNum())
            if (addResult.httpData == null) {
                DebugUtil.e(TAG, "DATA_ADDTASK!")
                setStatus(req, ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, Code.DATA_ADDTASK, "httpData is null, ${addResult.httpMessage}")
                return false
            }
            if (addResult.httpData.data.taskID.isNullOrEmpty()) {
                DebugUtil.e(TAG, "taskID is null!")
                setStatus(req, ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, Code.DATA_ADDTASK, "data?.taskID is null")
                return false
            }
            req.taskId = addResult.httpData.data.taskID
            val updateCount = ConvertDbUtil.updateConvertTaskId(req.recordId, req.taskId)
            DebugUtil.i(TAG, "req.taskId = ${req.taskId}, updateCount:$updateCount")
            /*
             * 转文本保存serverPlanCode 1:北研所 3:讯飞
             */
            val serverPlanCode = addResult.httpData.serverPlanCode
            ConvertDbUtil.updateServerPlanCode(req.recordId, serverPlanCode)
            ConvertDbUtil.setConvertServicePlanCode(serverPlanCode)
            setStatus(req, ConvertStatus.CONVERT_STATUS_ADD_TASK_SUC)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "HTTP_ADDTASK!2")
            handleException(req, e)
            return false
        }
        return true
    }


    enum class QueryStatus {
        QUERY_FAIL,
        QUERY_SUC,
        QUERY_ABORT,
    }


    private fun queryTask(req: ConvertRecord): QueryStatus {
        setStatus(req, ConvertStatus.CONVERT_STATUS_QUERY_TASK)

        val isDirtyData = checkIsDirtyDataQueryTask(req)
        if (isDirtyData) {
            deleteDirtyData(req)
            setStatus(req, ConvertStatus.CONVERT_STATUS_QUERY_FAIL, Code.CONVERT_TASKID_IS_NULL, "queryTask isDirtyData")
            return QueryStatus.QUERY_FAIL
        }

        var times = 1
        val timeOutLimit = genTimeOutLimit()
        DebugUtil.i(TAG, "queryTask  in: req.onlyId: ${req.onlyId}!!, req.taskId:  ${req.taskId}")
        while (!mIsAbort) {
            if (times >= timeOutLimit) {
                DebugUtil.e(TAG, "CONVERT_STATUS_QUERY_TASK_TIMEOUT!")
                setStatus(
                    req,
                    ConvertStatus.CONVERT_STATUS_QUERY_TASK_TIMEOUT,
                    Code.QUERYTASK_TIMEOUT,
                    "queryTask timeOutLimit times=$times, limit=$timeOutLimit")
                return QueryStatus.QUERY_FAIL
            }
            try {
                Thread.sleep(Constant.LONG_1000)
                val supportKeyWord = if (FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) { //开启分词打分
                    EXTRACT_KEY_WORD_ON
                } else { // 不开启分词打分
                    EXTRACT_KEY_WORD_OFF
                }
                val queryResult = mOkhttpHelper.queryTask(req.onlyId!!, req.taskId!!, duration, supportKeyWord)
                if (queryResult.httpData == null) {
                    DebugUtil.e(TAG, "DATA_QUREYTASK!")
                    setStatus(req, ConvertStatus.CONVERT_STATUS_QUERY_FAIL, Code.DATA_QUERYTASK, "httpData is null,${queryResult.httpMessage}")
                    return QueryStatus.QUERY_FAIL
                }
                handleQueryResult(req, queryResult.httpData)
                when (req.convertStatus) {
                    ConvertStatus.CONVERT_STATUS_QUERY_SUC -> return QueryStatus.QUERY_SUC
                    ConvertStatus.CONVERT_STATUS_QUERY_FAIL -> return QueryStatus.QUERY_FAIL
                    ConvertStatus.CONVERT_STATUS_QUERY_TASK -> {
                        times++
                    }
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "queryTask Exception!")
                handleException(req, e)
                return QueryStatus.QUERY_FAIL
            }
        }
        return QueryStatus.QUERY_ABORT
    }


    private fun abortTask(req: ConvertRecord): Boolean {
        DebugUtil.i(TAG, "===========  abortTask")
        if (req.taskId.isNullOrEmpty()) {
            DebugUtil.e(TAG, "AbortTask, convertTaskId is null!")
            setStatus(req, ConvertStatus.CONVERT_STATUS_ABORTTASK_FAIL, Code.CONVERT_TASKID_IS_NULL)
            return false
        }

        try {
            val abortResult = mOkhttpHelper.abortTask(req.onlyId!!, req.taskId!!)
            if (abortResult.httpData == null) {
                DebugUtil.e(TAG, "HTTP_ABORTTASK!--abortResult.data = null")
                setStatus(req, ConvertStatus.CONVERT_STATUS_ABORTTASK_FAIL, Code.DATA_ABORTTASK)
                return false
            }

            return if (abortResult.httpData.code == Code.TASKCODE_200) {
                setStatus(req, ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC)
                ConvertDbUtil.updateTaskIdByRecordId(req.recordId, "")
                DebugUtil.e(TAG, "aborTask True!")
                true
            } else {
                setStatus(req, ConvertStatus.CONVERT_STATUS_ABORTTASK_FAIL, Code.DATA_ABORTTASK)
                false
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "ABORTTASK Exception $e")
            setStatus(req, ConvertStatus.CONVERT_STATUS_ABORTTASK_FAIL, Code.DATA_ABORTTASK)
            //handleException(req,e)
            return false
        }
    }

    private fun handleQueryResult(req: ConvertRecord, beanConvert: BeanConvert) {
        //TODO
        val retcode = beanConvert.code
        val beanConvertText = beanConvert.data
        beanConvertText.traceId = beanConvert.traceId
        val showSwith = beanConvert.showSwitch == true
        //val showSwith =  true
        DebugUtil.i(TAG, "handleQueryResult in ! retcode:$retcode, showSwith: $showSwith")
        when (retcode) {
            Code.TASKCODE_200 -> {
                if (showSwith) {
                    ConvertDbUtil.updateCanShowSpeakerRole(req.recordId, SHOW_SWITH_TRUE)
                }
                req.convertTextfilePath = genTextfilePath(req)
                req.completeStatus = CONVERT_COMP_STATUS_COMPLETE
                handleText(beanConvertText)
                if (FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) {
                    handleKeyWords(req.recordId, beanConvertText.keyWord ?: emptyList<KeyWord>())
                }
                ConvertDbUtil.updateCompleteAndFilePathByRecordId(req.recordId, req.convertTextfilePath)
                ConvertDbUtil.updateSpeakerRoleOriginalNumber(req.recordId, beanConvertText.getSpeakerRoleNumber())
                setStatus(req, ConvertStatus.CONVERT_STATUS_QUERY_SUC)
                NewConvertResultUtil.reWriteConvertFile(BaseApplication.getAppContext(),
                    genTextfileName(req),
                    beanConvertText)
                CollectionInfoDbUtils.addConvertCollectionInfo(content = req.recordId.toString())
                convertCallback?.onConvertTextReceived(req.recordId, req.serverPlanCode, beanConvertText, showSwith, convertAiTitle)
                if (!convertAiTitle) {
                    toastCompletedConvert(req)
                }

                if ((beanConvertText.sublist?.isNotEmpty() == true)) {
                    BrowseFileAction.onFileUpdateSuccess(req.recordId, null)
                }
            }
            Code.TASKCODE_2001, Code.TASKCODE_2002 -> {
                setStatus(req, ConvertStatus.CONVERT_STATUS_QUERY_TASK)
                DebugUtil.e(TAG, beanConvert.data.process)
                if (!beanConvert.data.process.isNullOrEmpty()) {
                    convertCallback?.onConvertProgressChanged(req.recordId,
                            100,
                            beanConvert.data.process.toInt(),
                            beanConvert.serverPlanCode)
                }
            }
            else -> {
                DebugUtil.e(TAG, "DATA_QUERYTASK FAIL==> retCode:$retcode  retMsg:${beanConvert.msg}")
                deleteDirtyData(req)
                setStatus(req, ConvertStatus.CONVERT_STATUS_QUERY_FAIL, retcode, "handleQueryResult fail ${beanConvert.msg}")
            }
        }
    }

    private fun handleText(beanConvertText: BeanConvertText) {
        beanConvertText.recogText = beanConvertText.recogText.replace(NewConvertResultUtil.SPLIT_FLAG, " ")
        beanConvertText.sublist?.forEach { sub ->
            sub.recgText = sub.recgText.replace(NewConvertResultUtil.SPLIT_FLAG, " ")
        }
    }

    /**
     * 处理分词打分结果,保存到数据库
     * @param recordId 录音的媒体库id
     * @param keyWords 分词打分结果
     */
    private fun handleKeyWords(recordId: Long, keyWords: List<KeyWord>) {
        DebugUtil.d(TAG, "handleKeyWords recordId:$recordId")
        if (keyWords.isEmpty()) {
            DebugUtil.d(TAG, "handleKeyWords keyWord is empty")
            return
        }
        KeyWordDbUtils.addKeyWords(keyWords, recordId)
    }

    private fun entryConvertStatus(req: ConvertRecord): Boolean {
        if (req.uploadStatus == ConvertStatus.UPLOAD_STATUS_UPLOAD_SUC) {
            setStatus(req, ConvertStatus.CONVERT_STATUS_INIT)
            return true
        } else {
            return false
        }
    }


    private fun isNeedAddTask(req: ConvertRecord): Boolean {
        return req.taskId.isNullOrBlank()
    }


    fun cancel() {
        mIsAbort = true
    }

    private fun setStatus(req: ConvertRecord, status: Int, errorCode: Int = Code.NORMAL, errorMessage: String = "") {
        DebugUtil.i(TAG, "req.recordId = ${req.recordId}, status = $status, error = $errorCode ")
        req.convertStatus = status
        ConvertDbUtil.updateConvertStatusOnConvert(req.recordId, status)
        convertCallback?.onConvertStatusChange(
            req.recordId,
            ConvertStatus(ConvertStatus.UPLOAD_STATUS_UPLOAD_SUC, status),
            errorCode,
            errorMessage,
            convertAiTitle)
    }

    private fun genTextfileName(req: ConvertRecord): String {
        val localUri = MediaDBUtils.genUri(req.recordId)
        val mediaRecord = MediaDBUtils.getRecordFromMediaByUriId(localUri)
        val displayName = mediaRecord.displayName
        val fileName = NewConvertResultUtil.genFileName(req.recordId, displayName)
        DebugUtil.i(TAG, "fileName = $fileName")
        return fileName
    }

    private fun genTextfilePath(req: ConvertRecord): String {
        val temp = NewConvertResultUtil.genConvertTextPath(BaseApplication.getAppContext(), genTextfileName(req))
        DebugUtil.i(TAG, "genTextfilePath = $temp")
        return temp
    }

    private fun genTimeOutLimit(): Int {
        val result = max(Constant.INT_60, duration.toInt())
        DebugUtil.i(TAG, "===>genTimeOutLimit: $result")
        return result
    }

    private fun getRecordDuration(req: ConvertRecord): Long {
        // with seconds
        var duration = 0L
        try {
            val localUri = MediaDBUtils.genUri(req.recordId)
            val mediaRecord = MediaDBUtils.getRecordFromMediaByUriId(localUri)
            if (mediaRecord != null) {
                duration = ceil(mediaRecord.duration.toDouble() / Constant.INT_1000).toLong()
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, e.toString())
        } finally {
            DebugUtil.i(TAG, "duration: $duration")
        }
        DebugUtil.i(TAG, "===>genTimeOutLimit, duration:$duration")
        return duration
    }


    private fun handleException(req: ConvertRecord, e: Exception, code: Int = Code.EXCEPTION) {
        DebugUtil.e(TAG, "e:$e")
        when (e) {
            is EncryptException -> {
                setStatus(req, ConvertStatus.ENCRYPT_EXCEPTION, code, "e:$e")
                deleteDirtyData(req)
            }
            is JsonParseException -> {
                setStatus(req, ConvertStatus.JSONPARSE_EXCEPTION, code, "e:$e")
                deleteDirtyData(req)
            }
            is NetworkErrorException -> setStatus(req, ConvertStatus.NETWORKERROR_EXCEPTION, code, "e:$e")
            else -> setStatus(req, ConvertStatus.EXCEPTION, code, "e:$e")
        }
    }

    private fun toastCompletedConvert(record: ConvertRecord?) {
        val context = BaseApplication.getAppContext()
        val id = record?.recordId ?: 0
        // When converting the rename, you need to re-query the latest title.
        val newestTitle = MediaDBUtils.queryRecordById(id)?.displayName.title()
        Handler(Looper.getMainLooper()).post {
            val title = "\"$newestTitle\""
            val text = context.resources?.getString(com.soundrecorder.common.R.string.convert_end, title)
            if (text?.isNotEmpty() == true) {
                ToastManager.showShortToast(context, text)
            }
        }
    }

    private fun getRoleTypeOn(): Int {
        //todo
        // 1 is On; 0 is Off
        return 1
    }

    private fun getRoleNum(): Int {
        //todo
        return 0
    }
}