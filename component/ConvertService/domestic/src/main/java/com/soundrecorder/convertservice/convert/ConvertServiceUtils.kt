/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertServiceUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert

import android.net.Uri
import com.soundrecorder.convertservice.R
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.convertservice.okhttphelper.HeaderHelper
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.convertservice.bean.Constant
import java.io.FileInputStream
import java.security.MessageDigest
import java.util.*

@Suppress("TooGenericExceptionCaught")
object ConvertServiceUtils {
    const val TAG = "ConvertServiceUtils"

    private const val SEPARATE_FLAG = "-"

    @JvmStatic
    fun getSHA256HEX(uri: Uri): String {
        DebugUtil.e(TAG, "tempUri: $uri")
        var result = ""
        try {
            val inputStream = BaseApplication.getAppContext().getContentResolver()
                .openInputStream(uri) as FileInputStream
            val byteArray = ByteArray(Constant.BUFFER_SIZE_4096)
            DebugUtil.e(TAG, "byteArray")
            val messageDigest = MessageDigest.getInstance("SHA-256")
            var len = 0
            while (true) {
                len = inputStream.read(byteArray, 0, byteArray.size)
                if (len < 0) {
                    inputStream.close()
                    break
                }
                messageDigest.update(byteArray, 0, len)
            }
            val byteResult = messageDigest.digest()
            result = byteResult.toHexString()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "Exception")
            DebugUtil.e(TAG, e.toString())
        }
        DebugUtil.e(TAG, ":result $result")
        return result
    }

    @JvmStatic
    fun getSHA256HEX(inString: String): String {
        DebugUtil.e(TAG, "inString: $inString")
        var result = ""
        try {
            val inByte = inString.toByteArray()
            val messageDigest = MessageDigest.getInstance("SHA-256")
            val byteResult = messageDigest.digest(inByte)
            result = byteResult.toHexString()
        } catch (e: Exception) {
            DebugUtil.e(TAG, "Exception")
            DebugUtil.e(TAG, e.toString())
        }
        DebugUtil.e(TAG, ":result $result")
        return result
    }

    @JvmStatic
    fun ByteArray.toHexString(): String {
        return this.joinToString("") {
            java.lang.String.format("%02x", it)
        }
    }

    /**
     * 生成请求的id，全局唯一
     */
    @JvmStatic
    fun genRequestId(key: String): String {
        val uniqueID = UUID.randomUUID().toString()
        val result = "$key$SEPARATE_FLAG$uniqueID"
        DebugUtil.i(TAG, "RequestId: $result")
        return result
    }

    /**
     * 生成上传的key
     */
    @JvmStatic
    fun genUploadKey(recordId: Long): String {
        val tempUri = MediaDBUtils.genUri(recordId)
        val uploadKey = HeaderHelper.brand + SEPARATE_FLAG + getSHA256HEX(tempUri)
        DebugUtil.i(TAG, ":uploadKey $uploadKey")
        return uploadKey
    }
}