/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ExtractKeyWordTask
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.keyword

import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.KeyWordDbUtils
import com.soundrecorder.convertservice.convert.ConvertServiceUtils
import com.soundrecorder.convertservice.okhttphelper.IOkhttpHelper
import com.soundrecorder.convertservice.okhttphelper.OkhttpHelperImpl
import com.soundrecorder.convertservice.process.Code

/**
 * 提取关键字的任务
 */
class ExtractKeyWordTask(
    val recordId: Long,
    val callBack: IExtractKeywordCallback
) : Runnable, IRespCallback<List<KeyWord>> {

    companion object {
        private const val TAG = "ExtractKeyWordTask"

        // 没有转文本内容
        const val CODE_NO_CONVERT_CONTENT = 100

        // 服务器提取结果为空
        const val CODE_SERVER_KEY_WORD_EMPTY = 101

        // 服务器异常
        const val CODE_SERVER_ERROR = 102

        // 无网络
        const val CODE_NO_NET = 103

        // 判断成功
        const val CODE_OK = 200
    }

    private val httpHelper: IOkhttpHelper by lazy {
        OkhttpHelperImpl()
    }

    override fun run() {
        // 获取转文本内容，如果为空，则return 更新UI
        val convertText =
            callBack.getConvertText() ?: return onFailed(CODE_NO_CONVERT_CONTENT, "没有转文本内容")

        // 上传到服务器
        val keyWords = requestExtractKeyWord(convertText)

        // 处理结果
        if (keyWords.isNotEmpty()) {
            // 保存数据库，更新UI
            saveKeyWord(keyWords)
            // 更新UI
            onSuccess(keyWords)
        }
    }

    /**
     * 请求网络接口，获取关键词
     */
    private fun requestExtractKeyWord(text: List<String>): List<KeyWord> {
        // 生成请求的id
        val uploadKey = ConvertServiceUtils.genUploadKey(recordId)
        val requestId = ConvertServiceUtils.genRequestId(uploadKey)

        try {
            val resp = httpHelper.extractKeyWords(requestId, text)
            if (Code.HTTP_OK == resp.code && !resp.data.isNullOrEmpty()) {
                val keyWords = resp.data ?: emptyList()
                DebugUtil.d(TAG, "requestExtractKeyWord list size:${keyWords.size}")

                return keyWords.sortedByDescending { //从大到小排序
                    it.tfidfvalue
                }.take(8) //最多取8个
            } else {
                onFailed(CODE_SERVER_KEY_WORD_EMPTY, "关键词数据为空")
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "requestExtractKeyWord error", e)
            onFailed(CODE_SERVER_ERROR, "异常：${e.message}")
        }
        return emptyList()
    }

    /**
     * 保存关键词到数据库
     */
    private fun saveKeyWord(keyWords: List<KeyWord>) {
        KeyWordDbUtils.addKeyWords(keyWords, recordId)
    }


    /**
     * 成功提取关键词
     */
    override fun onSuccess(data: List<KeyWord>) {
        DebugUtil.d(TAG, "onSuccess ${Thread.currentThread().name} data:$data")
        callBack.onSuccess(data)
    }

    /**
     * 成功提取关键词失败
     */
    override fun onFailed(code: Int, msg: String) {
        DebugUtil.e(TAG, "onFailed ${Thread.currentThread().name} code:$code msg:$msg")
        callBack.onFailed(code, msg)
    }
}