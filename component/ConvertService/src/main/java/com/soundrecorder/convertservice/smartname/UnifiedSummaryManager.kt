/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  UnifiedSummaryManager
 * * Description: UnifiedSummaryManager
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import android.app.Activity
import android.content.Context
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.dialog.AIUnitDialog
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback
import com.soundrecorder.modulerouter.smartname.IUnifiedSummaryCallBack
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction.newAsrPluginDownloadDelegate
import com.soundrecorder.modulerouter.translate.IAsrDownloadCallback
import com.soundrecorder.translate.AIDownloadManager.Companion.DOWNLOAD_MODEL_CANCEL

class UnifiedSummaryManager : IUnifiedSummaryCallBack {
    companion object {
        private const val TAG = "UnifiedSummaryManager"
        private const val PROCESS_TIMEOUT: Long = 1000L
    }

    private var mAiUnitDialog: AIUnitDialog? = null
    private var lastTime: Long = 0

    override fun showSmartNameGuideDialog(activity: Activity, callback: IPluginDownloadCallback?) {
        val applicationContext = activity.applicationContext
        releaseAllDialog()
        mAiUnitDialog = AIUnitDialog(activity)
        mAiUnitDialog?.showSmartNameGuideDialog {
            DebugUtil.d(TAG, "showSmartNameGuideDialog, isOpen:$it")
            if (it) {
                val asrPluginDelegate = newAsrPluginDownloadDelegate()
                if (asrPluginDelegate != null) {
                    val smartPluginDownload = asrPluginDelegate.isSmartNamePluginDownload()
                    DebugUtil.d(TAG, "showSmartNameGuideDialog, smartPluginDownload:$smartPluginDownload")
                    if (smartPluginDownload) {
                        //插件已下载，直接打开开关
                        SmartNameApi.setSmartNameSwitchStatus(applicationContext, true)
                    }
                    //aiunit 插件下载
                    callback?.onDownLoadResult(true)
                } else {
                    callback?.onDownLoadResult(false)
                }
            } else {
                SmartNameApi.setSmartNameSwitchStatus(applicationContext, false)
                callback?.onDownLoadResult(false)
            }
        }
    }

    override fun showAiUnitPluginsDialog(
        context: Context,
        callback: IPluginDownloadCallback?,
        isOpenSwitch: Boolean,
        isSummaryTab: Boolean
    ) {
        val applicationContext = context.applicationContext
        val supportSmartName = SmartNameApi.checkSupportSmartName(applicationContext, forceUpdate = true)
        DebugUtil.d(TAG, "showAiUnitPluginsDialog, supportSmartName:$supportSmartName isSummaryTab:$isSummaryTab")
        if (isSummaryTab || supportSmartName) {
            //插件下载
            checkPluginsDownload(applicationContext, callback, isOpenSwitch, isSummaryTab)
        } else {
            callback?.onDownLoadResult(false)
        }
    }

    private fun checkPluginsDownload(context: Context, callback: IPluginDownloadCallback?, isOpenSwitch: Boolean, isSummaryTab: Boolean) {
        val asrPluginDelegate = newAsrPluginDownloadDelegate()
        if (asrPluginDelegate == null) {
            DebugUtil.d(TAG, "checkPluginsDownload, asrPluginDelegate is null")
            callback?.onDownLoadResult(false)
            return
        }
        val smartPluginDownload = asrPluginDelegate.isSmartNamePluginDownload()
        if (isSummaryTab) {
            callback?.onDownLoadResult(smartPluginDownload)
        }
        DebugUtil.d(TAG, "checkPluginsDownload, smartPluginDownload:$smartPluginDownload")
        if (!smartPluginDownload) {
            if (NetworkUtils.isNetworkInvalid(context)) {
                ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
                return
            }
            asrPluginDelegate.checkAndDownloadAsrUnifiedPlugins(context, isSummaryTab, object : IAsrDownloadCallback {
                override fun downloadSuccess(sceneName: String) {
                    DebugUtil.d(TAG, "checkPluginsDownload, downloadSuccess:")
                    if (System.currentTimeMillis() - lastTime < PROCESS_TIMEOUT) {
                        DebugUtil.d(TAG, "checkPluginsDownload, timeout:")
                        return
                    }
                    lastTime = System.currentTimeMillis()
                    if (isOpenSwitch) {
                        SmartNameApi.setSmartNameSwitchStatus(context, true)
                    }
                    //智能标题生成start
                    callback?.onDownLoadResult(true)
                }

                override fun downloadFail(errorMessage: String) {
                    DebugUtil.d(TAG, "checkPluginsDownload, downloadFail:$errorMessage")
                    if (errorMessage != DOWNLOAD_MODEL_CANCEL) {
                        ToastManager.showShortToast(context, com.soundrecorder.common.R.string.smart_aiunit_plugin_download_failed_retry)
                    }
                    if (isOpenSwitch) {
                        SmartNameApi.setSmartNameSwitchStatus(context, false)
                    }
                    callback?.onDownLoadResult(false)
                }
            })
        } else {
            if (isOpenSwitch) {
                val needStatistics = !SmartNameApi.isSmartNameSwitchOpen(context)
                SmartNameApi.setSmartNameSwitchStatus(context, true, needStatistics)
            }
            callback?.onDownLoadResult(true)
        }
    }

    override fun releaseAllDialog() {
        mAiUnitDialog?.dismissDialog()
        mAiUnitDialog = null
    }
}