/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ISmartNameProgressCallback
 * * Description: ISmartNameProgressCallback
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

interface ISmartNameProgressCallback {

    fun preStartSmartName(mediaId: Long)

    fun postSmartNameEnd(mediaId: Long)

    fun postCancelSmartName(mediaId: Long)
}