/***********************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved
 * * File        :  CancelableRunnable.java
 * * Description : CancelableRunnable.java
 * *              interface runnable can be canceled
 * * Version     : 1.0
 * * Date        : 2019/9/25
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * huangyuanwang  2019/9/25      1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.convert;

public interface CancelableRunnable extends <PERSON>na<PERSON> {

    /**
     * cancel the work
     */
    public void cancelWork();


}
