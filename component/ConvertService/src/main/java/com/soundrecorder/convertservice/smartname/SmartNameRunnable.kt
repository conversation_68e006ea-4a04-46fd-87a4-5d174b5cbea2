/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameRunnable
 * * Description: SmartNameRunnable
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback

class SmartNameRunnable(val mediaId: Long,  var params: SmartNameParam?) : ISmartNameRunnable {

    companion object {
        private const val TAG = "SmartNameRunnable"
    }
    private var mProcessSmartNameConvert: ProcessSmartNameConvert? = null
    private var mSmartNameUiCallback: ISmartNameCallback? = null
    private var runnableProgressCallback: ISmartNameProgressCallback? = null

    override fun registerSmartNameUiCallback(convertUiCallback: ISmartNameCallback?) {
        mSmartNameUiCallback = convertUiCallback
    }

    override fun registerProgressCallback(progressCallback: ISmartNameProgressCallback?) {
        runnableProgressCallback = progressCallback
    }

    override fun startSmartName() {
        mProcessSmartNameConvert = ProcessSmartNameConvert(mediaId, mSmartNameUiCallback, params)
        mProcessSmartNameConvert?.registerNameProgressCallback(runnableProgressCallback)
        mProcessSmartNameConvert?.initHandlerThread()
        mProcessSmartNameConvert?.doStartSmartName()
    }

    override fun cancelSmartName() {
        doCancel()
        postCancelConvert()
    }

    override fun unregisterSmartNameUiCallback() {
        mSmartNameUiCallback = null
        mProcessSmartNameConvert?.callback = null
        mProcessSmartNameConvert?.mProgressCallback = null
    }

    /**
     * real cancel convert process
     */
    private fun doCancel() {
        cancelConvert()
        mProcessSmartNameConvert?.cancel()
    }

    private fun cancelConvert() {
        if (ConvertTaskThreadManager.checkIsTaskRunning(mediaId)) {
            ConvertTaskThreadManager.cancelConvert(mediaId)
        }
    }

    private fun postCancelConvert() {
        DebugUtil.i(TAG, "postCancelConvert $mediaId")
        runnableProgressCallback?.postCancelSmartName(mediaId)
    }

    override fun release() {
        cancelSmartName()
        unregisterSmartNameUiCallback()
    }
}