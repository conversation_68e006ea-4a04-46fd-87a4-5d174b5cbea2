/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SafeLinkedList
 Description:
 Version: 1.0
 Date: 2022/8/9
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/9 1.0 create
 */

package com.soundrecorder.wavemark.wave.load

import java.util.LinkedList

class WaveSafeLinkedList : LinkedList<Int>() {
    @Synchronized
    override fun add(element: Int): <PERSON><PERSON>an {
        return super.add(element)
    }

    @Synchronized
    override fun addAll(elements: Collection<Int>): Bo<PERSON>an {
        return super.addAll(elements)
    }

    @Synchronized
    override fun clear() {
        super.clear()
    }

    @Synchronized
    override fun removeAt(index: Int): Int {
        return super.removeAt(index)
    }

    @Synchronized
    override fun remove(element: Int): <PERSON><PERSON>an {
        return super.remove(element)
    }

    @Suppress("TooGenericExceptionCaught")
    override fun get(index: Int): Int {
        return try {
            super.get(index)
        } catch (e: Exception) {
            0
        }
    }
}
