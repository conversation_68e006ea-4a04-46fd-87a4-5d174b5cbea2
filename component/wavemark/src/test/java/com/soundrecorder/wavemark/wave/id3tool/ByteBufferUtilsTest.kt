/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ByteBufferUtilsTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.id3tool

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import java.nio.ByteBuffer

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class ByteBufferUtilsTest {

    @Test
    fun should_equals_when_extractNullTerminatedString() {
        var byteArray = byteArrayOf(0)
        var byteBuffer = ByteBuffer.wrap(byteArray)
        var result = ByteBufferUtils.extractNullTerminatedString(byteBuffer)
        Assert.assertEquals("", result)

        byteArray = byteArrayOf(0, 1)
        byteBuffer = ByteBuffer.wrap(byteArray)
        result = ByteBufferUtils.extractNullTerminatedString(byteBuffer)
        Assert.assertEquals("", result)

        byteArray = byteArrayOf(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
        byteBuffer = ByteBuffer.wrap(byteArray)
        result = ByteBufferUtils.extractNullTerminatedString(byteBuffer)
        Assert.assertEquals("", result)
    }
}