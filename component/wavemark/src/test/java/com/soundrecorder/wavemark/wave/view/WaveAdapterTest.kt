/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: WaveAdapterTest
 * Description:
 * Version: 1.0
 * Date: 2023/5/24
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/5/24 1.0 create
 */

package com.soundrecorder.wavemark.wave.view

import android.app.Activity
import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.Mockito.*
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class WaveAdapterTest {

    private var mContext: Context? = null
    private var mActivity: Activity? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(Activity::class.java).get()
    }

    @Test
    fun should_when_updateWidth() {
        val recyclerView = Mockito.mock(WaveRecyclerView::class.java)
        val adapter = WaveAdapter(mActivity, recyclerView)
        Assert.assertEquals(0, Whitebox.getInternalState(adapter, "mWidth"))

        adapter.updateWidth(1080)
        Assert.assertEquals(1080, Whitebox.getInternalState(adapter, "mWidth"))

        adapter.totalTime = 10000
        Assert.assertEquals(10000, adapter.totalTime)
    }

    @Test
    fun should_when_getItemViewType() {
        val recyclerView = Mockito.mock(WaveRecyclerView::class.java)
        val adapter = WaveAdapter(mActivity, recyclerView)
        Whitebox.setInternalState(adapter, "mTotalCount", 10)
        Assert.assertEquals(WaveAdapter.FIRST_ITEM, adapter.getItemViewType(0))
        Assert.assertEquals(WaveAdapter.NORMAL_ITEM, adapter.getItemViewType(1))
        Assert.assertEquals(WaveAdapter.LAST_ITEM, adapter.getItemViewType(9))
    }

    @Test
    fun should_when_setMarkTimeList() {
        val recyclerView = Mockito.mock(WaveRecyclerView::class.java)
        val adapter = WaveAdapter(mActivity, recyclerView)
        adapter.setMarkTimeList(mutableListOf(MarkDataBean(1000)))
        Assert.assertEquals(1, adapter.markDataList?.size)

        adapter.removeBookMark(1)
        Assert.assertEquals(1, adapter.markDataList?.size)

        adapter.removeBookMark(0)
        Assert.assertEquals(0, adapter.markDataList?.size)
    }

    @Test
    fun should_when_getItemCount() {
        val recyclerView = Mockito.mock(WaveRecyclerView::class.java)
        val adapter = WaveAdapter(mActivity, recyclerView)
        Assert.assertEquals(0, adapter.itemCount)

        adapter.updateWidth(1080)
        doReturn(10).`when`(recyclerView).fixItemCount(anyInt())
        Assert.assertEquals(10, adapter.itemCount)
    }

    @Test
    fun should_when_onCreateViewHolder() {
        val recyclerView = Mockito.mock(WaveRecyclerView::class.java)
        val waveItemView = Mockito.mock(WaveItemView::class.java)
        val adapter = WaveAdapter(mActivity, recyclerView)

        doReturn(waveItemView).`when`(recyclerView).createNewItemView(mActivity!!, recyclerView)
        var viewHolder = adapter.onCreateViewHolder(recyclerView, WaveAdapter.FIRST_ITEM)
        Assert.assertNotNull(viewHolder)

        viewHolder = adapter.onCreateViewHolder(recyclerView, WaveAdapter.NORMAL_ITEM)
        Assert.assertNotNull(viewHolder)

        viewHolder = adapter.onCreateViewHolder(recyclerView, WaveAdapter.LAST_ITEM)
        Assert.assertNotNull(viewHolder)
    }
}