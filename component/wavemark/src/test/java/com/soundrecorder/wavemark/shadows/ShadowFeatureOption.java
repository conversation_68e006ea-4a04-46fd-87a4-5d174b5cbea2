/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShadowFeatureOption
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.shadows;

import android.content.Context;

import com.soundrecorder.base.utils.FeatureOption;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(FeatureOption.class)
public class ShadowFeatureOption {

    @Implementation
    public static void loadOptions(Context context) {
    }

    @Implementation
    public static boolean isTablet() {
        return false;
    }

}
