package com.soundrecorder.wavemark.wave.load;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;
import com.soundrecorder.wavemark.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.io.File;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class SoundFileTest {
    private Context mContext;
    private static final String test = "emulated/0/Music/Recordings/Standard Recordings/1.mp3";

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_returnFalse_when_isFilenameSupported() {
        String str = "111.mp3";
        SoundFile soundFile = new SoundFile();
        Assert.assertTrue(soundFile.isFilenameSupported(str));
    }

    @Test
    public void should_returnFalse_when_readFile() throws Exception {
        SoundFile soundFile = new SoundFile();
        soundFile.setDecodedSoundFileListener(new SoundFile.DecodedSoundFileListener() {
            @Override
            public boolean decodedSoundFileFinish(boolean isFinish) {
                return false;
            }
        });
        Whitebox.setInternalState(soundFile, "isDecodeFinish", true, SoundFile.class);
        Whitebox.invokeMethod(soundFile, "readFile", new File(test), mContext);
        boolean isDecodeFinish = Whitebox.getInternalState(soundFile, "isDecodeFinish");
        Assert.assertFalse(isDecodeFinish);
    }

    @Test
    public void should_returnFalse_when_getSuffix() throws Exception {
        SoundFile soundFile = new SoundFile();
        String str = Whitebox.invokeMethod(soundFile, "getSuffix", test);
        Assert.assertEquals(str, "mp3");
    }

    @After
    public void tearDown() {
        mContext = null;
    }

}
