/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ReMarkNameAlertDialogTest
 * Description:
 * Version: 1.0
 * Date: 2023/5/23
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/5/23 1.0 create
 */

package com.soundrecorder.wavemark.mark.dialog

import android.app.Activity
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.wavemark.R
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowToast

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class ReMarkNameAlertDialogTest {
    private var mActivity: Activity? = null
    private var mReMarkNameAlertDialog: ReMarkNameAlertDialog? = null

    @Before
    fun setUp() {
        mActivity = Robolectric.buildActivity(Activity::class.java).get()
        mReMarkNameAlertDialog = ReMarkNameAlertDialog(mActivity!!, "11", {})
    }

    @Test
    fun show() {
        mReMarkNameAlertDialog?.show()
        Assert.assertNotNull(mReMarkNameAlertDialog?.getEditText())
        Assert.assertEquals(
            mActivity!!.getString(R.string.talkback_input_flag_name),
            mReMarkNameAlertDialog?.getEditText()?.contentDescription
        )

        mReMarkNameAlertDialog?.onCancel()
    }

    @Test
    fun getTitleText() {
        Assert.assertEquals(
            R.string.rename_mark,
            mReMarkNameAlertDialog?.getTitleText()
        )
    }

    @Test
    fun getOriginalContent() {
        Assert.assertEquals(
            "11",
            mReMarkNameAlertDialog?.getOriginalContent()
        )
    }

    @Test
    fun onSave() {
        Assert.assertNull(ShadowToast.getLatestToast())
        mReMarkNameAlertDialog?.onSave()
        Assert.assertNotNull(ShadowToast.getLatestToast())
    }
}