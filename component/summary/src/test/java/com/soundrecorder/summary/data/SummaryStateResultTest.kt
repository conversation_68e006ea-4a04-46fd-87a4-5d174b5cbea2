/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryStateResultTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/28
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/28 1.0 create
 */

package com.soundrecorder.summary.data

import android.os.Build
import androidx.core.os.bundleOf
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.summary.shadow.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SummaryStateResultTest {
    @Test
    fun should_null_when_fromBundle() {
        Assert.assertNull(SummaryStateResult.fromBundle(1, null))
    }

    @Test
    fun should_notNull_when_fromBundle() {
        Assert.assertNotNull(SummaryStateResult.fromBundle(1, bundleOf()))

        val bundle = bundleOf("mediaId" to 1L, "curr_summary" to bundleOf())
        Assert.assertNotNull(SummaryStateResult.fromBundle(2, bundle))
        Assert.assertNotNull(SummaryStateResult.fromBundle(3, bundleOf()))
        Assert.assertNotNull(SummaryStateResult.fromBundle(5, bundleOf()))
        Assert.assertNotNull(SummaryStateResult.fromBundle(0, bundleOf()))
    }

    @Test
    fun should_notNull_when_getSummaryErrorInfoFromBundle() {
        Assert.assertNotNull(SummaryStateResult.getSummaryErrorInfoFromBundle(bundleOf()))
    }
}