<?xml version="1.0" encoding="utf-8"?>
<com.soundrecorder.summary.ui.content.SummaryContentContainer xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.oplus.anim.EffectiveAnimationView
        android:id="@+id/summary_loading"
        android:layout_width="@dimen/dp28"
        android:layout_height="@dimen/dp20"
        android:forceDarkAllowed="false"
        android:gravity="start"
        android:visibility="gone"
        app:anim_loop="true"
        app:anim_rawRes="@raw/ic_ai_title_loading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.soundrecorder.summary.ui.content.SummaryAnimateTextView
        android:id="@+id/summary_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:fontFamily="sans-serif"
        android:forceDarkAllowed="false"
        android:includeFontPadding="false"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textColorHighlight="@android:color/transparent"
        android:textFontWeight="400"
        android:textSize="@dimen/sp16"
        android:visibility="gone"
        app:couiAnimateStyle="2"
        app:couiAnimateTextDelay="30"
        app:couiAnimateTextDuration="420"
        app:couiAnimateTextOffset="10"
        app:couiAnimateTextType="7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/layout_error"
        layout="@layout/layout_summary_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/card_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/summary_content" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/copyright"
        style="@style/couiTextBodyXS"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp44"
        android:ellipsize="end"
        android:forceDarkAllowed="false"
        android:gravity="center_vertical|start"
        android:includeFontPadding="false"
        android:lines="1"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textFontWeight="400"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/summary_content" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="?attr/couiColorContainer8"
        android:forceDarkAllowed="false"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/summary_tool_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/copyright" />

    <RelativeLayout
        android:id="@+id/summary_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp56"
        android:forceDarkAllowed="false"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/copy"
            android:layout_width="@dimen/dp28"
            android:layout_height="@dimen/dp28"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="@drawable/bg_image_icon"
            android:src="@drawable/ic_summary_copy" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/export"
            android:layout_width="@dimen/dp28"
            android:layout_height="@dimen/dp28"
            android:layout_alignBaseline="@id/copy"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp8"
            android:layout_toEndOf="@id/copy"
            android:background="@drawable/bg_image_icon"
            android:src="@drawable/ic_summary_export" />

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/summary_scene"
            style="@style/couiTextBodyXS"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp28"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp8"
            android:layout_toEndOf="@id/export"
            android:background="@drawable/bg_scene_select"
            android:drawableEnd="@drawable/ic_scene_select_expand"
            android:drawablePadding="@dimen/dp4"
            android:ellipsize="end"
            android:forceDarkAllowed="false"
            android:gravity="center"
            android:includeFontPadding="false"
            android:lines="1"
            android:maxWidth="120dp"
            android:paddingHorizontal="@dimen/dp8"
            android:paddingVertical="@dimen/dp4"
            android:text="@string/summary_style_detail"
            android:textColor="?attr/couiColorLabelPrimary"
            tools:ignore="RelativeOverlap" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/previous"
            android:layout_width="@dimen/dp28"
            android:layout_height="@dimen/dp28"
            android:layout_alignBaseline="@id/copy"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_toStartOf="@id/refresh"
            android:background="@drawable/bg_image_icon"
            android:src="@drawable/ic_summary_perious" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/refresh"
            android:layout_width="@dimen/dp28"
            android:layout_height="@dimen/dp28"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/bg_image_icon"
            android:src="@drawable/ic_summary_refresh" />

    </RelativeLayout>


</com.soundrecorder.summary.ui.content.SummaryContentContainer>