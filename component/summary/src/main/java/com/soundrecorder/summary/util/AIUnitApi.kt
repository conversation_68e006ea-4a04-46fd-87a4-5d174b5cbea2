/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AIUnitApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.util

import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.oplus.aiunit.core.data.UnitState
import com.oplus.aiunit.download.api.AIDownload
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.aiunit.toolkits.callback.SettingsCallback
import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.soundrecorder.base.utils.DebugUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object AIUnitApi {

    private const val TAG = "AIUnitApi"

    @JvmStatic
    fun startAIPrivacyDialogIfNeeded(fragmentActivity: FragmentActivity, callback: (agree: Boolean) -> Unit) {
        if (summaryAvailable(fragmentActivity)) {
            DebugUtil.e(TAG, "startAIPrivacyDialogIfNeeded summaryAvailable")
            startPrivacyGuide(fragmentActivity, callback)
        } else {
            callback.invoke(false)
        }
    }

    @JvmStatic
    fun summaryAvailable(context: Context): Boolean {
        return isSupportAiFunction(context, UnifiedSummaryKit.getDetectorName())
    }

    @JvmStatic
    private fun checkAIPrivacyAvailable(context: Context): Boolean {
        return AISettings.isPrivacyAvailable(context)
    }

    private fun isSupportAiFunction(context: Context, detectName: String): Boolean {
        /*
         * 后面有需要嘉保密feature判断
         */
        kotlin.runCatching {
            val detectData = AISettings.getDetectData(context, detectName)
            if (detectData.isSupport.not()) {
                return false
            }
            if (detectData.state == UnitState.STATE_UNAVAILABLE_NEED_DOWNLOAD
                || detectData.state == UnitState.STATE_AVAILABLE_AND_NEW_DOWNLOAD
            ) {
                return AIDownload.isDownloadSupport(context)
            }
            return true
        }.onFailure {
            DebugUtil.e(TAG, "error ${it.message}")
        }
        return false
    }


    @JvmStatic
    fun startPrivacyGuide(context: FragmentActivity, callback: (agree: Boolean) -> Unit) {
        context.lifecycleScope.launch {
            val available = withContext(Dispatchers.Default) {
                checkAIPrivacyAvailable(context)
            }
            if (available) {
                callback.invoke(true)
            } else {
                AISettings.startPrivacyGuide(context, object : SettingsCallback {
                    override fun onSwitch(status: Int) {
                        context.lifecycleScope.launch(Dispatchers.Main) {
                            callback.invoke(status == 1)
                        }
                    }

                    override fun onUI(status: Int) {
                        context.lifecycleScope.launch(Dispatchers.Main) {
                            if (status == 0) {
                                callback.invoke(false)
                            }
                        }
                    }

                    override fun onError(code: Int) {
                        context.lifecycleScope.launch(Dispatchers.Main) {
                            callback.invoke(false)
                        }
                    }
                }, false)
            }
        }
    }
}