/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SummaryCacheDatabase
 * Description:
 * Version: 1.0
 * Date: 2025/5/12
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/12      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

private const val DATABASE_NAME = "ai_summary.db"
private const val DATABASE_VERSION = 3

@Database(
    entities = [SummaryCacheEntity::class],
    version = DATABASE_VERSION,
    exportSchema = false
)

abstract class SummaryCacheDatabase : RoomDatabase() {
    /**
     * obtain the DAO for access AI Summary data
     */
    abstract fun summaryCacheDao(): SummaryCacheDao

    companion object {
        @Volatile
        private var databaseInstance: SummaryCacheDatabase? = null

        private val MIGRATION_1_TO_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE $SUMMARY_CACHE_DATA_TABLE_NAME ADD COLUMN $SUMMARY_CACHE_DATA_COLUMN_TITLE TEXT DEFAULT NULL")
            }
        }

        private val MIGRATION_2_TO_3 = object : Migration(2, DATABASE_VERSION) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL("ALTER TABLE $SUMMARY_CACHE_DATA_TABLE_NAME ADD COLUMN $SUMMARY_CACHE_DATA_COLUMN_LANGUAGE TEXT DEFAULT NULL")
            }
        }

        @JvmStatic
        fun getInstance(context: Context): SummaryCacheDatabase =
            databaseInstance ?: synchronized(this) {
                databaseInstance ?: buildDatabase(context.applicationContext).also {
                    databaseInstance = it
                }
            }


        @JvmStatic
        private fun buildDatabase(context: Context): SummaryCacheDatabase =
            Room.databaseBuilder(context, SummaryCacheDatabase::class.java, DATABASE_NAME)
                .enableMultiInstanceInvalidation()
                .addMigrations(MIGRATION_1_TO_2, MIGRATION_2_TO_3)
                .build()
    }
}