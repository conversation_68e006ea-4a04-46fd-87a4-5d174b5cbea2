/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConversionFileApi.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.api

import android.content.Context
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.modulerouter.summary.ConversionFileAction
import com.soundrecorder.modulerouter.summary.ConversionFileAction.CONVERSION_FILE_TO_PDF
import com.soundrecorder.modulerouter.summary.ConversionFileAction.CONVERSION_FILE_TO_TXT
import com.soundrecorder.modulerouter.summary.ConversionFileAction.CONVERSION_FILE_TO_WORD
import com.soundrecorder.summary.exportfile.ConversionFileHelper

@Component(ConversionFileAction.COMPONENT_NAME)
object ConversionFileApi {

    @JvmStatic
    @Action(CONVERSION_FILE_TO_WORD)
    fun conversionFileToWord(
        act: Context,
        targetPath: String,
        title: String,
        text: String
    ): Boolean {
        val convertFileToWord = ConversionFileHelper.convertFileToWord(act, targetPath, title, text)
        return convertFileToWord
    }

    @JvmStatic
    @Action(CONVERSION_FILE_TO_PDF)
    fun conversionFileToPdf(
        act: Context,
        targetPath: String,
        title: String,
        text: String
    ): Boolean {
        val convertFileToPdf = ConversionFileHelper.convertFileToPdf(act, targetPath, title, text)
        return convertFileToPdf
    }

    @JvmStatic
    @Action(CONVERSION_FILE_TO_TXT)
    fun conversionFileToTxt(
        act: Context,
        targetPath: String,
        title: String,
        text: String
    ) {
        ConversionFileHelper.convertFileToTxt(act, targetPath, title, text)
    }
}