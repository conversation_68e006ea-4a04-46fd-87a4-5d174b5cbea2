/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ExportDoc
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/04/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.exportfile

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import androidx.core.content.FileProvider
import com.oplus.aiunit.common.client.NoteExportClient
import com.oplus.aiunit.core.data.AIUNIT_PACKAGE_NAME
import com.oplus.aiunit.core.data.DetectName
import com.oplus.aiunit.core.data.UnitState
import com.oplus.aiunit.download.api.AIDownload
import com.oplus.aiunit.download.api.DownloadRequest
import com.oplus.aiunit.download.core.DownloadListener
import com.oplus.aiunit.toolkits.AISettings
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import org.xml.sax.helpers.AttributesImpl
import java.io.File
import java.io.FileOutputStream
import javax.xml.transform.OutputKeys
import javax.xml.transform.sax.SAXTransformerFactory
import javax.xml.transform.sax.TransformerHandler
import javax.xml.transform.stream.StreamResult

object ExportDoc {
    private const val TAG = "ExportDoc"
    private const val DEFAULT_ENCODING = "UTF-16"
    private const val DEFAULT_INDENT = "yes"
    @VisibleForTesting
    const val EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD = -2
    @VisibleForTesting
    const val EXPORT_FAILURE = -1
    @VisibleForTesting
    const val EXPORT_SUCCESS = 0

    internal object Attr {
        const val XMLNS_W = "http://schemas.openxmlformats.org/wordprocessingml/2006/main"
        const val XMLNS_R = "http://schemas.openxmlformats.org/officeDocument/2006/relationships"

        //document attr
        const val ATTR_DOCUMENT_ATTR_XMLNS_W = "xmlns:w"
        const val ATTR_DOCUMENT_ATTR_XMLNS_R = "xmlns:r"

        const val ATTR_COMMON_VALUE = "w:val"
        const val ATTR_ANCHOR_VALUE = "w:anchor"

        const val ATTR_TEXT_UNDERLINE_COLOR = "w:color"

        const val TAG_PR_W_TYPE = "w:type"

        //media attr
        const val ATTR_MEDIA_SCALE_PATTERN = "w:scalepattern"
        const val ATTR_MEDIA_ALIGN = "w:align"
        const val ATTR_MEDIA_URL = "w:url"
    }

    internal object Tag {
        //root tag
        const val TAG_ROOT_DOCUMENT = "w:document"

        //body tag
        const val TAG_BODY = "w:body"

        //paragraph root tag
        const val TAG_PARAGRAPH_ROOT = "w:p"

        //paragraph style
        const val TAG_PARAGRAPH_STYLE = "w:pPr"

        //paragraph align
        const val TAG_PARAGRAPH_ALIGNMENT = "w:jc"

        //text tag
        const val TAG_CONTENT_ROOT = "w:r"

        //text tag
        const val TAG_TEXT_STYLE = "w:rPr"

        //text size
        const val TAG_TEXT_STYLE_SIZE = "w:sz"

        //text bold
        const val TAG_TEXT_STYLE_BOLD = "w:b"

        //text bold
        const val TAG_TEXT_CONTENT = "w:t"

        //text color
        const val TAG_TEXT_STYLE_COLOR = "w:color"

        //text underline
        const val TAG_TEXT_STYLE_UNDERLINE = "w:u"

        //text hyperlink
        const val TAG_HYPERLINK = "w:hyperlink"

        //media tag
        const val TAG_MEDIA = "w:media"
    }

    internal object AIUnitState {
        const val STATE_AVAILABLE = 0
        const val STATE_AVAILABLE_NEED_DOWNLOAD = 1
        const val STATE_UNAVAILABLE = 2
    }

    internal object Value {
        const val FIT = "fit"
        const val NONE = "none"
        const val AUTO = "auto"
        const val SINGLE = "single"
        const val TRUE = "true"
        const val PNG = "png"
    }

    internal object Alignment {
        const val RIGHT = "right"
        const val LEFT = "left"
        const val CENTER = "center"
    }

    internal object Color {
        const val COLOR_DEFAULT_TEXT = "000000"
        const val COLOR_UNDERLINE = "0000FF"
    }

    internal object Text {
        //默认标题字体大小，对应二号字体
        const val TEXT_TITLE_SIZE = "22"
        const val TEXT_CONTENT_SIZE = "14"
    }


    @WorkerThread
    @JvmStatic
    fun isSupportExport(context: Context): Boolean {
        //根据能力状态获取本地定义的状态
        val detectDataState = getDetectDataState(context)
        //判断能力是否需要下载
        if (detectDataState == AIUnitState.STATE_AVAILABLE_NEED_DOWNLOAD) {
            return true
        }
        return detectDataState == AIUnitState.STATE_AVAILABLE
    }

    /**
     * context 上下文
     * targetPath 存储的文件绝对路径
     * data 要绘制的图片的路径
     */
    @JvmStatic
    fun saveToWord(
        context: Context,
        targetPath: String,
        data: ExportSummaryData,
    ): Boolean {
        val xmlFilePath = createXml(context, targetPath, data)
        val xmlFile = File(xmlFilePath)
        if (xmlFilePath.isEmpty() || xmlFile.exists().not()) {
            DebugUtil.e(TAG, "create xml failed!")
            return false
        }
        val tempDocFile = createTempDoc(targetPath) ?: run {
            DebugUtil.e(TAG, "createTempDoc failed!")
            return false
        }
        val fileProviderAuthority = "${context.packageName}.fileProvider"
        val xmlFileUri = grantUriPermission(context, xmlFilePath, fileProviderAuthority).toString()
        val docPathUri = grantUriPermission(context, tempDocFile.absolutePath, fileProviderAuthority).toString()
        val result = export(context, xmlFileUri, docPathUri)
        DebugUtil.d(
            TAG,
            "tempDocFile = ${tempDocFile.path}, ${tempDocFile.absolutePath}, ${tempDocFile.exists()}"
        )
        val state = when (result) {
            EXPORT_SUCCESS -> {
                val renameState = tempDocFile.renameTo(File(targetPath))
                scanFile(context, targetPath)
                renameState
            }

            EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD -> false
            else -> false
        }
        val delete = xmlFile.delete()
        DebugUtil.d(TAG, "saveToWord delete = $delete")
        return state
    }

    @JvmStatic
    private fun createTempDoc(targetPath: String): File? {
        val targetFile = File(targetPath)
        val parent = targetFile.parentFile ?: return null
        if (parent.exists().not()) {
            parent.mkdirs()
        }
        val tempFileName =
            "${getFileNameWithoutExtension(targetPath)}_${System.currentTimeMillis()}.docx"
        return File(parent.absolutePath, tempFileName)
    }

    @JvmStatic
    private fun grantUriPermission(context: Context, path: String, authority: String): Uri {
        val uri = FileProvider.getUriForFile(context, authority, File(path))
        val flag =
            Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
        context.grantUriPermission(AIUNIT_PACKAGE_NAME, uri, flag)
        return uri
    }

    @JvmStatic
    private fun createXml(context: Context, targetPath: String, data: ExportSummaryData): String {
        return kotlin.runCatching {
            val tff = SAXTransformerFactory.newInstance() as SAXTransformerFactory
            val handler = tff.newTransformerHandler()
            val tf = handler.transformer
            tf.setOutputProperty(OutputKeys.ENCODING, DEFAULT_ENCODING)
            tf.setOutputProperty(OutputKeys.INDENT, DEFAULT_INDENT)
            val targetFile = File(targetPath)
            val parent = targetFile.parentFile ?: return ""
            if (parent.exists().not()) {
                parent.mkdirs()
            }
            val tempXmlName =
                "${getFileNameWithoutExtension(targetPath)}_${System.currentTimeMillis()}.xml"
            val file = File(parent, tempXmlName)
            if (file.exists()) {
                DebugUtil.d(TAG, "createXml exist, delete old")
                file.delete()
            }
            val result = StreamResult(FileOutputStream(file))
            handler.setResult(result)
            handler.startDocument()
            val attr = AttributesImpl()
            addAttr(
                attr,
                hashMapOf(
                    Attr.ATTR_DOCUMENT_ATTR_XMLNS_W to Attr.XMLNS_W,
                    Attr.ATTR_DOCUMENT_ATTR_XMLNS_R to Attr.XMLNS_R
                )
            )
            handler.startElement("", "", Tag.TAG_ROOT_DOCUMENT, attr)
            attr.clear()
            handler.startElement("", "", Tag.TAG_BODY, attr)

            //绘制标题
            createTitle(data, handler, attr)
            //绘制内容
            createContent(data, handler, attr)
            //绘制图表
            val fileProviderAuthority = "${context.packageName}.fileProvider"
            createImage(context, fileProviderAuthority, data.bitmap, handler, attr)

            handler.endElement("", "", Tag.TAG_BODY)
            handler.endElement("", "", Tag.TAG_ROOT_DOCUMENT)
            handler.endDocument()

            file.path
        }.onFailure {
            DebugUtil.w(TAG, "createXml :${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    private fun createTitle(
        data: ExportSummaryData,
        handler: TransformerHandler,
        attr: AttributesImpl
    ) {
        data.title.takeIf { it.isNotEmpty() }?.let {
            createTitleElement(handler, attr, it)
        }
    }

    @JvmStatic
    private fun createContent(
        data: ExportSummaryData,
        handler: TransformerHandler,
        attr: AttributesImpl
    ) {
        addTextContent(data.summary, handler, attr)
    }

    @JvmStatic
    private fun addTextContent(
        summaryContent: SummaryContent,
        handler: TransformerHandler,
        attr: AttributesImpl
    ) {
        val content = summaryContent.content.splitByKeywords(summaryContent.htmlContent)
        attr.clear()
        handler.startElement("", "", Tag.TAG_PARAGRAPH_ROOT, attr)
        content.forEach {
            if (it.type == ContentType.Normal) {
                addNormalTextContent(it.content, handler, attr)
            } else {
                addLinkTextContent(it.content, handler, attr)
            }
        }
        handler.endElement("", "", Tag.TAG_PARAGRAPH_ROOT)
    }

    @JvmStatic
    private fun addNormalTextContent(
        content: String,
        handler: TransformerHandler,
        attr: AttributesImpl
    ) {
        attr.clear()
        handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr)
        handler.startElement("", "", Tag.TAG_TEXT_STYLE, attr)
        createElementAttr(
            handler,
            attr,
            tagName = Tag.TAG_TEXT_STYLE_SIZE,
            hashMapOf(Attr.ATTR_COMMON_VALUE to Text.TEXT_CONTENT_SIZE)
        )
        handler.endElement("", "", Tag.TAG_TEXT_STYLE)
        createElementValue(handler, attr, Tag.TAG_TEXT_CONTENT, content)
        handler.endElement("", "", Tag.TAG_CONTENT_ROOT)
    }

    @JvmStatic
    private fun addLinkTextContent(
        content: String,
        handler: TransformerHandler,
        attr: AttributesImpl
    ) {
        attr.clear()
        addAttr(
            attr,
            hashMapOf(Attr.ATTR_COMMON_VALUE to content, Attr.ATTR_ANCHOR_VALUE to content)
        )
        handler.startElement("", "", Tag.TAG_HYPERLINK, attr)
        attr.clear()
        handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr)
        handler.startElement("", "", Tag.TAG_TEXT_STYLE, attr)
        createElementAttr(
            handler,
            attr,
            Tag.TAG_TEXT_STYLE_UNDERLINE,
            hashMapOf(
                Attr.ATTR_TEXT_UNDERLINE_COLOR to Value.AUTO,
                Attr.ATTR_COMMON_VALUE to Value.SINGLE
            )
        )
        createElementAttr(
            handler,
            attr,
            Tag.TAG_TEXT_STYLE_COLOR,
            hashMapOf(
                Attr.ATTR_COMMON_VALUE to Color.COLOR_UNDERLINE,
            )
        )
        createElementAttr(
            handler,
            attr,
            Tag.TAG_TEXT_STYLE_SIZE,
            hashMapOf(
                Attr.ATTR_COMMON_VALUE to Text.TEXT_CONTENT_SIZE,
            )
        )
        handler.endElement("", "", Tag.TAG_TEXT_STYLE)

        createElementValue(handler, attr, Tag.TAG_TEXT_CONTENT, content)

        handler.endElement("", "", Tag.TAG_CONTENT_ROOT)
        handler.endElement("", "", Tag.TAG_HYPERLINK)
    }

    @JvmStatic
    @VisibleForTesting
    fun String.splitByKeywords(htmlContent: List<String>): List<Content> {
        val newHtmlContent = htmlContent.filter { it.isNotEmpty() }
        val result = mutableListOf<Content>()
        if (newHtmlContent.isEmpty()) {
            val content = Content(this, ContentType.Normal)
            result.add(content)
            return result
        }
        // 将关键词列表转为正则表达式模式
        val regexPattern = newHtmlContent.joinToString("|") { "\\Q$it\\E" }
        val regex = Regex("($regexPattern)")

        // 通过正则表达式进行分割，并保持分隔符
        val parts = this.split(regex)

        var i = 0
        for (match in regex.findAll(this)) {
            // 添加匹配之前的部分
            if (i < parts.size) {
                val previousText = parts[i].trim()
                if (previousText.isNotEmpty()) {
                    val content = Content(previousText, ContentType.Normal)
                    result.add(content)
                }
                i++
            }
            // 添加匹配到的关键词
            val content = Content(match.value.trim(), ContentType.Link)
            result.add(content)
        }

        // 添加最后一个部分（如果有）
        if (i < parts.size) {
            val remainingText = parts[i].trim()
            if (remainingText.isNotEmpty()) {
                val content = Content(remainingText, ContentType.Normal)
                result.add(content)
            }
        }
        return result
    }

    @JvmStatic
    private fun addAttr(attr: AttributesImpl, map: HashMap<String, String>? = null) {
        attr.clear()
        map?.forEach { attr.addAttribute("", "", it.key, "", it.value) }
    }

    @JvmStatic
    private fun createImage(
        context: Context,
        authority: String,
        bitmaps: List<String>,
        handler: TransformerHandler,
        attr: AttributesImpl
    ) {
        bitmaps.forEach { path ->
            attr.clear()
            handler.startElement("", "", Tag.TAG_PARAGRAPH_ROOT, attr)
            handler.startElement("", "", Tag.TAG_PARAGRAPH_STYLE, attr)
            //set paragraph style
            createElementAttr(
                handler,
                attr,
                tagName = Tag.TAG_PARAGRAPH_ALIGNMENT,
                hashMapOf(Attr.ATTR_COMMON_VALUE to Alignment.CENTER)
            )
            handler.endElement("", "", Tag.TAG_PARAGRAPH_STYLE)
            attr.clear()
            handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr)

            val file = File(path)
            val imageUri =
                FileProvider.getUriForFile(
                    context, authority, if (file.exists()) {
                        file
                    } else {
                        getDefaultFileIfAttachmentLoss(context)
                    }
                )
            val flag =
                Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
            context.grantUriPermission(AIUNIT_PACKAGE_NAME, imageUri, flag)

            //set image attr
            createElementAttr(
                handler, attr, tagName = Tag.TAG_MEDIA,
                hashMapOf(
                    Attr.TAG_PR_W_TYPE to Value.PNG,
                    Attr.ATTR_MEDIA_SCALE_PATTERN to Value.FIT,
                    Attr.ATTR_MEDIA_ALIGN to Alignment.CENTER,
                    Attr.ATTR_MEDIA_URL to imageUri.toString()
                )
            )
            handler.endElement("", "", Tag.TAG_CONTENT_ROOT)
            handler.endElement("", "", Tag.TAG_PARAGRAPH_ROOT)
        }
    }

    @JvmStatic
    @VisibleForTesting
    fun getDetectDataState(context: Context): Int {
        val detectData = AISettings.getDetectData(context, DetectName.NOTE_EXPORT_DOC)
        return when (detectData.state) {
            UnitState.STATE_AVAILABLE,
            UnitState.STATE_AVAILABLE_LOCAL,
            UnitState.STATE_AVAILABLE_INTERNET -> AIUnitState.STATE_AVAILABLE

            UnitState.STATE_UNAVAILABLE_NEED_DOWNLOAD,
            UnitState.STATE_AVAILABLE_AND_NEW_DOWNLOAD -> AIUnitState.STATE_AVAILABLE_NEED_DOWNLOAD

            else -> AIUnitState.STATE_UNAVAILABLE
        }
    }

    @JvmStatic
    @VisibleForTesting
    fun export(context: Context, xmlPath: String, docPath: String): Int {
        val state = getDetectDataState(context)
        when (state) {
            AIUnitState.STATE_AVAILABLE_NEED_DOWNLOAD -> {
                DebugUtil.d(TAG, "export, need to download")
                startForAIDownload(context)
                return EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD
            }

            AIUnitState.STATE_UNAVAILABLE -> {
                DebugUtil.d(TAG, "export, not support")
                return EXPORT_FAILURE
            }

            else -> {
                val noteExportClient = NoteExportClient(context)
                val resultCode = noteExportClient.process(xmlPath, docPath)?.resultCode
                DebugUtil.d(TAG, "export, resultCode = $resultCode")
                return if (resultCode != 0) EXPORT_FAILURE else EXPORT_SUCCESS
            }
        }
    }


    private val downloadListener = object : DownloadListener {
        override fun onCancel() {
            DebugUtil.d(TAG, "AIDownload onCancel")
        }

        override fun onFail(err: Int) {
            DebugUtil.d(TAG, "AIDownload onFail err = $err")
        }

        override fun onInstall() {
            DebugUtil.d(TAG, "AIDownload onInstall")
        }

        override fun onPrepare(fullSize: Long, offsetSize: Long) {
            DebugUtil.d(TAG, "AIDownload onPrepare")
        }

        override fun onProgress(fullSize: Long, offsetSize: Long, speed: Long) {
            DebugUtil.d(TAG, "AIDownload onProgress")
        }

        override fun onStart(fullSize: Long, offsetSize: Long) {
            DebugUtil.d(TAG, "AIDownload onStart")
        }

        override fun onSuccess(fullSize: Long, downloadSize: Long, fromBreakpoint: Boolean) {
            DebugUtil.d(TAG, "AIDownload onSuccess")
        }
    }

    /**
     * 下载AIUnit云端配置
     */
    @JvmStatic
    @VisibleForTesting
    fun startForAIDownload(context: Context) {
        AIDownload.cancelByName(DetectName.NOTE_EXPORT_DOC)
        AIDownload.start(context.applicationContext, DownloadRequest().apply {
            detectName = DetectName.NOTE_EXPORT_DOC
            enableProgressUI = true
            enableProgressCallback = true
            downloadListener = <EMAIL>
        })
    }

    /**
     * create title element
     */
    @JvmStatic
    fun createTitleElement(handler: TransformerHandler, attr: AttributesImpl, title: String) {
        val align = Alignment.CENTER
        attr.clear()
        handler.startElement("", "", Tag.TAG_PARAGRAPH_ROOT, attr)
        //create paragraph style
        handler.startElement("", "", Tag.TAG_PARAGRAPH_STYLE, attr)
        createElementAttr(
            handler,
            attr,
            Tag.TAG_PARAGRAPH_ALIGNMENT,
            hashMapOf(Attr.ATTR_COMMON_VALUE to align)
        )
        handler.endElement("", "", Tag.TAG_PARAGRAPH_STYLE)
        //create text
        attr.clear()
        handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr)
        handler.startElement("", "", Tag.TAG_TEXT_STYLE, attr)
        //set text size
        createElementAttr(
            handler,
            attr,
            tagName = Tag.TAG_TEXT_STYLE_SIZE,
            hashMapOf(Attr.ATTR_COMMON_VALUE to Text.TEXT_TITLE_SIZE)
        )
        //set text bold
        createElementAttr(
            handler,
            attr,
            tagName = Tag.TAG_TEXT_STYLE_BOLD,
            hashMapOf(Attr.ATTR_COMMON_VALUE to Value.TRUE)
        )
        handler.endElement("", "", Tag.TAG_TEXT_STYLE)
        createElementValue(handler, attr, tagName = Tag.TAG_TEXT_CONTENT, title)
        handler.endElement("", "", Tag.TAG_CONTENT_ROOT)
        handler.endElement("", "", Tag.TAG_PARAGRAPH_ROOT)
    }

    @JvmStatic
    private fun createElementAttr(
        handler: TransformerHandler,
        attr: AttributesImpl,
        tagName: String,
        map: HashMap<String, String>? = null
    ) {
        addAttr(attr, map)
        handler.startElement("", "", tagName, attr)
        handler.endElement("", "", tagName)
    }

    @JvmStatic
    private fun createElementValue(
        handler: TransformerHandler,
        attr: AttributesImpl,
        tagName: String,
        value: String
    ) {
        attr.clear()
        handler.startElement("", "", tagName, attr)
        handler.characters(value.toCharArray(), 0, value.length)
        handler.endElement("", "", tagName)
    }

    @JvmStatic
    private fun getFileNameWithoutExtension(filePath: String): String {
        return filePath.substringAfterLast('/').substringBeforeLast('.')
    }
}

enum class ContentType {
    Normal, Link
}

data class Content(val content: String, val type: ContentType)