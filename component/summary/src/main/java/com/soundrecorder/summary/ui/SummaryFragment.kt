/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/06
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionPrivacyCallback
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyAction
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SUMMARY_MIND
import com.soundrecorder.modulerouter.summary.SummaryAction
import com.soundrecorder.summary.databinding.LayoutSummaryFragmentBinding
import com.soundrecorder.summary.model.AISummaryViewModel
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.request.database.SummaryCacheDao.Companion.MAX_COUNT
import com.soundrecorder.summary.ui.content.SummaryContentView
import com.soundrecorder.summary.ui.content.callback.ISummaryFunctionCallback
import com.soundrecorder.summary.util.AIUnitApi

class SummaryFragment : Fragment() {
    companion object {
        private const val TAG = "SummaryFragment"
    }

    private var binding: LayoutSummaryFragmentBinding? = null
    private var viewModel: AISummaryViewModel? = null
    private var summaryContentView: SummaryContentView? = null
    private var mediaId: Long = 0L
    private var recordType: Int = 0
    private var recordCreateTime: Long = 0L
    private var isSelected = false
    private var warningDialog: AlertDialog? = null
    private var currentChooseTheme: SummaryTheme? = null

    private val callback: ISummaryFunctionCallback by lazy {
        object : ISummaryFunctionCallback {
            override fun onClickAgent(agents: List<SummaryAgentEvent>) {
                DebugUtil.e(TAG, "onClickAgent")
                viewModel?.updateSummaryAgent(agents)
            }

            override fun onClickRefresh() {
                viewModel?.regenerateSummary(requireContext(), true)
            }

            override fun onClickRetry() {
                viewModel?.regenerateSummary(requireContext())
            }

            override fun onClickPrevious() {
                viewModel?.switchToPreviousSummary()
            }

            override fun onClickScene(theme: SummaryTheme) {
                currentChooseTheme = theme
                summaryContentView?.onStartLoading()
                viewModel?.regenerateSummary(requireContext(), true, theme = theme)
            }

            override fun onClickNext() {
                viewModel?.switchToNextSummary()
            }

            override fun onClickEntity(view: View, entity: SummaryEntity) {
                super.onClickEntity(view, entity)
                DebugUtil.d(TAG, "onClickEntity entity = $entity")
            }
        }
    }

    fun summaryFragmentSelected() {
        DebugUtil.d(TAG, "summaryFragmentSelected")
        isSelected = true
        summaryContentView?.onStartLoading()
    }

    fun summaryFragmentUnSelected() {
        DebugUtil.d(TAG, "summaryFragmentUnSelected")
        isSelected = false
        summaryContentView?.onStopLoading()
        //viewModel?.updateSummaryAgentDB()
    }

    override fun onAttach(context: Context) {
        DebugUtil.d(TAG, "onAttach")
        super.onAttach(context)
        initBundleData()
    }

    private fun startOrResumeAISummary(activity: Activity?) {
        Log.d(TAG, "startOrResumeAISummary activity = $activity")
        activity ?: return
        summaryContentView?.onStartLoading()
        viewModel?.loadSummary(activity, mediaId, recordType, recordCreateTime)
        /*
        viewModel?.checkPluginsDownload(activity) { download ->
            if (download) {
                DebugUtil.d(TAG, "checkPluginsDownload success, thread:${Thread.currentThread()}")
                lifecycleScope.launch(Dispatchers.Main) {
                    viewModel?.loadSummary(activity, mediaId, recordType)
                }
            }
        }
         */
    }

    private fun initBundleData() {
        val bundle = arguments ?: return
        mediaId = bundle.getLong(SummaryAction.BUNDLE_MEDIA_ID)
        recordType = bundle.getInt(SummaryAction.BUNDLE_RECORD_TYPE)
        recordCreateTime = bundle.getLong(SummaryAction.BUNDLE_RECORD_MODIFY_TIME)
        DebugUtil.d(TAG, "initBundleData mediaId:$mediaId, recordType:$recordType, recordCreateTime:$recordCreateTime")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        DebugUtil.d(TAG, "onCreateView")
        val binding = LayoutSummaryFragmentBinding.bind(
            inflater.inflate(
                com.soundrecorder.summary.R.layout.layout_summary_fragment,
                container,
                false
            )
        )
        this.binding = binding
        viewModel = ViewModelProvider(this)[AISummaryViewModel::class.java]
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        DebugUtil.d(TAG, "onViewCreated")
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserver()
        checkStatement()
    }

    private fun initView() {
        summaryContentView = binding?.root?.findViewById(com.soundrecorder.summary.R.id.summary_content_view)
        summaryContentView?.setSummaryFunctionCallback(callback)
    }

    private fun initObserver() {
        initSummaryStreamObserver()
        initSummaryFullModelObserver()
        initSummaryStateObserver()
        initSummaryCountObserver()
    }

    private fun checkStatement() {
        if (BaseUtil.isEXP().not()) {
            if (PermissionUtils.hasFuncTypePermission(BaseApplication.getAppContext(), FUNC_TYPE_SUMMARY_MIND).not()) {
                val functionPrivacy =
                    PrivacyPolicyAction.newFunctionPrivacyDelegate(FUNC_TYPE_SUMMARY_MIND)
                functionPrivacy?.showFunctionPrivacyDialog(
                    requireActivity(),
                    object : IFunctionPrivacyCallback {
                        override fun onPrivacyAgreed() {
                            AIUnitApi.startPrivacyGuide(requireActivity()) {
                                if (it) {
                                    startOrResumeAISummary(activity)
                                } else {
                                    DebugUtil.d(TAG, "not agree")
                                }
                            }
                        }

                        override fun onPrivacyRejected() {
                            //go back to other tab
                        }
                    })
            } else {
                startOrResumeAISummary(activity)
            }
        } else {
            startOrResumeAISummary(activity)
        }
    }


    private fun initSummaryStreamObserver() {
        viewModel?.summaryStream?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSummaryStreamObserver = $it")
            summaryContentView?.updateStream(it.stream, animator = true, it.extra)
        }
    }

    private fun initSummaryFullModelObserver() {
        viewModel?.summaryFullModel?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSummaryFullModelObserver = $it")
            summaryContentView?.setSummaryContent(it)
        }
    }

    private fun initSummaryStateObserver() {
        viewModel?.summaryState?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSummaryStateObserver = $it")
            when (it) {
                AISummaryViewModel.SummaryState.START -> summaryContentView?.onStartSummary()
                AISummaryViewModel.SummaryState.WAIT -> summaryContentView?.onStartSummary()
                AISummaryViewModel.SummaryState.TASK_RUN -> {
                    viewModel?.currentStream?.let { currentStream ->
                        summaryContentView?.updateStream(currentStream.stream, animator = false, currentStream.extra)
                    }
                }

                AISummaryViewModel.SummaryState.OVER_TIME -> showWarningOverSummaryTimes()

                AISummaryViewModel.SummaryState.FINISH -> {
                    val summaryModel = viewModel?.summaryFinishModel ?: return@observe
                    summaryContentView?.onFinishSummary(summaryModel)
                }

                AISummaryViewModel.SummaryState.ERROR -> {
                    val summaryError = viewModel?.summaryError ?: return@observe
                    val msg = requireContext().getString(summaryError.errorMsgRes)
                    summaryContentView?.onError(summaryError.canRetry, msg)
                }

                AISummaryViewModel.SummaryState.STOPPED -> currentChooseTheme = null

                else -> DebugUtil.e(TAG, "initSummaryStateObserver it = $it")
            }
        }
    }

    private fun initSummaryCountObserver() {
        viewModel?.summaryCount?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSummaryCountObserver = $it")
            val isLastSummary = it.currentPosition == it.count
            val isOnly = it.count == 1
            val isFirstSummary = it.currentPosition == 1
            summaryContentView?.checkCurrentState(isLastSummary, isOnly, isFirstSummary)
        }
    }

    private fun showWarningOverSummaryTimes() {
        val message = requireActivity().resources.getQuantityString(
            com.soundrecorder.common.R.plurals.summary_refresh_notice_dialog_msg,
            MAX_COUNT,
            MAX_COUNT
        )
        warningDialog = COUIAlertDialogBuilder(requireActivity())
            .setTitle(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_title)
            .setMessage(message)
            .setPositiveButton(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_yes) { dialog, _ ->
                viewModel?.regenerateSummary(requireContext(), false, currentChooseTheme)
                dialog.dismiss()
            }.setNeutralButton(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_no) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }

    override fun onStop() {
        super.onStop()
        //退出界面保存数据库
        viewModel?.updateSummaryAgentDB()
    }

    override fun onDetach() {
        super.onDetach()
        warningDialog?.dismiss()
        warningDialog = null
    }

    override fun onDestroy() {
        super.onDestroy()
        currentChooseTheme = null
    }
}