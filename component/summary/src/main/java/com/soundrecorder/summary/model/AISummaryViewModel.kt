/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryViewModel
 * Description:
 * Version: 1.0
 * Date: 2025/6/4
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/6/4      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.model

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.Looper
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SUMMARY_MIND
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.request.AISummaryProcess.Companion.DATABASE_ID
import com.soundrecorder.summary.request.AISummaryProcess.Companion.IS_FINISH
import com.soundrecorder.summary.request.AISummaryProcess.Companion.IS_OVER_SIZE
import com.soundrecorder.summary.request.AISummaryProcess.Companion.IS_STOP
import com.soundrecorder.summary.request.AISummaryProcess.Companion.SUMMARY_TIME
import com.soundrecorder.summary.request.AISummaryProcess.Companion.THEME_CODE
import com.soundrecorder.summary.request.AISummaryService
import com.soundrecorder.summary.request.AISummaryServiceBinder
import com.soundrecorder.summary.request.AISummaryTaskManager
import com.soundrecorder.summary.request.IAISummaryCallback
import com.soundrecorder.summary.request.database.CHOOSE
import com.soundrecorder.summary.request.database.SummaryCacheDBHelper
import com.soundrecorder.summary.request.database.SummaryCacheDao.Companion.MAX_COUNT
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AISummaryViewModel : ViewModel() {
    companion object {
        private const val TAG = "AISummaryViewModel"
    }

    /**
     * 摘要状态枚举
     */
    enum class SummaryState {
        EMPTY,
        START,
        STREAM,
        WAIT,
        OVER_TIME,
        TASK_ALREADY_RUN,
        FINISH,
        RETRY_ERROR,
        ERROR,
        STOPPED
    }

    // 摘要状态和内容相关
    val summaryStream = MutableLiveData<SummaryStream>()
    val summaryState = MutableLiveData<SummaryState>()
    val summaryFullModel = MutableLiveData<SummaryModel>()
    val summaryCount = MutableLiveData<SummaryCountModel>()
    val permissionRequestEvent = MutableLiveData<Boolean>()

    var summaryFinishModel: SummaryModel? = null
    var summaryError: SummaryError? = null
    var summaryStop: SummaryStop? = null
    var currentStream: SummaryStream = SummaryStream("", null)
    var isRegenerate = false

    // 数据管理
    private var summaryRequestModel: SummaryRequestModel? = null

    //Service相关
    private var startBind = false
    private var alreadyBindService = false
    private var summaryService: AISummaryService? = null
    private var serviceConnection: SummaryConnection? = null

    private var changeAgents = mutableListOf<SummaryAgentEvent>()

    private val browseFileAction by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    // AI摘要回调
    private val aiSummaryCallback = object : IAISummaryCallback {

        override fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {
            DebugUtil.d(TAG, "onAISummaryStart: mediaId=$mediaId, currentMediaId=${summaryRequestModel?.mediaId}")
            if (mediaId == summaryRequestModel?.mediaId) {
                summaryState.postValue(SummaryState.START)
            }
        }

        override fun onAISummaryDataAvailable(
            mediaId: Long,
            stream: String,
            extras: Map<String, Any>?
        ) {
            if (mediaId == summaryRequestModel?.mediaId && stream.isNotEmpty()) {
                //返回流式数据
                processSummaryDataAvailable(stream, extras)
            }
        }

        override fun onAISummaryFinished(
            mediaId: Long,
            jsonResult: String,
            extras: Map<String, Any>?
        ) {
            DebugUtil.d(TAG, "onAISummaryFinished: mediaId=$mediaId, resultLength=${jsonResult.length},jsonResult:$jsonResult, extras = $extras")
            if (mediaId == summaryRequestModel?.mediaId && jsonResult.isNotEmpty()) {
                val id = (extras?.get(DATABASE_ID) as? Long) ?: return
                val time = (extras[SUMMARY_TIME] as? Long) ?: 0
                val isOverSize = (extras[IS_OVER_SIZE] as? Boolean) ?: false
                val theme = (extras[THEME_CODE] as? SummaryTheme)
                processSummaryFinish(jsonResult, id, time, isOverSize, theme)
            }
        }

        override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
            DebugUtil.e(TAG, "onAISummaryError: mediaId=$mediaId, errorCode=$errorCode, errorMsg=$errorMsg")
            if (mediaId == summaryRequestModel?.mediaId) {
                handleSummaryError(errorCode, errorMsg)
            }
        }

        override fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {
            val reason = when {
                (extras?.get(IS_STOP) == true) -> SummaryStop.REASON_STOP
                (extras?.get(IS_FINISH) == true) -> SummaryStop.REASON_FINISH
                else -> -1
            }
            DebugUtil.d(TAG, "onAISummaryStop: mediaId=$mediaId, reason=$reason")
            summaryStop = SummaryStop(reason)
            if (mediaId == summaryRequestModel?.mediaId) {
                summaryState.postValue(SummaryState.STOPPED)
            }
        }

        override fun onAISummaryWait(mediaId: Long) {
            DebugUtil.d(TAG, "onAISummaryWait: mediaId=$mediaId")
            if (mediaId == summaryRequestModel?.mediaId) {
                summaryState.postValue(SummaryState.WAIT)
            }
        }

        override fun onAISummaryTaskRunning(mediaId: Long, content: String, extras: Map<String, Any>?) {
            if (mediaId == summaryRequestModel?.mediaId) {
                currentStream = SummaryStream(content, extras)
                summaryState.postValue(SummaryState.TASK_ALREADY_RUN)
            }
        }
    }

    fun loadSummary(mediaId: Long, recordType: Int, recordTime: Long) {
        val summaryRequestModel = SummaryRequestModel(mediaId, recordType, recordTime)
        this.summaryRequestModel = summaryRequestModel
        if (checkValidMediaId().not()) {
            summaryError = SummaryError(INVALID_MEDIA, -1, false)
            summaryState.postValueSafe(SummaryState.ERROR)
            return
        }
        reset()
        viewModelScope.launch {
            //数据库查
            val success = withContext(Dispatchers.IO) {
                loadSummaryFromDataBase(mediaId)
            }
            if (success) {
                DebugUtil.e(TAG, "loadSummaryFromDataBase database success")
                return@launch
            }
            //便签查，并且插入数据
            val noteDataSuccess = withContext(Dispatchers.IO) {
                loadSummaryFromNote(mediaId)
            }
            if (noteDataSuccess) {
                DebugUtil.e(TAG, "loadSummaryFromDataBase note success")
                return@launch
            }
            //打开service，service连接成功后开始load摘要
            if (PermissionUtils.hasFuncTypePermission(BaseApplication.getAppContext(), FUNC_TYPE_SUMMARY_MIND)) {
                permissionRequestEvent.postValueSafe(false)
                bindSummaryService(summaryRequestModel)
            } else {
                permissionRequestEvent.postValueSafe(true)
            }
        }
    }

    fun continueSummary() {
        val summaryRequestModel = summaryRequestModel ?: return
        bindSummaryService(summaryRequestModel)
    }

    /**
     * 重置权限请求事件状态
     * 在权限弹框处理完成后调用，避免重复弹框
     */
    fun resetPermissionRequestEvent() {
        permissionRequestEvent.postValueSafe(false)
    }

    fun permissionDenied() {
        summaryError = SummaryError(AUTH_FAILED, errorMsg(AUTH_FAILED), false)
        summaryState.postValueSafe(SummaryState.ERROR)
        resetPermissionRequestEvent()
    }

    fun loadChooseSummary() {
        val summaryRequestModel = summaryRequestModel ?: return
        if (checkValidMediaId().not()) {
            return
        }
        val mediaId = summaryRequestModel.mediaId
        viewModelScope.launch(Dispatchers.IO) {
            val loadSuccess = loadSummaryFromDataBase(mediaId)
            if (loadSuccess.not()) {
                runOnMain {
                    <EMAIL>(SummaryState.ERROR)
                }
            }
        }
    }

    fun stopSummary(context: Context?) {
        context ?: return
        val summaryRequestModel = this.summaryRequestModel ?: return
        if (checkValidMediaId().not()) {
            DebugUtil.e(TAG, "stopSummary ivValid MediaId")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            val cancelState = if (alreadyBindService) {
                summaryService?.cancelAISummary(mediaId = summaryRequestModel.mediaId)
            } else {
                AISummaryTaskManager.cancelAISummary(mediaId = summaryRequestModel.mediaId)
            }
            DebugUtil.i(TAG, "stopSummary cancelState = $cancelState")
        }
    }

    fun isStartLoading(): Boolean {
        return summaryState.value == SummaryState.EMPTY
                || summaryState.value == SummaryState.START
                || summaryState.value == SummaryState.WAIT
    }

    fun isRunning(): Boolean {
        return summaryState.value == SummaryState.STREAM || summaryState.value == SummaryState.TASK_ALREADY_RUN
    }

    fun isFinish(): Boolean {
        return summaryState.value == SummaryState.FINISH
    }

    fun isStop(): Boolean {
        return summaryState.value == SummaryState.STOPPED
    }

    fun isError(): Boolean {
        return summaryState.value == SummaryState.ERROR
    }

    private fun checkValidMediaId(): Boolean {
        return (summaryRequestModel?.mediaId ?: -1) > 0
    }

    private fun runOnMain(callback: (() -> Unit)) {
        if (Looper.getMainLooper()?.thread?.id == Thread.currentThread().id) {
            callback.invoke()
        } else {
            viewModelScope.launch(Dispatchers.Main) {
                callback.invoke()
            }
        }
    }

    private fun loadSummaryFromDataBase(mediaId: Long): Boolean {
        val summaryCacheEntity =
            SummaryCacheDBHelper.getChooseSummaryByMediaId(mediaId) ?: return false
        val summeryContent = summaryCacheEntity.summaryContent
        DebugUtil.d(TAG, "loadSummaryFromDataBase summaryCacheEntity = $summaryCacheEntity")
        if (summeryContent.isNullOrEmpty()) {
            return false
        }
        val summaryModel = summaryCacheEntity.toSummaryModel()
        runOnMain {
            <EMAIL> = summaryModel
            <EMAIL>(summaryModel)
            DebugUtil.e(TAG, "loadSummaryFromDataBase database success")
            checkSummaryCount(summaryModel.summaryTime)
        }
        return true
    }

    private suspend fun loadSummaryFromNote(mediaId: Long): Boolean {
        val noteData = NoteDbUtils.queryNoteByMediaId(mediaId.toString()) ?: return false
        val isNoteExist = noteData.noteId?.let { browseFileAction?.isNoteExist(it) } ?: false
        DebugUtil.d(TAG, "loadSummaryFromNote isNoteExist=$isNoteExist mediaId=$mediaId")
        if (isNoteExist) {
            val summaryContent = noteData.noteContent
            if (summaryContent.isNullOrEmpty()) {
                return false
            }
            val summaryModel = SummaryModel(
                0,
                mediaId,
                summaryContent,
                emptyList(),
                emptyList(),
                emptyList(),
                summaryTime = 0L
            )
            runOnMain {
                <EMAIL> = summaryModel
                <EMAIL>(summaryModel)
            }
            //插入自己的数据库
            saveNoteDataToDataBase(mediaId, summaryModel.summary)
            checkSummaryCount(summaryModel.summaryTime)
            return true
        }
        return false
    }

    private fun saveNoteDataToDataBase(
        mediaId: Long,
        summaryContent: String
    ) {
        val state = SummaryCacheDBHelper.saveNoteDataToDatabase(mediaId, summaryContent)
        DebugUtil.d(TAG, "saveNoteDataToDataBase state = $state")
    }

    private fun bindSummaryService(summaryRequestModel: SummaryRequestModel) {
        DebugUtil.i(TAG, "bindSummaryService currentMediaId = ${summaryRequestModel.mediaId}")
        //打开service
        startSummaryService()
        bindService(summaryRequestModel)
    }

    private fun startSummaryService() {
        val serviceIntent = Intent(BaseApplication.getAppContext(), AISummaryService::class.java)
        serviceIntent.`package` = BaseApplication.getAppContext().packageName
        BaseApplication.getAppContext().startService(serviceIntent)
    }

    private fun bindService(summaryRequestModel: SummaryRequestModel): Boolean {
        if (!startBind) {
            startBind = true
            val serviceIntent = Intent(BaseApplication.getAppContext(), AISummaryService::class.java)
            serviceIntent.`package` = BaseApplication.getAppContext().packageName
            val serviceConnection = SummaryConnection(summaryRequestModel, aiSummaryCallback)
            this.serviceConnection = serviceConnection
            return BaseApplication.getAppContext().bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
        return false
    }

    inner class SummaryConnection(
        private val model: SummaryRequestModel,
        private val callback: IAISummaryCallback
    ) : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, binder: IBinder?) {
            val service = (binder as? AISummaryServiceBinder)?.service ?: run {
                DebugUtil.e(TAG, "onServiceConnected but service is null")
                return
            }
            summaryService = service
            alreadyBindService = true
            service.registerAISummaryCallback(model.mediaId, callback)
            service.startAISummary(model)
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            alreadyBindService = false
            startBind = false
            summaryService = null
        }
    }


    /**
     * 重新生成摘要
     */
    fun regenerateSummary(
        context: Context,
        checkSize: Boolean = false,
        theme: SummaryTheme? = summaryFinishModel?.theme
    ) {
        val summaryRequestModel = this.summaryRequestModel ?: return
        val currentMediaId = summaryRequestModel.mediaId
        if (checkValidMediaId().not()) {
            DebugUtil.e(TAG, "regenerateSummary, return")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            if (checkSize) {
                val summarySize =
                    SummaryCacheDBHelper.getAllSummaryByMediaId(currentMediaId).size
                DebugUtil.d(TAG, "regenerateSummary summarySize = $summarySize")
                if (summarySize >= MAX_COUNT) {
                    summaryState.postValueSafe(SummaryState.OVER_TIME)
                    return@launch
                }
            }
            updateSummaryAgentDB()
            reset()
            isRegenerate = true
            AISummaryTaskManager.cancelAISummary(mediaId = summaryRequestModel.mediaId)
            summaryRequestModel.apply { this.theme = theme }
            if (alreadyBindService) {
                summaryService?.startAISummary(summaryRequestModel)
            } else {
                bindSummaryService(summaryRequestModel)
            }
        }
    }

    fun switchToPreviousSummary() {
        DebugUtil.i(TAG, "switchToPreviousSummary")
        val currentMediaId = summaryRequestModel?.mediaId ?: return
        if (checkValidMediaId().not()) {
            DebugUtil.e(TAG, "switchToPreviousSummary, return")
            return
        }
        val currentTime = summaryFinishModel?.summaryTime ?: return
        viewModelScope.launch(Dispatchers.IO) {
            updateSummaryAgentDB()
            val summaryCacheEntity = SummaryCacheDBHelper.switchPreviousSummary(
                mediaId = currentMediaId,
                currentTimestamp = currentTime
            ) ?: return@launch
            if (summaryCacheEntity.summaryContent.isNullOrEmpty()) {
                DebugUtil.e(TAG, "switchToPreviousSummary summaryContent is null")
            }
            if (summaryCacheEntity.chooseState != CHOOSE) {
                DebugUtil.e(TAG, "switchToPreviousSummary current is not choose")
                return@launch
            }

            val model = summaryCacheEntity.toSummaryModel()
            runOnMain {
                <EMAIL> = model
                checkSummaryCount(model.summaryTime)
            }
            <EMAIL>(model)
        }
    }

    /**
     * 切换到下一个摘要版本
     */
    fun switchToNextSummary() {
        DebugUtil.i(TAG, "switchToNextSummary")
        val currentMediaId = summaryRequestModel?.mediaId ?: return
        if (checkValidMediaId().not()) {
            DebugUtil.e(TAG, "switchToNextSummary, return")
            return
        }
        val currentTime = summaryFinishModel?.summaryTime ?: return
        viewModelScope.launch(Dispatchers.IO) {
            updateSummaryAgentDB()
            val summaryCacheEntity = SummaryCacheDBHelper.switchNextSummary(
                mediaId = currentMediaId,
                currentTimestamp = currentTime
            ) ?: return@launch
            if (summaryCacheEntity.summaryContent.isNullOrEmpty()) {
                DebugUtil.e(TAG, "switchToNextSummary summaryContent is null")
            }
            if (summaryCacheEntity.chooseState != CHOOSE) {
                DebugUtil.e(TAG, "switchToNextSummary current is not choose")
                return@launch
            }
            val model = summaryCacheEntity.toSummaryModel()
            runOnMain {
                <EMAIL> = model
                checkSummaryCount(model.summaryTime)
            }
            <EMAIL>(model)
        }
    }

    fun updateSummaryAgent(agents: List<SummaryAgentEvent>) {
        DebugUtil.i(TAG, "updateSummaryAgent agents = $agents")
        changeAgents.clear()
        changeAgents.addAll(agents)
    }

    fun updateSummaryAgentDB() {
        //do something
        val model = summaryFinishModel ?: run {
            DebugUtil.e(TAG, "updateSummaryAgentDB summaryFinishModel is null")
            return
        }
        if (checkValidMediaId().not()) {
            DebugUtil.e(TAG, "updateSummaryAgentDB id is error")
            return
        }
        val run = {
            DebugUtil.d(TAG, "updateSummaryAgentDB $changeAgents")
            if (changeAgents.isNotEmpty()) {
                val agentString = GsonUtil.getGson().toJson(changeAgents)
                SummaryCacheDBHelper.updateSummaryCacheWithAgent(
                    model.id,
                    model.mediaId,
                    agentString
                )
                runOnMain { changeAgents.clear() }
            }
        }
        if (Looper.getMainLooper()?.thread?.id == Thread.currentThread().id) {
            viewModelScope.launch(Dispatchers.IO) {
                run.invoke()
            }
        } else {
            run.invoke()
        }
    }

    private fun processSummaryDataAvailable(steam: String, extras: Map<String, Any>?) {
        //返回流式数据给fragment上屏处理
        DebugUtil.d(TAG, "processSummaryDataAvailable: $steam")
        val summaryStream = SummaryStream(steam, extras)
        runOnMain {
            currentStream = summaryStream
            <EMAIL>(summaryStream)
            if (<EMAIL> != SummaryState.STREAM) {
                <EMAIL>(SummaryState.STREAM)
            }
        }
    }

    /**
     * 处理摘要结果
     */
    private fun processSummaryFinish(
        jsonResult: String,
        id: Long,
        time: Long,
        isOverSize: Boolean,
        theme: SummaryTheme?
    ) {
        val currentMediaId = summaryRequestModel?.mediaId ?: return
        val content = SummaryDataParser.parseContentInFinish(jsonResult)
        if (content.isEmpty()) {
            DebugUtil.e(TAG, "processSummaryFinish content is null")
            handleSummaryError(SERVER_ERROR, "")
            return
        }
        val agent = SummaryDataParser.parseAgent(BaseApplication.getAppContext(), content)
        val entities = SummaryDataParser.parseSummaryEntity(jsonResult)
        val extras = hashMapOf<String, Any>().apply {
            put(IS_OVER_SIZE, isOverSize)
        }
        val model = SummaryModel(
            id = id,
            mediaId = currentMediaId,
            summary = content,
            agentEvents = agent,
            entities = entities,
            summaryTrace = emptyList(),
            theme = theme,
            summaryTime = time,
            extra = extras
        )
        runOnMain {
            <EMAIL> = model
            checkSummaryCount(time)
            summaryState.postValueSafe(SummaryState.FINISH)
        }
    }

    private fun checkSummaryCount(currentSummaryTime: Long) {
        val currentMediaId = summaryRequestModel?.mediaId ?: return
        viewModelScope.launch(Dispatchers.IO) {
            val summaryList = SummaryCacheDBHelper.getAllSummaryByMediaId(currentMediaId)
            val position = summaryList.indexOfFirst { it.timeStamp == currentSummaryTime } + 1
            val summaryCount = SummaryCountModel(position, summaryList.size)
            <EMAIL>(summaryCount)
        }
    }

    /**
     * 处理摘要错误
     */
    private fun handleSummaryError(errorCode: Int, errorMsg: String?) {
        DebugUtil.e(TAG, "handleSummaryError: errorCode=$errorCode, errorMsg=$errorMsg")
        val errorMsgRes = errorMsg(errorCode)
        val canRetry = canRetry(errorCode)
        val summaryError = SummaryError(errorCode, errorMsgRes, canRetry)
        //如果是重试的话，就不会返回错误，直接查当前的
        runOnMain {
            this.summaryError = summaryError
            val state = if (isRegenerate) {
                SummaryState.RETRY_ERROR
            } else {
                SummaryState.ERROR
            }
            this.summaryState.postValueSafe(state)
        }
    }

    private fun errorMsg(errorCode: Int): Int {
        return when (errorCode) {
            RISK_CONTENT -> com.soundrecorder.common.R.string.summary_error_risk
            LANGUAGE_NOT_SUPPORT -> com.soundrecorder.common.R.string.summary_content_not_support
            CONTENT_LESS_ERROR -> com.soundrecorder.common.R.string.summary_tips_content_too_short
            FILE_PARSE_FAILED -> com.soundrecorder.common.R.string.summary_error_damage_file
            else -> com.soundrecorder.common.R.string.summary_service_failed
        }
    }

    private fun canRetry(errorCode: Int): Boolean {
        return errorCode == DURATION_MAX_UNSUPPORT
                || errorCode == REQUEST_TIMEOUT
                || errorCode == NETWORK_ERROR
                || errorCode == SERVER_ERROR
                || errorCode == PLUGIN_INIT_ERROR
                || errorCode == AI_SERVICE_ERROR
                || errorCode == AI_SERVICE_STOP
                || errorCode == AI_SERVICE_TOKEN_EXCEED
                || errorCode == VOICE_SUMMARY_INNER_ERROR
                || errorCode == LLM_CLIENT_ERROR
                || errorCode == NER_CLIENT_ERROR
                || errorCode == LLM_SUMMARY_INNER_ERROR
    }

    override fun onCleared() {
        super.onCleared()
        summaryRequestModel?.mediaId?.let {
            summaryService?.unregisterAISummaryCallback(mediaId = it, aiSummaryCallback)
        }
        summaryService = null
        changeAgents.clear()
        reset()
    }

    private fun reset() {
        summaryStop = null
        isRegenerate = false
    }
}