/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryRunnable
 * Description: 管理录音摘要Task
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

import com.soundrecorder.summary.model.SummaryRequestModel

class AISummaryRunnable(
    private val requestModel: SummaryRequestModel,
    private var aISummaryUiCallback: IAISummaryCallback
) : IAISummaryRunnable {

    //录音摘要进程 实例化
    private var mAISummaryProcess: AISummaryProcess? = null

    override fun startAISummary() {
        mAISummaryProcess = AISummaryProcess(requestModel, aISummaryUiCallback)
        mAISummaryProcess?.initHandlerThread()
        mAISummaryProcess?.doStartAISummary()
    }

    override fun getCurrentSteam(): String {
        return mAISummaryProcess?.getCurrentSteam() ?: ""
    }

    override fun getStreamExtra(): Map<String, Any>? {
        return mAISummaryProcess?.getStreamExtra()
    }

    override fun cancelAISummary() {
        doCancel()
    }

    /**
     * real cancel AISummary process
     */
    private fun doCancel() {
        mAISummaryProcess?.cancel()
    }

    override fun release() {
        mAISummaryProcess?.release()
    }
}