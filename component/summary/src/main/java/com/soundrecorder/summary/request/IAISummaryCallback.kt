/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAISummaryCallback
 * Description:提供录音摘要的启动、停止、结束、重试、错误信息返回等接口，并返回相关数据
 * Version: 1.0
 * Date: 2025/5/14
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/14      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

interface IAISummaryCallback {

    /**
     * Called when an AI summary operation starts.
     *
     * @param mediaId The ID of the media being summarized.
     * @param extras Additional data related to the summary operation.
     */
    fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {}

    /**
     * Called when an AI summary operation stops.
     *
     * @param mediaId The ID of the media being summarized.
     * @param extras Additional data related to the summary operation.
     */
    fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {}

    /**
     * Called when AI summary data is available.
     *
     * @param mediaId The ID of the media being summarized.
     * @param stream The summary data in text.
     * @param extras Additional data related to the summary operation.
     */
    fun onAISummaryDataAvailable(mediaId: Long, stream: String, extras: Map<String, Any>?) {}

    /**
     * Called when an AI summary operation finishes.
     *
     * @param mediaId The ID of the media being summarized.
     * @param jsonResult The final summary data in JSON format.
     * @param extras Additional data related to the summary operation.
     * @param time The time taken to complete the summary operation.
     */
    fun onAISummaryFinished(
        mediaId: Long,
        jsonResult: String,
        extras: Map<String, Any>?,
        id: Long = -1,
        time: Long = 0L
    ) {
    }

    /**
     * Called when an error occurs during an AI summary operation.
     *
     * @param mediaId The ID of the media being summarized.
     * @param errorCode The error code associated with the error.
     * @param errorMsg An optional error message.
     */
    fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String? = null) {}

    /**
     * Called when an AI summary operation is waiting.
     *
     * @param mediaId The ID of the media being summarized.
     */
    fun onAISummaryWait(mediaId: Long) {}

    /**
     * Called when an AI summary task is running.
     *
     * @param mediaId The ID of the media being summarized.
     * @param content The current content being processed.
     */
    fun onAISummaryTaskRunning(mediaId: Long, content: String) {}
}