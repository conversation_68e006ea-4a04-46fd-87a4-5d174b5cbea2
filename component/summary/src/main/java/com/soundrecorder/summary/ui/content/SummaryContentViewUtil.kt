/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryContentViewUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/13
 * * Author      : W9021607
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author> W9021607          <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Environment
import androidx.core.content.FileProvider
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.share.normal.note.ExportNoteUtil
import com.soundrecorder.summary.exportfile.ExportSummaryData
import com.soundrecorder.summary.exportfile.SummaryContent
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object SummaryContentViewUtil {
    private const val TAG = "SummaryContentViewUtil"

    private const val DOC_PACKAGE_NAME = "andes.oplus.documentsreader"
    private const val YOZO_CONST = "com.yozo.DispatchActivity"
    private const val APPRECOVER_DISPLAY_LIST = "com.oplus.apprecover.APPRECOVER_DISPLAY_LIST"
    private const val JUMP_TO_SYSTEM_APP = "com.oplus.apprecover"
    private const val JUMP_TO_NOTE_DETAIL_ACTION = "action.nearme.note.textnote"
    private const val GUID = "guid"
    private const val KEY_SHOW_AI_PANEL = "key_show_ai_panel"
    private const val FILE_PATH = "File_Path"
    private const val FILE_EXPORT = "Export"

    private val notePackageName = AppUtil.getNotesPackageName()

    /**
     * 准备导出数据
     */
    @JvmStatic
     fun prepareExportData(context: Context, recordTitle: String, recordContent: String): ExportSummaryData {
        val title = generateExportTitle(context, recordTitle)
        val summaryContent = SummaryContent(content = recordContent, htmlContent = emptyList())
        val bitmapList = prepareBitmapList()
        return ExportSummaryData(
            title = title,
            summary = summaryContent,
            bitmap = bitmapList
        )
    }

    /**
     * 生成导出标题
     * 优先使用录音文件的实际标题，如果获取不到则使用默认格式
     */
    @JvmStatic
     fun generateExportTitle(context: Context, recordTitle: String): String {
        return if (recordTitle.isNotEmpty()) {
            recordTitle.substringBeforeLast(".")
        } else {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            context.getString(com.soundrecorder.common.R.string.summary) + "$timestamp"
        }
    }

    /**
     * 生成导出文件路径
     * 参考项目中录音文件存储逻辑，导出到Music/Recordings/Export目录下
     * 自动处理文件名冲突，确保不会覆盖已存在的文件
     */
    @JvmStatic
     fun generateExportFilePath(context: Context, extension: String, recordTitle: String): String {
        // 获取基础标题（不包含便签冲突处理的后缀）
        val baseTitle = generateExportTitle(context, recordTitle)

        // 获取存储前缀路径（与录音文件存储方式一致),构建Music/Recordings/Export路径
        val storagePrefix = StorageManager.getInstance(context).storagePrefix
        val recordingsPath = storagePrefix + File.separator + Environment.DIRECTORY_MUSIC + File.separator + Constants.RECORDINGS
        val exportPath = recordingsPath + File.separator + FILE_EXPORT
        val exportDir = File(exportPath)
        if (!exportDir.exists()) {
            exportDir.mkdirs()
        }

        // 处理文件名冲突
        val finalFileName = resolveFileConflict(exportDir, baseTitle, extension)
        val targetFile = File(exportDir, finalFileName)

        DebugUtil.d(TAG, "generateExportFilePath: baseTitle=$baseTitle, finalFileName=$finalFileName, path=${targetFile.absolutePath}")
        return targetFile.absolutePath
    }

    /**
     * 解决文件名冲突
     */
    @JvmStatic
     fun resolveFileConflict(exportDir: File, baseTitle: String, extension: String): String {
        // 如果目录不存在，直接返回原始文件名
        if (!exportDir.exists()) {
            return "$baseTitle.$extension"
        }

        // 检查基础文件是否存在
        val baseFile = File(exportDir, "$baseTitle.$extension")
        val baseFileExists = baseFile.exists()

        // 扫描目录中所有匹配的文件，找出最大的数字后缀
        val maxSuffix = findMaxSuffixInDirectory(exportDir, baseTitle, extension)

        val finalFileName = when {
            !baseFileExists && maxSuffix == 0 -> {
                // 没有任何冲突文件，使用原始文件名
                "$baseTitle.$extension"
            }
            baseFileExists && maxSuffix == 0 -> {
                // 只有基础文件存在，新文件应该是(1)
                "$baseTitle(1).$extension"
            }
            else -> {
                // 有带数字后缀的文件，使用最大后缀+1
                "$baseTitle(${maxSuffix + 1}).$extension"
            }
        }
        return finalFileName
    }

    /**
     * 在目录中查找匹配文件的最大数字后缀
     * 使用括号格式：录音(1).mp3, 录音(2).mp3
     */
    @JvmStatic
     fun findMaxSuffixInDirectory(exportDir: File, baseTitle: String, extension: String): Int {
        var maxSuffix = 0
        var hasBaseFile = false
        val files = exportDir.listFiles() ?: return maxSuffix

        val basePattern = "$baseTitle.$extension"
        val suffixPattern = Regex("^${Regex.escape(baseTitle)}\\((\\d+)\\)\\.$extension$")

        for (file in files) {
            if (file.isFile) {
                val fileName = file.name

                // 检查是否匹配基础文件名（如：录音.docx）
                if (fileName == basePattern) {
                    hasBaseFile = true
                    DebugUtil.d(TAG, "findMaxSuffixInDirectory: found base file: $fileName")
                    continue
                }

                // 检查是否匹配带数字后缀的文件名（如：录音(2).docx）
                val matchResult = suffixPattern.find(fileName)
                if (matchResult != null) {
                    val suffixGroup = matchResult.groupValues[1]
                    try {
                        val suffix = suffixGroup.toInt()
                        maxSuffix = maxOf(maxSuffix, suffix)
                    } catch (e: NumberFormatException) {
                        DebugUtil.w(TAG, "findMaxSuffixInDirectory: invalid suffix in file: $fileName")
                    }
                }
            }
        }

        // 调试日志：显示扫描结果
        DebugUtil.d(TAG, "findMaxSuffixInDirectory: baseTitle=$baseTitle, extension=$extension, hasBaseFile=$hasBaseFile, maxSuffix=$maxSuffix")
        return maxSuffix
    }

    /**
     * 准备图片列表
     */
    @JvmStatic
    private fun prepareBitmapList(): List<String> {
        return emptyList()
    }

    /**
     * 打开Word文档文件
     */
    @JvmStatic
    fun openDocumentFile(context: Context, filePath: String) {
        val intent = Intent().apply {
            action = Intent.ACTION_VIEW
            addFlags(FLAG_ACTIVITY_NEW_TASK or FLAG_GRANT_READ_URI_PERMISSION)
            setPackage(DOC_PACKAGE_NAME)
            putExtra(KEY_SHOW_AI_PANEL, true)
            // 传入文件的绝对路径，因为文件在公共目录下
            putExtra(FILE_PATH, filePath)
            setClassName(DOC_PACKAGE_NAME, YOZO_CONST)
        }
        try {
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            DebugUtil.w(TAG, "OPPO document reader not found, trying fallback: $filePath")
            // 如果OPPO文档阅读器不可用，尝试使用系统的应用选择器
            showAppChooser(context, filePath)
        }
    }

    /**
     * 显示系统应用选择器
     */
    @JvmStatic
     fun showAppChooser(context: Context, filePath: String) {
        val file = File(filePath)
        val uri = FileProvider.getUriForFile(context, "${context.packageName}.fileProvider", file)
        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(uri, "*/*")
            addFlags(FLAG_GRANT_READ_URI_PERMISSION or FLAG_ACTIVITY_NEW_TASK)
        }
        val chooserIntent = Intent.createChooser(intent, "")
        chooserIntent.addFlags(FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(chooserIntent)
    }

    /**
     * 便签应用是否下载
     */
    @JvmStatic
    fun isNoteAppInstalled(context: Activity): Boolean {
        return try {
            context.packageManager.getPackageInfo(notePackageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    /**
     * 跳转到便签应用
     */
    @JvmStatic
     fun jumpToNote(insertUri: Uri, context: Activity) {
        val localId = ExportNoteUtil.getLocalId(insertUri)
        val intent = Intent(JUMP_TO_NOTE_DETAIL_ACTION).apply {
            setPackage(notePackageName)
            putExtra(GUID, localId)
            addFlags(FLAG_ACTIVITY_NEW_TASK or FLAG_ACTIVITY_CLEAR_TOP)
        }
        context.startActivity(intent)
    }

    /**
     * 跳转到系统找回页面
     */
    @JvmStatic
    fun jumpAppRecoverList(context: Context) {
        kotlin.runCatching {
            val intent = Intent().apply {
                action = APPRECOVER_DISPLAY_LIST
                addFlags(FLAG_ACTIVITY_NEW_TASK)
                setPackage(JUMP_TO_SYSTEM_APP)
                putExtra("fromPkg", context.packageName)
            }
            context.startActivity(intent)
        }.onFailure {
            DebugUtil.e(TAG, "jumpAppRecoverList,error ${it.message}")
        }
    }
}