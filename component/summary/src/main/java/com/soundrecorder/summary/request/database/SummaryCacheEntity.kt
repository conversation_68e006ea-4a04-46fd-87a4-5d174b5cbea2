/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SummaryCacheEntity
 * Description:SummaryCacheEntity
 * Version: 1.0
 * Date: 2025/5/12
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/12      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request.database

import androidx.annotation.Keep
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

internal const val SUMMARY_CACHE_DATA_TABLE_NAME = "summary_cache_data"
internal const val SUMMARY_CACHE_DATA_COLUMN_ID = "_id"
internal const val SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = "media_id"
internal const val SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP = "time_stamp"
internal const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_CONTENT = "summary_content"
internal const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_AGENT = "summary_agent"
internal const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_ENTITY = "summary_entity"
internal const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_TRACE = "summary_trace"
internal const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_STYLE = "summaryStyle"
internal const val SUMMARY_CACHE_DATA_COLUMN_SUMMARY_THEME = "summaryTheme"
internal const val SUMMARY_CACHE_DATA_COLUMN_CHOOSE_STATE = "choose_state"
internal const val SUMMARY_CACHE_DATA_COLUMN_TITLE = "title"
internal const val SUMMARY_CACHE_DATA_COLUMN_LANGUAGE = "language"

const val CHOOSE = 0
const val UN_CHOOSE = -1

@Keep
@Entity(
    tableName = SUMMARY_CACHE_DATA_TABLE_NAME
)
data class SummaryCacheEntity(
    //主键id，自增
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_ID) var id: Long = 0,

    //录音文件ID
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID) var mediaId: Long,

    //时间戳
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP) var timeStamp: Long,

    //选中态记录，默认为非选中：-1 选中态：0
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_CHOOSE_STATE) var chooseState: Int = UN_CHOOSE,

    //录音摘要内容json
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_SUMMARY_CONTENT) var summaryContent: String? = null,

    //录音摘要的待办
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_SUMMARY_AGENT) var summaryAgent: String? = null,

    //录音摘要的实体
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_SUMMARY_ENTITY) var summaryEntity: String? = null,

    //录音摘要溯源json, 5.1 章节的javabean转成的json字串
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_SUMMARY_TRACE) var summaryTrace: String? = null,

    //当前摘要风格，默认为无效：-1
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_SUMMARY_STYLE) var summaryStyle: Int = -1,

    //详细还是简单
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_SUMMARY_THEME) var summaryTheme: String? = null,
    //摘要标题
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_TITLE) var summaryTitle: String? = null,

    //摘要语种
    @ColumnInfo(name = SUMMARY_CACHE_DATA_COLUMN_LANGUAGE) var summaryLanguage: String? = null,
)