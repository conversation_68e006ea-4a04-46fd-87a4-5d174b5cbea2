/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: ProcessAISummary
 * Description:
 * Version: 1.0
 * Date: 2025/5/8
 * Author: ********
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * ********                         2025/5/8      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import com.oplus.recorderlog.util.GsonUtil
import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.oplus.unified.summary.sdk.callback.ISummaryInitCallback
import com.oplus.unified.summary.sdk.common.SummaryInitParam
import com.oplus.unified.summary.sdk.common.SummaryResultType
import com.oplus.unified.summary.sdk.speech.SpeechSummaryRequest
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.PrefUtil.SUMMARY_SUPPORT_THEME
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.model.CONTENT_LESS_ERROR
import com.soundrecorder.summary.model.CONVERT_TEXT_UNSUPPORT
import com.soundrecorder.summary.model.LOAD_ERROR
import com.soundrecorder.summary.model.PLUGIN_INIT_ERROR
import com.soundrecorder.summary.model.SummaryRequestModel
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.request.SummaryRecordParamGetter.SUMMARY_MIN_SIZE
import com.soundrecorder.summary.request.database.CHOOSE
import com.soundrecorder.summary.request.database.SummaryCacheDBHelper
import com.soundrecorder.summary.request.database.SummaryCacheEntity

class AISummaryProcess(
    private val requestModel: SummaryRequestModel,
    private var callback: IAISummaryCallback
) {

    companion object {
        const val IS_OVER_SIZE = "is_over_size"
        const val SUMMARY_TIME = "summary_time"
        const val DATABASE_ID = "database_id"
        const val IS_STOP = "is_stop"
        const val IS_FINISH = "is_finish"
        const val THEME_CODE = "theme_code"

        private const val TAG = "AISummaryProcess"
        private const val MSG_START_AI_SUMMARY = 1
        private const val MSG_RELEASE = 2
        private const val MAX_RETRY_COUNT = 3
    }

    private var mHandlerThread: HandlerThread? = null
    private var workHandler: Handler? = null
    private var isThreadQuit = false

    private var bizTag: String? = null
    private var mSummaryKit: UnifiedSummaryKit? = null
    private var mErrorRetryCount = 0  // 错误重试计数器

    private val mediaId = requestModel.mediaId
    private var sessionId: String = ""

    private var stream: String = ""
    private var isOverSize = false
    private var isStop = false

    private var currentStyleCode: Int = -1
    private var currentTitle = ""

    fun initHandlerThread() {
        bizTag = BaseApplication.getAppContext().packageName + "-$mediaId"
        DebugUtil.d(TAG, "initHandlerThread, bizTag:$bizTag")
        isThreadQuit = false
        mHandlerThread = HandlerThread("AISummary-$mediaId")
        mHandlerThread?.start()
        mHandlerThread?.looper?.let {
            workHandler = object : Handler(it) {
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        MSG_START_AI_SUMMARY -> processAISummary()
                        MSG_RELEASE -> release()
                    }
                }
            }
        }
    }

    private fun getBizTag(): String {
        return bizTag ?: (BaseApplication.getAppContext().packageName + "-$mediaId")
    }

    fun release() {
        DebugUtil.d(TAG, "release")
        workHandler?.post {
            DebugUtil.d(TAG, "release run")
            releaseSummaryKit()
            workHandler?.removeCallbacksAndMessages(null)
            mHandlerThread?.quitSafely()
            mHandlerThread = null
            workHandler = null
            isThreadQuit = true
            isStop = false
        }
    }

    private fun releaseSummaryKit() {
        mSummaryKit?.release(getBizTag())
        mSummaryKit = null
    }

    fun doStartAISummary() {
        DebugUtil.i(TAG, "doStartAISummary: start")
        workHandler?.sendEmptyMessage(MSG_START_AI_SUMMARY)
    }

    fun getCurrentSteam(): String {
        return stream
    }

    fun isOverSize(): Boolean {
        return isOverSize
    }

    private fun processAISummary() {
        val params = SummaryRecordParamGetter.getAISummaryRecordParam(
            requestModel.mediaId,
            requestModel.recordType,
            requestModel.recordTime
        )
        handleSummaryRequest(params)
    }

    private fun handleSummaryRequest(params: AISummaryRecordParam) {
        if (params.sessionId.isEmpty()) {
            DebugUtil.e(TAG, "handleSummaryRequest sessionId is null")
            callback.onAISummaryError(mediaId, LOAD_ERROR, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        if (params.asrContent.isEmpty() && params.dialogContent.isEmpty()) {
            DebugUtil.e(TAG, "content is null")
            callback.onAISummaryError(mediaId, CONVERT_TEXT_UNSUPPORT, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        if (params.length <= SUMMARY_MIN_SIZE) {
            DebugUtil.e(TAG, "size low")
            callback.onAISummaryError(mediaId, CONTENT_LESS_ERROR, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        isOverSize = params.isOverSize
        val summaryType = if (requestModel.theme?.style == SummaryTheme.NORMAL_SIMPLE) {
            SummaryResultType.DEFAULT
        } else {
            SummaryResultType.DETAIL
        }
        val themeCode = if (requestModel.theme?.style == SummaryTheme.NORMAL_SIMPLE) {
            SummaryTheme.NORMAL
        } else {
            requestModel.theme?.style
        }
        val retryCount = AISummaryRetryCacheTask.getCurrentRetryCount(mediaId)
        DebugUtil.i(TAG, "themeCode = ${requestModel.theme?.style}, retryCount = $retryCount, isOverSize = $isOverSize")
        val request = if (params.asrContent.isNotEmpty()) {
            SpeechSummaryRequest.createRecordSummaryRequest(
                sessionId = params.sessionId,
                content = params.asrContent,
                timeout = params.timeout,
                inputLanguage = params.inputLanguage,
                outputLanguage = params.outputLanguage,
                summaryType = summaryType,
                outputType = 0,
                enableEntityCombination = true,
                retryCount = retryCount,
                isRetry = (retryCount > 0),
                enableTheme = true,
                themeCode = themeCode
            )
        } else {
            SpeechSummaryRequest.createPhoneSummaryRequest(
                sessionId = params.sessionId,
                otherName = "",
                dialogs = params.dialogContent,
                timeout = params.timeout,
                inputLanguage = params.inputLanguage,
                outputLanguage = params.outputLanguage,
                summaryType = summaryType,
                outputType = 0,
                enableEntityCombination = true,
                retryCount = retryCount,
                isRetry = (retryCount > 0),
                enableTheme = true,
                themeCode = themeCode
            )
        }
        AISummaryRetryCacheTask.addRetryTaskToCache(params.mediaID, retryCount)
        createSummaryKit(request)
    }


    /**
     * 取消 生成摘要
     */
    fun cancel() {
        workHandler?.post {
            isStop = true
            stopSummary()
            mErrorRetryCount = 0
        }
    }

    private fun stopSummary() {
        DebugUtil.d(TAG, "stopSummaryTask, sessionId:$sessionId")
        if (sessionId.isNotEmpty()) {
            mSummaryKit?.stopTask(sessionId)
        }
    }


    private fun createSummaryKit(request: SpeechSummaryRequest) {
        sessionId = request.sessionId
        mSummaryKit = UnifiedSummaryKit(BaseApplication.getAppContext())
        workHandler?.post { callback.onAISummaryStart(mediaId, null) }
        mSummaryKit?.initKit(
            SummaryInitParam(getBizTag(), true, null), object : ISummaryInitCallback {

                override fun onSuccess(bizTag: String?, extras: Map<String, Any>?) {
                    DebugUtil.d(TAG, "initUnifiedSummary, onSuccess:$bizTag, extras:$extras")
                    startSummary(request)
                }

                override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                    DebugUtil.d(TAG, "initUnifiedSummary, onError:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg")
                    workHandler?.post {
                        callback.onAISummaryError(mediaId, errorCode, errorMsg)
                        callback.onAISummaryStop(mediaId, null)
                    }
                }
            }
        )
    }

    /**
     * 开始生成摘要,实现摘要kit相关接口
     */
    private fun startSummary(request: SpeechSummaryRequest) {
        mSummaryKit?.getSummary(request, object : IAISummarySDKCallback {

            override fun onStart(sessionId: String?, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStart, sessionId:$sessionId, extras:$extras")
            }

            override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                DebugUtil.d(TAG, "onError, sessionId:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg")
                if (<EMAIL> == sessionId) {
                    workHandler?.post {
                        if (errorCode == PLUGIN_INIT_ERROR && mErrorRetryCount <= MAX_RETRY_COUNT) {
                            mErrorRetryCount += 1
                            DebugUtil.d(TAG, "startRetryAISummary, errorRetryCount:$mErrorRetryCount")
                            startRetryAISummary(request)
                        } else {
                            callback.onAISummaryError(mediaId, errorCode, errorMsg)
                            callback.onAISummaryStop(mediaId, null)
                        }
                    }
                }
            }

            override fun onDataAvailable(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onDataAvailable sessionId:$sessionId, jsonResult:$jsonResult, " +
                        "currentStyleCode = $currentStyleCode, extras:$extras")
                if (<EMAIL> == sessionId && isStop.not()) {
                    workHandler?.post {
                        val stream = SummaryDataParser.parseContentInStream(jsonResult)
                        <EMAIL> += stream
                        val wrapExtras = hashMapOf<String, Any>().apply {
                            put(IS_OVER_SIZE, isOverSize)
                        }
                        extras?.let { wrapExtras.plus(it) }
                        callback.onAISummaryDataAvailable(mediaId, <EMAIL>, wrapExtras)

                        if (currentStyleCode == -1) {
                            val abstractStyle = SummaryDataParser.parseAbstractStyle(jsonResult)
                            val themeCode = SummaryDataParser.parseThemeCode(jsonResult)
                            <EMAIL> = if (abstractStyle == "detail" || abstractStyle.isEmpty()) {
                                themeCode
                            } else {
                                SummaryTheme.NORMAL_SIMPLE
                            }
                            storeThemeList(SummaryDataParser.parseThemeInfo(jsonResult))
                            currentTitle = SummaryDataParser.parseTitle(jsonResult)
                            DebugUtil.d(TAG, "abstractStyle = $abstractStyle, themeCode = $themeCode, currentTitle = $currentTitle")
                        }
                    }
                }
            }

            /**
             * 流式数据结束回调
             * @param sessionId 当前请求ID
             * @param jsonResult 流式数据对象的json字符串
             * @param extras 扩展保留参数
             */
            override fun onFinished(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onFinished, jsonResult:$jsonResult, extras:$extras, " +
                        "isStop:$isStop, " +
                        "currentStyleCode = $currentStyleCode, " +
                        "title = $currentTitle")
                if (<EMAIL> == sessionId && isStop.not()) {
                    workHandler?.post {
                        //成功了就写入数据库
                        val summaryTheme = SummaryTheme(currentStyleCode)
                        val id = saveSummaryToDataBase(jsonResult, summaryTheme, currentTitle)
                        val time = SummaryDataParser.parseTime(jsonResult)
                        val map = hashMapOf<String, Any>().apply {
                            this[IS_OVER_SIZE] = isOverSize
                            this[SUMMARY_TIME] = time
                            this[DATABASE_ID] = id
                            this[THEME_CODE] = summaryTheme
                        }
                        extras?.let { map.plus(it) }
                        callback.onAISummaryFinished(mediaId, jsonResult, map)
                    }
                }
            }

            override fun onStop(sessionId: String, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStop, sessionId:$sessionId, extras:$extras, isStop:$isStop")
                if (<EMAIL> == sessionId) {
                    workHandler?.post {
                        val map = hashMapOf<String, Any>().apply {
                            this[IS_STOP] = isStop
                        }
                        extras?.let { map.plus(it) }
                        callback.onAISummaryStop(mediaId, map)
                    }
                }
            }
        })
    }

    /**
     * 重试AI摘要
     */
    private fun startRetryAISummary(request: SpeechSummaryRequest) {
        releaseSummaryKit()
        createSummaryKit(request)
    }

    private fun saveSummaryToDataBase(summaryContent: String, theme: SummaryTheme, title: String): Long {
        DebugUtil.d(TAG, "saveSummaryToDataBase summaryContent = $summaryContent, theme: = $theme")
        val content = SummaryDataParser.parseContentInFinish(summaryContent)
        val entities =
            GsonUtil.getGson().toJson(SummaryDataParser.parseSummaryEntity(summaryContent))
        val agent = GsonUtil.getGson()
            .toJson(SummaryDataParser.parseAgent(BaseApplication.getAppContext(), content))
        val time = SummaryDataParser.parseTime(summaryContent)
        val summaryCache = SummaryCacheEntity(
            mediaId = mediaId,
            summaryStyle = theme.style,
            chooseState = CHOOSE,
            summaryContent = content,
            summaryEntity = entities,
            summaryAgent = agent,
            timeStamp = time,
            summaryTitle = title
        )
        return SummaryCacheDBHelper.addSummaryCache(summaryCache)
    }

    private fun storeThemeList(themeInfo: String) {
        if (themeInfo.isEmpty()) {
            return
        }
        PrefUtil.putString(BaseApplication.getAppContext(), SUMMARY_SUPPORT_THEME, themeInfo)
    }
}