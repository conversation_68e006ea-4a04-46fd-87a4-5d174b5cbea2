/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: ProcessAISummary
 * Description:
 * Version: 1.0
 * Date: 2025/5/8
 * Author: ********
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * ********                         2025/5/8      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import com.oplus.recorderlog.util.GsonUtil
import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.oplus.unified.summary.sdk.speech.SpeechSummaryRequest
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.oplus.unified.summary.sdk.callback.ISummaryInitCallback
import com.oplus.unified.summary.sdk.common.SummaryInitParam
import com.oplus.unified.summary.sdk.common.SummaryResultType
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.model.SummaryRequestModel
import com.soundrecorder.summary.model.CONTENT_LESS_ERROR
import com.soundrecorder.summary.model.CONVERT_TEXT_UNSUPPORT
import com.soundrecorder.summary.model.LOAD_ERROR
import com.soundrecorder.summary.model.PLUGIN_INIT_ERROR
import com.soundrecorder.summary.request.SummaryRecordParamGetter.SUMMARY_MIN_SIZE
import com.soundrecorder.summary.request.database.CHOOSE
import com.soundrecorder.summary.request.database.SummaryCacheDBHelper
import com.soundrecorder.summary.request.database.SummaryCacheEntity

class AISummaryProcess(
    private val requestModel: SummaryRequestModel,
    private var callback: IAISummaryCallback
) {

    companion object {
        const val IS_OVER_SIZE = "is_over_size"
        const val SUMMARY_TIME = "summary_time"
        const val DATABASE_ID = "database_id"

        private const val TAG = "AISummaryProcess"
        private const val MSG_START_AI_SUMMARY = 1
        private const val MSG_RELEASE = 2
        private const val MAX_RETRY_COUNT = 3
    }

    private var mHandlerThread: HandlerThread? = null
    private var workHandler: Handler? = null
    private var isThreadQuit = false

    private var bizTag: String? = null
    private var mSummaryKit: UnifiedSummaryKit? = null
    private var mErrorRetryCount = 0  // 错误重试计数器

    private val mediaId = requestModel.mediaId
    private var sessionId: String = ""

    private var stream: String = ""
    private var isOverSize = false


    fun initHandlerThread() {
        bizTag = BaseApplication.getAppContext().packageName + "-$mediaId"
        DebugUtil.d(TAG, "initHandlerThread, bizTag:$bizTag")
        isThreadQuit = false
        mHandlerThread = HandlerThread("AISummary-$mediaId")
        mHandlerThread?.start()
        mHandlerThread?.looper?.let {
            workHandler = object : Handler(it) {
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        MSG_START_AI_SUMMARY -> processAISummary()
                        MSG_RELEASE -> release()
                    }
                }
            }
        }
    }

    private fun getBizTag(): String {
        return bizTag ?: (BaseApplication.getAppContext().packageName + "-$mediaId")
    }

    fun release() {
        DebugUtil.d(TAG, "release: quitHandlerThread")
        releaseSummaryKit()
        workHandler?.removeCallbacksAndMessages(null)
        mHandlerThread?.quitSafely()
        mHandlerThread = null
        workHandler = null
        isThreadQuit = true
    }

    private fun releaseSummaryKit() {
        mSummaryKit?.release(getBizTag())
        mSummaryKit = null
    }

    fun doStartAISummary() {
        DebugUtil.i(TAG, "doStartAISummary: start")
        workHandler?.sendEmptyMessage(MSG_START_AI_SUMMARY)
    }

    fun getCurrentSteam(): String {
        return stream
    }

    fun isOverSize(): Boolean {
        return isOverSize
    }

    private fun processAISummary() {
        val params = SummaryRecordParamGetter.getAISummaryRecordParam(
            requestModel.mediaId,
            requestModel.recordType,
            requestModel.recordTime
        )
        handleSummaryRequest(params)
    }

    private fun handleSummaryRequest(params: AISummaryRecordParam) {
        if (params.sessionId.isEmpty()) {
            DebugUtil.e(TAG, "handleSummaryRequest sessionId is null")
            callback.onAISummaryError(mediaId, LOAD_ERROR, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        if (params.asrContent.isEmpty() && params.dialogContent.isEmpty()) {
            DebugUtil.e(TAG, "content is null")
            callback.onAISummaryError(mediaId, CONVERT_TEXT_UNSUPPORT, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        if (params.length <= SUMMARY_MIN_SIZE) {
            DebugUtil.e(TAG, "size low")
            callback.onAISummaryError(mediaId, CONTENT_LESS_ERROR, "")
            callback.onAISummaryStop(mediaId, null)
            return
        }
        isOverSize = params.isOverSize
        val retryCount = AISummaryRetryCacheTask.getCurrentRetryCount(mediaId)
        val request = if (params.asrContent.isNotEmpty()) {
            SpeechSummaryRequest.createRecordSummaryRequest(
                sessionId = params.sessionId,
                content = params.asrContent,
                timeout = params.timeout,
                inputLanguage = params.inputLanguage,
                outputLanguage = params.outputLanguage,
                summaryType = SummaryResultType.DETAIL,
                outputType = 0,
                enableEntityCombination = true,
                retryCount = retryCount,
                isRetry = (retryCount > 0),
                abstractTheme = requestModel.theme?.theme,
                params = mapOf("enableTheme" to true, "themeVersion" to "1.0.0")
            )
        } else {
            SpeechSummaryRequest.createPhoneSummaryRequest(
                sessionId = params.sessionId,
                otherName = "",
                dialogs = params.dialogContent,
                timeout = params.timeout,
                inputLanguage = params.inputLanguage,
                outputLanguage = params.outputLanguage,
                summaryType = SummaryResultType.DETAIL,
                outputType = 0,
                enableEntityCombination = true,
                retryCount = retryCount,
                isRetry = (retryCount > 0),
                abstractTheme = requestModel.theme?.theme,
                params = mapOf("enableTheme" to true, "themeVersion" to "1.0.0")
            )
        }
        AISummaryRetryCacheTask.addRetryTaskToCache(params.mediaID, retryCount)
        DebugUtil.d(TAG, "createAISummaryRequestRetry, retryCount:$retryCount")
        createSummaryKit(request)
    }


    /**
     * 取消 生成摘要
     */
    fun cancel() {
        workHandler?.post {
            stopSummary()
            mErrorRetryCount = 0
            release()
        }
    }

    private fun stopSummary() {
        DebugUtil.d(TAG, "stopSummaryTask, sessionId:$sessionId")
        if (sessionId.isNotEmpty()) {
            mSummaryKit?.stopTask(sessionId)
        }
    }


    private fun createSummaryKit(request: SpeechSummaryRequest) {
        sessionId = request.sessionId
        mSummaryKit = UnifiedSummaryKit(BaseApplication.getAppContext())
        workHandler?.post { callback.onAISummaryStart(mediaId, null) }
        mSummaryKit?.initKit(
            SummaryInitParam(getBizTag(), true, null), object : ISummaryInitCallback {

                override fun onSuccess(bizTag: String?, extras: Map<String, Any>?) {
                    DebugUtil.d(TAG, "initUnifiedSummary, onSuccess:$bizTag, extras:$extras")
                    startSummary(request)
                }

                override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                    DebugUtil.d(
                        TAG,
                        "initUnifiedSummary, onError:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg"
                    )
                    if (<EMAIL> == sessionId) {
                        workHandler?.post {
                            callback.onAISummaryError(mediaId, errorCode, errorMsg)
                        }
                    }
                }
            }
        )
    }

    /**
     * 开始生成摘要,实现摘要kit相关接口
     */
    private fun startSummary(request: SpeechSummaryRequest) {
        mSummaryKit?.getSummary(request, object : IAISummarySDKCallback {

            override fun onStart(sessionId: String?, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStart, sessionId:$sessionId, extras:$extras")
            }

            override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                DebugUtil.d(
                    TAG,
                    "onError, sessionId:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg"
                )
                if (<EMAIL> == sessionId) {
                    workHandler?.post {
                        if (errorCode == PLUGIN_INIT_ERROR && mErrorRetryCount <= MAX_RETRY_COUNT) {
                            mErrorRetryCount += 1
                            DebugUtil.d(
                                TAG,
                                "startRetryAISummary, errorRetryCount:$mErrorRetryCount"
                            )
                            startRetryAISummary(request)
                        } else {
                            callback.onAISummaryError(mediaId, errorCode, errorMsg)
                            callback.onAISummaryStop(mediaId, null)
                        }
                    }
                }
            }

            override fun onDataAvailable(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onDataAvailable, jsonResult:$jsonResult, extras:$extras")
                if (<EMAIL> == sessionId) {
                    workHandler?.post {
                        val stream = SummaryDataParser.parseContentInStream(jsonResult)
                        <EMAIL> += stream
                        val wrapExtras = hashMapOf<String, Any>().apply {
                            put(IS_OVER_SIZE, isOverSize)
                        }
                        extras?.let { wrapExtras.plus(it) }
                        callback.onAISummaryDataAvailable(mediaId, stream, wrapExtras)
                    }
                }
            }

            /**
             * 流式数据结束回调
             * @param sessionId 当前请求ID
             * @param jsonResult 流式数据对象的json字符串
             * @param extras 扩展保留参数
             */
            override fun onFinished(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onFinished, jsonResult:$jsonResult, extras:$extras")
                if (<EMAIL> == sessionId) {
                    workHandler?.post {
                        //成功了就写入数据库
                        val id = saveSummaryToDataBase(jsonResult)
                        val time = SummaryDataParser.parseTime(jsonResult)
                        val map = hashMapOf<String, Any>().apply {
                            this[IS_OVER_SIZE] = isOverSize
                            this[SUMMARY_TIME] = time
                            this[DATABASE_ID] = id
                        }
                        extras?.let { map.plus(it) }
                        callback.onAISummaryFinished(mediaId, jsonResult, map)
                    }
                }
            }

            override fun onStop(sessionId: String, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStop, sessionId:$sessionId, extras:$extras")
                if (<EMAIL> == sessionId) {
                    workHandler?.post {
                        callback.onAISummaryStop(mediaId, extras)
                    }
                }
            }
        })
    }

    /**
     * 重试AI摘要
     */
    private fun startRetryAISummary(request: SpeechSummaryRequest) {
        releaseSummaryKit()
        createSummaryKit(request)
    }

    private fun saveSummaryToDataBase(summaryContent: String): Long {
        DebugUtil.d(TAG, "saveSummaryToDataBase summaryContent = $summaryContent")
        val content = SummaryDataParser.parseContentInFinish(summaryContent)
        val entities =
            GsonUtil.getGson().toJson(SummaryDataParser.parseSummaryEntity(summaryContent))
        val agent = GsonUtil.getGson()
            .toJson(SummaryDataParser.parseAgent(BaseApplication.getAppContext(), summaryContent))
        val style = SummaryDataParser.parseStyle(summaryContent)
        val theme = SummaryDataParser.parseTheme(summaryContent)
        val time = SummaryDataParser.parseTime(summaryContent)
        val summaryCache = SummaryCacheEntity(
            mediaId = mediaId,
            summaryStyle = style,
            summaryTheme = theme,
            chooseState = CHOOSE,
            summaryContent = content,
            summaryEntity = entities,
            summaryAgent = agent,
            timeStamp = time,
        )
        return SummaryCacheDBHelper.addSummaryCache(summaryCache)
    }
}