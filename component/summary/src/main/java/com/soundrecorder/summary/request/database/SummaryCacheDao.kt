/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SummaryCacheDao
 * Description:
 * Version: 1.0
 * Date: 2025/5/12
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/12      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request.database

import android.util.Log
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update

@Dao
interface SummaryCacheDao {
    companion object {
        private const val MAX = 10
    }

    @Transaction
    fun insertByCheck(item: SummaryCacheEntity): Long {
        val count = countByValue(item.mediaId)
        if (count >= MAX) {
            val oldestData = getOldestData(item.mediaId)
            oldestData?.id?.let { deleteById(it) }
        }
        val newID = insert(item)
        Log.d("SummaryCacheDao", "newID = $newID")
        updateAllChooseStateWithOutInputId(item.mediaId, newID, UN_CHOOSE)
        return newID
    }

    @Query(
        "SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId " +
                "ORDER BY $SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP ASC LIMIT 1"
    )
    fun getOldestData(mediaId: Long): SummaryCacheEntity?

    @Query("SELECT COUNT(*) FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId")
    fun countByValue(mediaId: Long): Int

    @Query(
        "UPDATE $SUMMARY_CACHE_DATA_TABLE_NAME SET $SUMMARY_CACHE_DATA_COLUMN_CHOOSE_STATE = :target " +
                "WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId AND $SUMMARY_CACHE_DATA_COLUMN_ID != :inputID"
    )
    fun updateAllChooseStateWithOutInputId(mediaId: Long, inputID: Long, target: Int): Int

    @Query(
        "UPDATE $SUMMARY_CACHE_DATA_TABLE_NAME SET $SUMMARY_CACHE_DATA_COLUMN_CHOOSE_STATE = :target " +
                "WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId AND $SUMMARY_CACHE_DATA_COLUMN_ID = :inputID"
    )
    fun updateChooseState(mediaId: Long, inputID: Long, target: Int): Int

    @Insert(
        entity = SummaryCacheEntity::class,
        onConflict = OnConflictStrategy.REPLACE
    )
    fun insert(item: SummaryCacheEntity): Long

    @Update(onConflict = OnConflictStrategy.REPLACE)
    fun update(item: SummaryCacheEntity): Int

    @Query("DELETE FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_ID = :id")
    fun deleteById(id: Long)

    @Query(
        "UPDATE $SUMMARY_CACHE_DATA_TABLE_NAME SET $SUMMARY_CACHE_DATA_COLUMN_SUMMARY_TRACE = :trace " +
                "WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId"
    )
    fun updateByTrace(mediaId: Long, trace: String): Int

    @Query(
        "UPDATE $SUMMARY_CACHE_DATA_TABLE_NAME SET $SUMMARY_CACHE_DATA_COLUMN_SUMMARY_AGENT = :agent " +
                "WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId AND $SUMMARY_CACHE_DATA_COLUMN_ID = :id"
    )
    fun updateByAgent(id: Long, mediaId: Long, agent: String): Int

    @Update(onConflict = OnConflictStrategy.REPLACE)
    fun update(items: Collection<SummaryCacheEntity>): Int

    @Query(
        "SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId " +
                "ORDER BY $SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP ASC"
    )
    fun query(mediaId: Long): List<SummaryCacheEntity>

    @Query(
        "SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME " +
                "WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId AND $SUMMARY_CACHE_DATA_COLUMN_CHOOSE_STATE = $CHOOSE"
    )
    fun queryCurrentSummary(mediaId: Long): List<SummaryCacheEntity>

    @Query("SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_ID = :id")
    fun queryById(id: Long): SummaryCacheEntity?

    @Transaction
    fun switchNextSummary(mediaId: Long, currentTimestamp: Long): SummaryCacheEntity? {
        val summaryCacheEntity = getNextSummary(mediaId, currentTimestamp) ?: return null
        updateChooseState(mediaId, summaryCacheEntity.id, CHOOSE)
        updateAllChooseStateWithOutInputId(mediaId, summaryCacheEntity.id, UN_CHOOSE)
        val result = queryById(summaryCacheEntity.id)
        return result
    }

    @Query(
        "SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId " +
                "AND $SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP > :currentTimestamp ORDER BY $SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP ASC LIMIT 1"
    )
    fun getNextSummary(mediaId: Long, currentTimestamp: Long): SummaryCacheEntity?


    @Transaction
    fun switchPreviousSummary(mediaId: Long, currentTimestamp: Long): SummaryCacheEntity? {
        val summaryCacheEntity = getPreviousSummary(mediaId, currentTimestamp) ?: return null
        updateChooseState(mediaId, summaryCacheEntity.id, CHOOSE)
        updateAllChooseStateWithOutInputId(mediaId, summaryCacheEntity.id, UN_CHOOSE)
        val result = queryById(summaryCacheEntity.id)
        return result
    }

    @Query(
        "SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_MEDIA_ID = :mediaId " +
                "AND $SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP < :currentTimestamp ORDER BY $SUMMARY_CACHE_DATA_COLUMN_TIME_STAMP DESC LIMIT 1"
    )
    fun getPreviousSummary(mediaId: Long, currentTimestamp: Long): SummaryCacheEntity?

    @Query("SELECT * FROM $SUMMARY_CACHE_DATA_TABLE_NAME WHERE $SUMMARY_CACHE_DATA_COLUMN_SUMMARY_CONTENT LIKE '%'||:selectKeyWord||'%'")
    fun getSummaryItemsByKeyWord(selectKeyWord: String): List<SummaryCacheEntity>
}