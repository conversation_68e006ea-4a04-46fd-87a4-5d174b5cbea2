/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConversionFileHelper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.exportfile

import android.content.Context
import com.soundrecorder.summary.exportfile.ExportPdf.saveToPdf

object ConversionFileHelper {

    fun convertFileToWord(act: Context, targetPath: String, title: String, text: String): Boolean {
        val saveToWord = ExportDoc.saveToWord(
            act, targetPath, ExportSummaryData(
                title,
                SummaryContent(text, listOf()), listOf()
            )
        )
        return saveToWord
    }

    fun convertFileToPdf(act: Context, targetPath: String, title: String, text: String): Boolean {
        val saveToPdf = ExportPdf.saveToPdf(
            act, targetPath, ExportSummaryData(
                title,
                SummaryContent(text, listOf()), listOf()
            )
        )
        return saveToPdf
    }

    fun convertFileToTxt(act: Context, targetPath: String, title: String, text: String) {
         ExportTxt.saveReport(
            act, targetPath, title, text
        )
    }
}