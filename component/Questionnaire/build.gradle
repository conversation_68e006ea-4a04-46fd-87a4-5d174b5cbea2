apply from: "../../common_flavor_build.gradle"

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
        }
        domestic {
            java.srcDirs += ['questionaire/src/main/java', 'feedback/src/main/java']
            res.srcDirs += ['questionaire/src/main/res', 'feedback/src/main/res', '../../res-strings-domestic']
            manifest.srcFile 'questionaire/src/main/AndroidManifest.xml'
        }

        export {
            java.srcDirs += ['feedback/src/main/java']
            res.srcDirs += ['feedback/src/main/res', '../../res-strings-export']
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "QUESTIONNAIRE_ID", "\"9fbd26fe608450dbc91f7d94adfd6987\""
            buildConfigField "String", "QUESTIONNAIRE_KEY", "\"PmaLVXQHRXz+5JfWvg5UfFEfoA3tL63lW2cAMZiribk=\""
            buildConfigField "String", "QUESTIONNAIRE_CODE", "\"20007\""
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "QUESTIONNAIRE_ID", "\"beacda022a81cb946ba95779137d7436\""
            buildConfigField "String", "QUESTIONNAIRE_KEY", "\"DsxAf6/yjqhB+E33hdGLBWBfDBh0TE3GaOqtCXGap3E=\""
            buildConfigField "String", "QUESTIONNAIRE_CODE", "\"20007\""
        }
    }
    compileOptions {
        sourceCompatibility prop_targetCompatibility
        targetCompatibility prop_targetCompatibility
    }
    kotlinOptions {
        jvmTarget = "${prop_targetCompatibility}"
    }
    testOptions {
        unitTests.returnDefaultValues = true
    }
}

dependencies {

    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.appcompat:appcompat:${prop_appcompatVersion}"
    implementation "com.google.android.material:material:${material_version}"

    //for coroutines
    api 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.2'
    api "org.jetbrains.kotlinx:kotlinx-coroutines-android:$kotlinx_coroutines_android_version"
    //for lifecycle
    api "androidx.lifecycle:lifecycle-viewmodel-savedstate:$lifecycleVersion"
    api "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycleVersion"
    api "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycleVersion"
    api "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion"

    //stdid
    implementation "com.oplus.stdid.sdk:sdk:$prop_stdsdkVersion"
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    compileOnly "com.oplus.sdk:addon:${prop_addonAdapterVersion}"

    // base包为必须引用的包，prop_versionName需保持一致
    compileOnly ("com.oplus.appcompat:core:${prop_versionName}")
    compileOnly ("com.oplus.appcompat:dialog:${prop_versionName}")
    compileOnly ("com.oplus.materialcolor:coui-material-color:${coui_colorVersion}")
    // WebView
    implementation 'androidx.webkit:webkit:1.4.0'

    //retrofit,cloudkit也接入了retrofit
    implementation ("com.squareup.retrofit2:retrofit:2.9.0"){
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    implementation ("com.squareup.retrofit2:converter-gson:2.9.0"){
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    implementation ("com.squareup.okhttp3:logging-interceptor:4.9.0"){
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    /*clodkit使用taphttp,为了解冲突，okhttp统一替换使用taphttp*/
    implementation ("com.heytap.nearx:taphttp:${prop_taphttp_version}")
    //room,version same with cloukit
    kapt "androidx.room:room-compiler:${room_version}"
    implementation "androidx.room:room-runtime:${room_version}"
    implementation "androidx.room:room-ktx:${room_version}"
    implementation ("io.coil-kt:coil:2.4.0"){
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation("com.squareup.okio:okio:${prop_okio_version}")

    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"
    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"

    androidTestImplementation "androidx.room:room-testing:${room_version}"

    //feedback_sdk
    domesticImplementation "com.customer.feedback.sdk:feedback-env-domestic:$prop_feedbackEnvVersion"
    domesticImplementation("com.customer.feedback.sdk:feedback-cdp:$prop_feedbackVersion") {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    exportImplementation "com.customer.feedback.sdk:feedback-env-export:$prop_feedbackEnvVersion"
    exportImplementation("com.customer.feedback.sdk:feedback-sdk:$prop_feedbackVersion") {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }

    implementation "com.github.bumptech.glide:glide:4.12.0"
    testImplementation "com.oplus.appcompat:core:${prop_versionName}"
    testImplementation "com.oplus.appcompat:dialog:${prop_versionName}"
}
