apply from: "../../common_build.gradle"
apply plugin: 'org.jetbrains.kotlin.android'

dependencies {
    implementation fileTree(include: ['*.so'], dir: 'libs')
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation "androidx.appcompat:appcompat:${prop_appcompatVersion}"
    //kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:${prop_kotlinVersion}"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-extensions:$lifecycle_extensions"
    implementation "androidx.paging:paging-runtime-ktx:$paging_version"
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    // fragment
    implementation "androidx.fragment:fragment-ktx:$prop_fragmentVersion"
    implementation project(path: ':common:RecorderLogBase')

    kaptTest "androidx.databinding:databinding-compiler:7.3.1"

    // base包为必须引用的包，prop_versionName需保持一致
    implementation ("com.oplus.appcompat:core:${prop_versionName}") {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation "com.oplus.appcompat:reddot:${prop_versionName}"
    implementation "com.oplus.appcompat:snackbar:${prop_versionName}"
    implementation "com.oplus.appcompat:cardview:${prop_versionName}"
    implementation "com.oplus.appcompat:bottomnavigation:${prop_versionName}"
    implementation "com.oplus.appcompat:recyclerview:${prop_versionName}"
    implementation "com.oplus.appcompat:dialog:${prop_versionName}"
    implementation "com.oplus.appcompat:scrollbar:${prop_versionName}"
    implementation "com.oplus.appcompat:toolbar:${prop_versionName}"
    implementation "com.oplus.appcompat:springchain:${prop_versionName}"
    implementation "com.oplus.appcompat:poplist:${prop_versionName}"
    implementation "com.oplus.appcompat:tips:${prop_versionName}"
    implementation "com.oplus.appcompat:card:${prop_versionName}"
    implementation "com.oplus.appcompat:preference:${prop_versionName}"
    implementation "com.oplus.appcompat:input:${prop_versionName}"
    implementation "com.oplus.appcompat:seekbar:${prop_versionName}"
    implementation "com.oplus.appcompat:rotateview:${prop_versionName}"
    implementation "com.oplus.appcompat:grid:${prop_versionName}"
    implementation "com.oplus.appcompat:chip:${prop_versionName}"
    implementation "com.oplus.appcompat:progressbar:${prop_versionName}"
    implementation "com.oplus.appcompat:panel:${prop_versionName}"
    implementation "com.oplus.appcompat:responsiveui:${prop_versionName}"

    implementation("com.oplus.materialcolor:coui-material-color:${coui_colorVersion}")
    compileOnly "com.oplus.sdk:addon:${prop_addonAdapterVersion}"
    testImplementation "com.oplus.sdk:addon:${prop_addonAdapterVersion}"

    implementation("com.facebook.rebound:rebound:0.3.8")
    implementation "com.google.code.gson:gson:$gson_version"

    testImplementation "com.oplus.support:api-adapter-compat:${prop_supportSdkVersion}"
    //sau
    implementation "com.oplus.sauaar:coui-sauaar:$prop_sauVersion"

    kapt "com.inno.ostitch:stitch-compile:${stitchCompileVersion}"
    implementation "com.inno.ostitch:stitch:${stitchVersion}"
    implementation "com.inno.ostitch:stitch-annotation:${stitchAnnotationVersion}"

    testImplementation project(':common:RecorderLogBase')
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':component:player')
    implementation project(':component:summary')
}
