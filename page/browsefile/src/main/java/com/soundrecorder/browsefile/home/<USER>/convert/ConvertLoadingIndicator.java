/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ConvertLoadingIndicator.java
 * * Description: ConvertLoadingIndicator.java
 * * Version: 1.0
 * * Date : 2019/11/5
 * * Author: liuyulong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * liuyulong  2019/11/5      1.0    build this module
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.convert;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.animation.PathInterpolator;

import java.util.ArrayList;

public class ConvertLoadingIndicator extends Indicator {

    private static final String TAG = "ConvertLoadingIndicator";

    private static final float SCALE = 1.0f;
    private static final int ALPHA = 80;

    private static final float RADIUS_PERCENT = 0.55f;
    private static final float SPACING_PERCENT = 0.85f;
    private static final float TOTAL_PERCENT = 5.176f;

    private static final int DELAY_START = 600;
    private static final int DURATION = 200;


    private float[] scaleFloats = new float[]{SCALE,
            SCALE,
            SCALE};

    private int[] alphas = new int[]{ALPHA,
            ALPHA,
            ALPHA,};

    @Override
    public void draw(Canvas canvas, Paint paint) {
        float circleSpacing = getWidth() * SPACING_PERCENT / TOTAL_PERCENT;
        float radius = getWidth() * RADIUS_PERCENT / TOTAL_PERCENT;
        float x = (getWidth() - circleSpacing * 2f - radius * 6f) / 2f + radius;
        float y = getHeight() / 2f;
        for (int i = 0; i < 3; i++) {
            canvas.save();
            float translateX = x + (radius * 2f) * i + circleSpacing * i;
            canvas.translate(translateX, y);
            canvas.scale(scaleFloats[i], scaleFloats[i]);
            paint.setAlpha(alphas[i]);
            canvas.drawCircle(0, 0, radius, paint);
            canvas.restore();
        }
    }

    @Override
    public ArrayList<AnimatorSet> onCreateAnimators() {
        final ArrayList<AnimatorSet> animatorSets = new ArrayList<>();
        int[] delays = new int[]{120, 240, 360};
        for (int i = 0; i < 3; i++) {
            final int index = i;

            final AnimatorSet animatorSet = new AnimatorSet();
            ValueAnimator scaleAnim = ValueAnimator.ofFloat(1f, 1.16f, 1f);
            scaleAnim.setInterpolator(new PathInterpolator(0.48f, 0.04f, 0.52f, 0.96f));
            scaleAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator valueAnimator) {
                    //DebugUtil.d(TAG, "onCreateAnimators, onAnimationUpdate, index:" + index);
                    if (valueAnimator.getAnimatedValue() != null) {
                        scaleFloats[index] = (float) valueAnimator.getAnimatedValue();
                        postInvalidate();
                    }
                }
            });
            
            ValueAnimator alphaAnim = ValueAnimator.ofInt(38, 77, 38);
            alphaAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator valueAnimator) {
                    if (valueAnimator.getAnimatedValue() != null) {
                        alphas[index] = (int) valueAnimator.getAnimatedValue();
                        postInvalidate();
                    }
                }
            });

            addUpdateListener(animatorSet, new android.animation.AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animator) {
                    if (animator != null) {
                        animator.setStartDelay(DELAY_START);
                        animator.start();
                    }
                }
            });

            animatorSet.playTogether(scaleAnim, alphaAnim);
            animatorSet.setStartDelay(delays[i]);
            animatorSet.setDuration(DURATION);
            animatorSets.add(animatorSet);
        }
        return animatorSets;
    }
}