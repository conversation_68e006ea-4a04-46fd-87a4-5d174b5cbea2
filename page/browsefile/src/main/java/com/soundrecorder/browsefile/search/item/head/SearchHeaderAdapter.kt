package com.soundrecorder.browsefile.search.item.head

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.paging.LoadState
import androidx.paging.LoadStateAdapter
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.SearchHeaderItemBinding

class SearchHeaderAdapter : LoadStateAdapter<SearchHeaderItemViewHolder>() {

    var totalCount = 0
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    override fun onBindViewHolder(
        holder: SearchHeaderItemViewHolder,
        loadState: LoadState
    ) = holder.bind(totalCount)

    override fun onCreateViewHolder(
        parent: ViewGroup,
        loadState: LoadState
    ) = SearchHeaderItemViewHolder.create(parent)

    override fun displayLoadStateAsItem(loadState: LoadState): Boolean {
        return totalCount > 0
    }
}

class SearchHeaderItemViewHolder(
    private val mBinding: SearchHeaderItemBinding
) : RecyclerView.ViewHolder(mBinding.root) {

    fun bind(totalCount: Int) {
        mBinding.summary.text =
            mBinding.root.context.resources.getQuantityString(
                com.soundrecorder.common.R.plurals.search_result_count,
                totalCount,
                totalCount
            )
        mBinding.summary.isVisible = totalCount > 0
        // empty click for talkback mode
        itemView.setOnClickListener { }
    }

    companion object {
        fun create(parent: ViewGroup): SearchHeaderItemViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.search_header_item, parent, false)
            val binding = SearchHeaderItemBinding.bind(view)
            return SearchHeaderItemViewHolder(binding)
        }
    }
}