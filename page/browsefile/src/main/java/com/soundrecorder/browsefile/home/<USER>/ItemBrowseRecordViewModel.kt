/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : BrowseRecord.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.item

import android.app.Activity
import android.content.Context
import android.text.format.DateUtils
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.browsefile.StartActivityUtils
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel
import com.soundrecorder.common.databean.CallGroupModel
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.utils.RecordModeUtil.recordType
import com.soundrecorder.player.status.PlayStatus
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

open class ItemBrowseRecordViewModel : BaseItemRecordViewModel(), Comparable<ItemBrowseRecordViewModel> {

    companion object {
        const val TAG = "ItemBrowseRecordViewModel"
        const val PLAYER_STATE_AUTO_MARK = 6
        const val  TIME_MILLIS_DAY30 = 30L * 24 * 60 * 60 * 1000
        const val SUMMARY_SOURCE_NOTE = 0
        const val SUMMARY_SOURCE_INNER = 1
        val liveEditMode = HashMap<Int, MutableLiveData<Boolean>>()
        val liveDragMode = HashMap<Int, MutableLiveData<Boolean>>()
        val summaryTipMediaId = HashMap<Int, Long?>()
        val livePhoneBookMode = HashMap<Int, Boolean>()
        val liveSelectedMap = HashMap<Int, MutableLiveData<ConcurrentHashMap<Long, Record>>>()
        val liveAddFooter = HashMap<Int, MutableLiveData<Int>>()
        var addAnimatorDisplayName: String? = null
        var addScrollAndTipsFilePath: String? = null
        var addScrollAndTipsDisplayName: String? = null

        //监听全量,都执行动画往下滑动时出现动画切换,有播放按钮和checkBox闪现效果，此处变量仅保证第一屏执行动画，后面直接显示
        var editModeChangeAnim = false
        //转文本状态
        var liveConvertStatus = HashMap<Int, MutableLiveData<ConcurrentHashMap<Long, LiveConvertStatus>>>()
    }
    var summarySource: Int = SUMMARY_SOURCE_INNER

    var converting = false

    var convertCompleted = false

    var convertTextPath: String? = null

    var mPlayState: Int? = null
    var mPlayProgress: Long? = null

    // 摘要ID
    var noteId: String? = null

    // 摘要表中的UUID
    var recordUUID: String? = null

    //通话录音分组
    var isGroupingParent: Boolean = false
    var recordList: CopyOnWriteArrayList<ItemBrowseRecordViewModel>? = null
    var avatarColor: String? = null
    //分组中录音数量
    var groupCallCount = 0
    //分组中录音总数量
    var totalRecordCount = 0

    //智能标题生成中
    var smartNaming = false
    var showSmartTextAnim = false

    fun recordType(): Int = relativePath.recordType()

    fun onClickCheckBox() {
        notifySelectedChanged()
    }

    private fun notifySelectedChanged() {
        val map = liveSelectedMap[taskId]?.value ?: ConcurrentHashMap()
        if (map.containsKey(mediaId)) {
            map.remove(mediaId)
        } else {
            map[mediaId] = getRecord()
        }
        liveSelectedMap[taskId]?.value = map
    }

    open fun startItemPlay(context: Context) {
        StartActivityUtils.startPlayback(this, context as Activity)
    }

    fun checkSelectItemOnLongClick(): Boolean {
        val map = liveSelectedMap[taskId]?.value ?: ConcurrentHashMap()
        if (!map.containsKey(mediaId)) {
            map[mediaId] = getRecord()
            liveSelectedMap[taskId]?.value = map
            return true
        }
        return false
    }

    fun onLongClick() {
        if (liveEditMode[taskId]?.value == true) {
            return
        }
        if (!fileIsExists()) {
            DebugUtil.i(TAG, "onLongClick  file is not exit")
            return
        }
        editModeChangeAnim = true
        liveEditMode[taskId]?.value = true
        notifySelectedChanged()
    }

    fun getRecord(): Record {
        val record = Record()
        record.id = mediaId
        record.relativePath = relativePath
        record.displayName = displayName
        record.data = data
        record.ownerPackageName = ownerPackageName
        record.setIsRecycle(isRecycle)
        record.recycleFilePath = recyclePath
        record.duration = mDuration
        record.dateModied = dateModified
        record.fileSize = size
        record.recordType = recordType()
        record.mimeType = mimeType
        record.groupId = groupId
        record.groupUuid = groupUuid
        record.callerName = callerName
        record.originalName = originalName
        record.callAvatarColor = avatarColor
        return record
    }

    fun durationText(): String {
        if (isRecycle) {
            return getRecycleRemainingTime()
        } else {
            return TimeUtils.getFormatTimeExclusiveMillLeastHour(mDuration, true)
        }
    }

    private fun getRecycleRemainingTime(): String {
        val totalTime = (deleteTime + TIME_MILLIS_DAY30).toLong()
        val remainingTime = totalTime - System.currentTimeMillis()
        //day：剩余多少天
        val day = TimeUtils.getFormatTimeByMilliForDays(remainingTime).toInt()
        return BaseApplication.getAppContext().resources
            .getQuantityString(com.soundrecorder.common.R.plurals.recycle_tips_delete_remaining_days_plurals, day, day)
    }

    fun dateModifiedText(): String {
        return RecorderICUFormateUtils.formatDateTimeDateUtils(dateModified * TimeUtils.TIME_ONE_SECOND,
            DateUtils.FORMAT_SHOW_YEAR or DateUtils.FORMAT_SHOW_DATE or DateUtils.FORMAT_NUMERIC_DATE or DateUtils.FORMAT_SHOW_TIME)
    }

    fun summaryText(): String? {
        return if (this is ItemSearchViewModel) {
            this.summaryText
        } else {
            null
        }
    }

    fun durationContentDescriptionText(): String {
        val context = BaseApplication.getAppContext()
        val timeStringForDuration =
            TimeUtils.getFormatContentDescriptionTimeByMillisecond(context, mDuration)
        val timeString: String = context.getString(com.soundrecorder.common.R.string.talkback_recording_time)
        return String.format(Locale.getDefault(), timeString, timeStringForDuration)
    }

    fun itemCheckBoxContentDescriptionText(): String {
        return title ?: ""
    }

    fun isNeedShowSeekBarArea(playerState: Int?): Boolean {
        return when (playerState) {
            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING,
            PLAYER_STATE_AUTO_MARK -> true

            else -> false
        }
    }

    override fun compareTo(other: ItemBrowseRecordViewModel): Int {
        return (other.dateModified - this.dateModified).toInt()
    }

    override fun toString(): String {
        return """
           mediaId = $mediaId
           displayName =  $displayName
           originalName =  $originalName
           relativePath =  $relativePath
           duration =  $mDuration
           dateModified = $dateModified
           isRecycle = $isRecycle
           recyclePath = $recyclePath
           globalId = $globalId
        """
    }

    /**
     * 1、首页列表通过mediaId去媒体库查询录音数据
     * 2、回收站通过回收站路径直接创建文件进行判断
     * 如果查询出来为空
     * 说明数据已经被删除或者出现错误
     */
    fun fileIsExists(): Boolean {
        val result = if (isRecycle) {
            /*
             直接返回true，以便在UI上可以直接删除当前记录。真正的删除逻辑是在deleteRecycleBinRecordFile里处理,
             这样修复【ID:8398407 标题:【评估不解】【录音】【货架包敏捷试用】【M11】最近删除中，长按录音无法选中】
             因为历史问题：无【文件管理权限】，但在首页进行了删除操作，导致录音表里产生了脏数据。
             */
            true
        } else {
            MediaDBUtils.queryAudioFileExist(mediaId)
        }
        DebugUtil.d(TAG, "fileIsExists result = $result, data = ${toString()}")
        return result
    }

    open fun toStartPlayModel(
        isFromOtherApp: Boolean = false,
        seekToMill: Long? = null,
        autoPlay: Boolean? = false,
        isRecycle: Boolean = false
    ): StartPlayModel =
        StartPlayModel(mediaId).apply {
            this.isFromOtherApp = isFromOtherApp
            this.seekToMill = seekToMill
            this.autoPlay = autoPlay ?: false
            this.isRecycle = isRecycle
            if (isRecycle) {
                this.playPath = recyclePath ?: data
            } else {
                this.playPath = data
            }
            this.duration = mDuration
        }

    open fun toCallGroupModel(
        groupInfo: GroupInfo?,
        callerName: String? = null,
        avatarColor: String? = null
    ): CallGroupModel =
        CallGroupModel().apply {
            this.callerName = callerName
            this.avatarColor = avatarColor
            this.groupInfo = groupInfo
        }
}