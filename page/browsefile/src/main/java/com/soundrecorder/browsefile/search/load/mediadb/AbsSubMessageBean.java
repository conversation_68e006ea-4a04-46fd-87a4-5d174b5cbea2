/******************************************************************
 * Copyright (C), 2020-2021, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - AbsSubMessageBean.java
 * Description:
 * Version: 1.0
 * Date :  2020/9/28
 * Author: <EMAIL>
 **
 * ---------------- Revision History: ---------------------------
 *      <author>        <data>        <version >        <desc>
 *    Yongjiang,Lu      2020/9/28          1.0         build this module
 ********************************************************************/

package com.soundrecorder.browsefile.search.load.mediadb;

import android.database.Cursor;

public class AbsSubMessageBean {
    private Cursor mCursor;
    private String mSearchValue;

    public Cursor getCursor() {
        return mCursor;
    }

    public void setCursor(Cursor cursor) {
        this.mCursor = cursor;
    }

    public String getSearchValue() {
        return mSearchValue;
    }

    public void setSearchValue(String searchValue) {
        this.mSearchValue = searchValue;
    }
}
