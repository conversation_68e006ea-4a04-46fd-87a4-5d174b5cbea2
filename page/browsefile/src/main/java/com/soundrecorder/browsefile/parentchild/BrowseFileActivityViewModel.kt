/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BrowseFileActivityViewModel
 * Description:
 * Version: 1.0
 * Date: 2022/10/13
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/10/13 1.0 create
 */

package com.soundrecorder.browsefile.parentchild

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.CustomMutableLiveData
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.common.databean.CallGroupModel
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.player.speaker.SpeakerModeController

class BrowseFileActivityViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {
    var mCurrentPlayRecordData: MutableLiveData<StartPlayModel?> = MutableLiveData()
    /*上一次播放的音频媒体库ID*/
    var lastRecordId: Long = -1
    var windowType: MutableLiveData<WindowType> = MutableLiveData()
    var isFromOtherApp: Boolean = false
    var allRecordCount: MutableLiveData<Int> = MutableLiveData()
    /*列表跳转详情，跳转动画执行中*/
    var childAnimRunning: MutableLiveData<Boolean> = MutableLiveData()

    /*记录点击录制进入录制页面,用于切小屏后，如果再录制页面，需要退出播放详情*/
    var clickedToRecord: Boolean = false
    var mSpeakerModeController: SpeakerModeController = SpeakerModeController()
    /*记录点击查看摘要，摘要又不存在的noteId，用于同步列表、播放详情数据;
    * 需要使用CustomMutableLiveData，避免重建或每次进入播放详情又走一遍onChange，通话录音noteId是不变的*/
    var beDeleteSummaryNoteId: MutableLiveData<String> = CustomMutableLiveData()
    var isNeedRefresh = MutableLiveData(true)
    /*通话录音分组更多页面显示*/
    var mCallGroupMoreData: MutableLiveData<CallGroupModel?> = MutableLiveData()
    /*通话录音更多页面录音删除、编辑刷新*/
    var isCallingRecordEditRefresh: MutableLiveData<Boolean> = MutableLiveData(false)
    /*分组管理页面退出或切换分组时的，首页loading的显示*/
    var isShowLoadingWithRefresh: MutableLiveData<Boolean> = MutableLiveData(false)
    var isFileChanged: Boolean = false

    /*首页分组信息列表*/
    val liveDataGroupList = MutableLiveData<MutableList<GroupInfo>>()
    /*当前选中组信息*/
    var currentGroup = MutableLiveData<GroupInfo>()
    /*分组列表是否显示*/
    var isGroupListShow = MutableLiveData(false)
    /*是否是删除所有音频。处于回收站下，永久删除了所有音频则不用进行mBottomNavigationView?.switchToMenu*/
    var isDialogDeleteAll = MutableLiveData(false)
    /*是否支持智能命名*/
    private val _supportSmartName = savedStateHandle.getLiveData<Boolean>("key_support_Smart_Name", false)
    private val supportSmartName: LiveData<Boolean> = _supportSmartName

    fun updateSupportSmartName(isSupport: Boolean) {
        _supportSmartName.value = isSupport
    }

    fun clearPlayRecordData() {
        if (mCurrentPlayRecordData.value != null) {
            mCurrentPlayRecordData.postValueSafe(null)
        }
    }

    fun isSmallWindow(): Boolean = windowType.value == WindowType.SMALL

    fun hasPlayPageData(): Boolean = mCurrentPlayRecordData.value != null

    fun noPlayPageData(): Boolean = mCurrentPlayRecordData.value == null

    fun isCallGroupMoreShowing(): Boolean = mCallGroupMoreData.value != null

    fun clearCallGroupMoreData() {
        if (mCallGroupMoreData.value != null) {
            mCallGroupMoreData.postValueSafe(null)
        }
    }

    fun isSupportSmartName(): Boolean {
        return supportSmartName.value ?: false
    }
}