/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : BrowseViewModel.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.load

import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.TAG
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.refresh.BounceLayout
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.browsefile.StartActivityUtils
import com.soundrecorder.browsefile.StartRecordModel
import com.soundrecorder.browsefile.home.BrowseFragment
import com.soundrecorder.browsefile.home.item.FastPlayHelper
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.home.item.LiveConvertStatus
import com.soundrecorder.browsefile.search.load.center.filechange.CenterFileChangeObserver
import com.soundrecorder.common.buryingpoint.BuryingPointUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.RecordConstant.CALL_RECORD_CONTACT_GROUPING
import com.soundrecorder.common.constant.RecordConstant.CALL_RECORD_NOT_GROUPING
import com.soundrecorder.common.databean.CollectionInfo
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_ALL
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_CALLING
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_COMMON
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_NONE
import com.soundrecorder.common.databean.GroupInfo.Companion.INT_DEFAULT_RECENTLY_DELETED
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.CollectionInfoDbUtils
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.GroupInfoDbUtil.genDefaultAllGroupInfo
import com.soundrecorder.common.db.GroupInfoDbUtil.genDefaultCallGroupInfo
import com.soundrecorder.common.db.GroupInfoDbUtil.genDefaultCommonGroupInfo
import com.soundrecorder.common.db.GroupInfoDbUtil.genDefaultDeletedGroupInfo
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.executor.ExecutorManager
import com.soundrecorder.common.fileobserve.ObserverController
import com.soundrecorder.common.fileobserve.OnFileEventListener
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.share.ShareTypeDoc
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.RecordModeUtil.isFromCall
import com.soundrecorder.common.widget.CircleTextImageUtil
import com.soundrecorder.common.widget.TransitionUtils
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_CONVERT_STATUS
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_RECORD_ID
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.share.ERROR_CODE_CONTENT_RISK
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.launch
import java.lang.reflect.Type
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import java.util.stream.Collectors

class BrowseViewModel : AbsViewModel<BrowseModel, ItemBrowseRecordViewModel, GroupInfo>(), IShareListener, DefaultLifecycleObserver {

    // use restore window
    var isDeleteDialogShowing: Boolean = false
    var isRenameDialogShowing: Boolean = false
    var isLimitDialogShowing: Boolean = false
    var isSingleDialogShowing: Boolean = false
    var isShareDialogShowing: Boolean = false
    var isShareTextDialogShowing: Boolean = false

    var isRecoverDialogShowing: Boolean = false
    var isRecycleDialogAllShowing: Boolean = false

    var isCanShareRecordFiles: Boolean = true

    var isCheckedMoveRecordFile = false
    private var stopUpdateCallerNameAvatarColorFlag = false

    var taskId: Int = 0
        set(value) {
            field = value
            fastPlayHelper.taskId = taskId
        }
    val browseModel: BrowseModel = BrowseModel()
    val showSearch = MutableLiveData(false)
    val searchValue = MutableLiveData("")
    var firstVisiblePosition = 0
    var isClickDelete = false
    var isClickDeleteAll = false
    var isClickRecover = false
    var isClickRename = false
    var renameContent: String? = null
    var isAlreadyStartRecorded: Boolean = false
    var isDelayStartRecorder: Boolean = false
    var mObserverController: ObserverController? = null
    var fileChange = MutableLiveData("")
    var currentGroup = MutableLiveData<GroupInfo>()
    var lastSetBehaviorStatus = 0
    var behaviorCurrentStatus = MutableLiveData(-1)
    var showSearchUseMaxHeight: Boolean = false
    var isInitPush = false
    var fastPlayHelper: FastPlayHelper = FastPlayHelper()

    var allRecycleRecordList: ArrayList<Record>? = null
    val liveDataGroupList = MutableLiveData<MutableList<GroupInfo>>()
    var mCallRecordGroupPosition: Int = CALL_RECORD_NOT_GROUPING
    //通话录音分组显示的录音数量
    var showGroupCallCount = 0
    var totalCallRecordCount = 0
    var isShowCallMorePage: Boolean = false
    val noGroupingRecordList = CopyOnWriteArrayList<ItemBrowseRecordViewModel>()
    val callGroupingRecordList = CopyOnWriteArrayList<ItemBrowseRecordViewModel>()
    private val cachedCallGroupingRecordList = CopyOnWriteArrayList<ItemBrowseRecordViewModel>()
    private val cachedDbUpdateFailedRecordMap = HashMap<String, CopyOnWriteArrayList<Record>>()
    val needShareWaitingDialog = MutableLiveData<Pair<Boolean, ShareType?>>()
    var currentLifecycleState: Lifecycle.Event = Lifecycle.Event.ON_ANY
    val showShareLinkPanel = MutableLiveData<String?>()
    val showShareToast = MutableLiveData<Int?>()
    private var updateRecordsCallerNameAvatarColorJob: Job? = null
    private var updateDbUpdateFailedCallerNameAvatarColorJob: Job? = null

    var isNeedSmartName = false
    var needSmartNameMediaList = mutableListOf<Long>()

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    private val aiAsrManagerAction by lazy {
        Injector.injectFactory<AIAsrManagerAction>()
    }

    override var logTag: String = "BrowseViewModel"

    private fun isGroupingByContact(groupInfo: GroupInfo): Boolean {
        return groupInfo.isCallingGroup() && (mCallRecordGroupPosition == CALL_RECORD_CONTACT_GROUPING)
    }

    private fun isGroupingByContact(): Boolean {
        return getCurrentGroup().isCallingGroup() && (mCallRecordGroupPosition == CALL_RECORD_CONTACT_GROUPING)
    }

    fun getCurrentGroup(): GroupInfo {
        return currentGroup.value ?: genDefaultAllGroupInfo()
    }

    fun refresh() {
        val hasPermission: Boolean = PermissionUtils.hasReadAudioPermission()
        DebugUtil.i(logTag, "hasPermission >>> $hasPermission")
        if (hasPermission) {
            // 注册PushRegister
            if (!isInitPush) {
                cloudKitApi?.registerPushIfNeed(BaseApplication.getAppContext())
                isInitPush = true
            }
            //刷新分组列表的逻辑移到这里，跟录音列表的刷新同步调用
            queryAllGroups(BaseApplication.getAppContext())
            mObserverController?.startDeleteFileObserver()
            if (isCheckedMoveRecordFile) {
                refresh(getCurrentGroup(), isGroupingByContact())
            } else {
                asyncCheckNeedMoveRecordFile(getCurrentGroup(), isGroupingByContact())
            }
        } else {
            browseModel.liveViewStatus.value = ViewStatus.NO_PERMISSION
        }
    }

    fun refreshCallByGroup(groupInfo: GroupInfo, callerName: String? = null) {
        releasePlayer()
        //recordFilter.value = filter
        val hasPermission: Boolean = PermissionUtils.hasReadAudioPermission()
        DebugUtil.i(logTag, "hasPermission >>> $hasPermission")
        if (hasPermission) {
            // 注册PushRegister
            if (!isInitPush) {
                cloudKitApi?.registerPushIfNeed(BaseApplication.getAppContext())
                isInitPush = true
            }
            mObserverController?.startDeleteFileObserver()
            if (isCheckedMoveRecordFile) {
                refresh(groupInfo, false, callerName)
            } else {
                asyncCheckNeedMoveRecordFile(groupInfo, false, callerName)
            }
        } else {
            browseModel.liveViewStatus.value = ViewStatus.NO_PERMISSION
        }
    }

    fun findPositionByFileNameAddPath(name: String?, path: String?): Int {
        if (name.isNullOrEmpty() || path.isNullOrEmpty()) {
            return -1
        }
        val dataList = liveDataList.value
        var position: Int = -1
        if (isGroupingByContact()) {
            dataList?.forEachIndexed { index, itemGroup ->
                itemGroup.recordList?.forEach {
                    if (it.displayName == name && it.relativePath == path) {
                         //返回分组父级position
                         position = index
                    }
                }
            }
        } else {
            position = dataList?.indexOfFirst {
                (it.displayName == name && it.relativePath == path)
            } ?: -1
        }
        return position
    }

    /**
     * 获取通话分组下的record位置
     */
    fun findChildPositionByFileNameAddPath(name: String?, path: String?): Int {
        if (name.isNullOrEmpty() || path.isNullOrEmpty()) {
            return -1
        }
        val dataList = liveDataList.value
        var position: Int = -1
        dataList?.forEach { itemGroup ->
            position = itemGroup.recordList?.indexOfFirst {
                (it.displayName == name && it.relativePath == path)
            } ?: -1
        }
        return position
    }

    fun findPositionByMediaId(id: Long?): Int {
        if (id == null) {
            return -1
        }
        val dataList = liveDataList.value
        return dataList?.indexOfFirst {
            it.mediaId == id
        } ?: -1
    }

    private fun asyncCheckNeedMoveRecordFile(groupInfo: GroupInfo, isContactGroup: Boolean, callerName: String? = null) {
        browseModel.liveViewStatus.value = ViewStatus.QUERYING
        isCheckedMoveRecordFile = true
        ExecutorManager.singleExecutor?.execute(MoveRecordFileTask {
            refresh(groupInfo, isContactGroup, callerName)
        })
        // 将activity延迟逻辑移动到viewModel中来，更改执行时机，用户同意用户须知进入首页才执行
        viewModelScope.launch(Dispatchers.IO) {
            /* 调用该埋点前需确认有REEAD_EXTERNAL_STORAG权限，此处由于调用前已判断，所以此处不做判断*/
            aiAsrManagerAction?.checkUpdateAIUnitConfigBackground(BaseApplication.getAppContext())
            BuryingPointUtil.addRecorderFileInfoEvent(BaseApplication.getAppContext())
        }
    }

    override fun createModel(): BrowseModel {
        return browseModel
    }

    /**
     * 获取分组的名称作为key
     * @param group 分组信息
     */
    override fun getCacheKey(group: GroupInfo): String {
        return group.mGroupName
    }

    /**
     * @param job 传入的job可能会被取消，触发抛出CancellationException，注意异常捕获问题
     */
    override fun onDataReadyCompleted(
        modelItem: AbsModel<ItemBrowseRecordViewModel, GroupInfo>,
        data: List<ItemBrowseRecordViewModel>,
        args: GroupInfo,
        name: String?,
        job: Job?
    ) {
        if (getCurrentGroup().mId != args.mId) {
            // 当前分组和数据分组不一致，则跳过不显示
            DebugUtil.i(TAG, "refresh, current group has been changed, data from:${args.mGroupName},skip show")
            return
        }

        if (isGroupingByContact(args)) {
            onGroupingDataReadyCompleted(data, args, job)
        } else {
            if (name != null) {
                val callerRecordList = mutableListOf<ItemBrowseRecordViewModel>()
                data.forEach {
                    //job可能会被取消，触发抛出CancellationException
                    job?.ensureActive()
                    if (it.callerName == name) {
                        callerRecordList.add(it)
                    }
                }
                liveDataList.postValueSafe(callerRecordList)
            } else {
                liveDataList.postValueSafe(data)
            }
        }
        correctSelectRecordInEditMode(data)
        setRecycleAllRecordList(data)
        /*
         * 放到首页数据回来后，不影响首页速度
         * for init center-dmp search:初始化中子，应用启动仅执行一次
         */
        if (!CenterFileChangeObserver.initDmpSearch()) {
            //文件总数量从有到无，从无到有，都执行一下兜底，没有变化则检查本地是否有脏数据
            CenterFileChangeObserver.checkUnSyncDirtyUpdateData()
        }

        if (args.isRecentlyDeleteGroup() || args.isCustomGroup()) {
            GroupInfoManager.getInstance(BaseApplication.getAppContext()).verifyGroupCount()
            if (args.isCustomGroup()) {
                // 更新分组文件个数，否则自定义分组文件个数不能实时更新
                val groupInfo = GroupInfoDbUtil.getGroupInfoByUuid(BaseApplication.getAppContext(), args.mUuId)
                groupInfo?.let {
                    currentGroup.value?.mGroupCount = groupInfo.mGroupCount
                }
            }
        }
        clearConvertStatus()
    }

    /**
     * 计算通话录音分组下，按联系人分组显示，每个联系人分组下的录音文件和个数
     * @param job 传入的job可能会被取消，触发抛出CancellationException，注意异常捕获问题
     */
    private fun onGroupingDataReadyCompleted(
        data: List<ItemBrowseRecordViewModel>,
        group: GroupInfo,
        job: Job? = null
    ) {
        showGroupCallCount = 0
        totalCallRecordCount = 0
        callGroupingRecordList.clear()
        //把相同的callerName筛选出来，记录到List中
        val map =
            data.stream().collect(Collectors.groupingBy(ItemBrowseRecordViewModel::callerName))
        map.forEach { key, itemRecordList ->
            //job可能会被取消，触发抛出CancellationException
            job?.ensureActive()
            val itemGroupParent = ItemBrowseRecordViewModel()
            itemRecordList.forEach { recordModel ->
                //job可能会被取消，触发抛出CancellationException
                job?.ensureActive()
                if (key == recordModel.callerName) {
                    itemGroupParent.callerName = key
                    //数据库没有则从缓存取值
                    itemGroupParent.originalName =
                        recordModel.originalName ?: getGroupItemFromCache(key)?.originalName
                    itemGroupParent.avatarColor =
                        recordModel.avatarColor ?: getGroupItemFromCache(key)?.avatarColor
                }
            }
            itemGroupParent.groupId = group.mId
            itemGroupParent.groupUuid = group.mUuId

            itemGroupParent.isGroupingParent = true
            itemGroupParent.dateModified = itemRecordList.maxOf { it.dateModified }
            itemGroupParent.totalRecordCount = itemRecordList.size
            if (itemRecordList.size >= NumberConstant.NUM_3) {
                itemGroupParent.groupCallCount = NumberConstant.NUM_3
            } else {
                itemGroupParent.groupCallCount = itemRecordList.size
            }
            showGroupCallCount += itemRecordList.size
            totalCallRecordCount += itemRecordList.size
            itemGroupParent.recordList = CopyOnWriteArrayList<ItemBrowseRecordViewModel>().apply {
                addAll(itemRecordList)
            }
            callGroupingRecordList.add(itemGroupParent)
        }

        if (callGroupingRecordList.isNotEmpty()) {
            callGroupingRecordList.sortByDescending { it.dateModified }
            generateCallGroupRandomColor(callGroupingRecordList)
        }
        liveDataList.postValueSafe(callGroupingRecordList)

        // 检查是否需要直接更新头像颜色和联系人名字
        checkUpdateRecordsCallerNameAvatarColor()
    }

    private fun checkUpdateRecordsCallerNameAvatarColor() {
        val cachedCount = getCountFormCallGroupingRecordList(cachedCallGroupingRecordList)
        val currentCount = getCountFormCallGroupingRecordList(callGroupingRecordList)
        DebugUtil.d(
            logTag,
            "onCallGroupDataReadyCompleted, callRecordGroupList:${callGroupingRecordList.size}"
                    + ", cachedCount:$cachedCount, currentCount:$currentCount"
        )
        if (cachedCount != currentCount) {
            cachedCallGroupingRecordList.clear()
            cachedCallGroupingRecordList.addAll(callGroupingRecordList)
            //中止可能启动job
            updateDbUpdateFailedCallerNameAvatarColorJob?.cancel()
            stopUpdateCallerNameAvatarColorFlag = true
            //启动新的job
            tryToUpdateRecordsCallerNameAvatarColor()
        } else {
            if (cachedDbUpdateFailedRecordMap.isNotEmpty()) {
                retryToUpdateFailedRecordsCallerNameAvatarColor()
            }
        }
    }

    private fun getCountFormCallGroupingRecordList(list: CopyOnWriteArrayList<ItemBrowseRecordViewModel>): Int {
        var count = 0
        list.forEach {
            count += it.totalRecordCount
        }
        return count
    }

    private fun getGroupItemFromCache(groupKey: String?): ItemBrowseRecordViewModel? {
        if (cachedCallGroupingRecordList.isNotEmpty() && !groupKey.isNullOrEmpty()) {
            return cachedCallGroupingRecordList.find { it.callerName == groupKey  }
        }
        return null
    }

    private fun tryToUpdateRecordsCallerNameAvatarColor() {
        if (updateRecordsCallerNameAvatarColorJob?.isCompleted == false) {
            DebugUtil.d(logTag, "updateRecordsCallerNameAvatarColor, updateRecordsCallerNameAvatarColorJob is running")
            return
        }

        if (cachedCallGroupingRecordList.isNotEmpty()) {
            val startTime = System.currentTimeMillis()
            stopUpdateCallerNameAvatarColorFlag = false
            updateRecordsCallerNameAvatarColorJob = updateRecordsCallerNameAvatarColor(cachedCallGroupingRecordList) {
                DebugUtil.d(logTag, "updateRecordsCallerNameAvatarColor, job completed! cost time = ${System.currentTimeMillis() - startTime}")
            }
        }
    }
    fun retryToUpdateFailedRecordsCallerNameAvatarColor() {
        if (updateRecordsCallerNameAvatarColorJob?.isCompleted == false) {
            DebugUtil.d(logTag, "updateRecordsCallerNameAvatarColor, 1.updateRecordsCallerNameAvatarColorJob is running")
            return
        }

        if (updateDbUpdateFailedCallerNameAvatarColorJob?.isCompleted == false) {
            DebugUtil.d(logTag, "updateRecordsCallerNameAvatarColor, 2.updateDbUpdateFailedCallerNameAvatarColorJob is running")
            return
        }

        if (cachedDbUpdateFailedRecordMap.isNotEmpty()) {
            val startTime = System.currentTimeMillis()
            stopUpdateCallerNameAvatarColorFlag = false
            val failedList = CopyOnWriteArrayList(cachedDbUpdateFailedRecordMap.values.flatten())
            updateDbUpdateFailedCallerNameAvatarColorJob = processFailedRecordsCallerNameAvatarColor(failedList)
        }
    }

    fun stopUpdateRecordsCallerNameAvatarColor() {
        stopUpdateCallerNameAvatarColorFlag = true
    }

    /**
     * 生成随机头像颜色
     */
    fun generateCallGroupRandomColor(callRecordGroupList: MutableList<ItemBrowseRecordViewModel>) {
        callRecordGroupList.forEachIndexed { index, itemCallGroup ->
            if (itemCallGroup.avatarColor == null) {
                var randomColor = CircleTextImageUtil.randomColor
                when (index) {
                    0 -> itemCallGroup.avatarColor = randomColor
                    else -> {
                        val prePos = index - 1
                        if (prePos < 0) {
                            return
                        }
                        val preItem = callRecordGroupList[prePos]
                        //获取上一个位置的avatarColor，保证本次生成color不与上一个位置的相同
                        if (preItem.avatarColor != randomColor) {
                            itemCallGroup.avatarColor = randomColor
                        } else {
                            while (preItem.avatarColor == randomColor) {
                                randomColor = CircleTextImageUtil.randomColor
                                if (preItem.avatarColor != randomColor) {
                                    itemCallGroup.avatarColor = randomColor
                                    continue
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 通话录音切換分组
     */
    fun changeCallGrouping() {
        if (liveDataList.value.isNullOrEmpty()) {
            DebugUtil.d(logTag, "changeCallGrouping, liveDataList is null")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            currentGroup.value?.let { onGroupingDataReadyCompleted(liveDataList.value!!, it) }
        }
    }

    private fun setRecycleAllRecordList(data: List<ItemBrowseRecordViewModel>) {
        if (data.isNotEmpty() && isRecycleFilter()) {
            if (allRecycleRecordList.isNullOrEmpty()) {
                allRecycleRecordList = ArrayList<Record>()
            }
            allRecycleRecordList?.clear()
            data.forEach {
                allRecycleRecordList?.add(it.getRecord())
            }
        }
    }

    private fun isRecycleFilter(): Boolean {
        return currentGroup.value?.mGroupType == INT_DEFAULT_RECENTLY_DELETED
    }

    fun getShowRecordCount(): Int = when (currentGroup.value?.mGroupType) {
        INT_DEFAULT_ALL -> browseModel.browseListCount?.allCount ?: 0
        INT_DEFAULT_COMMON -> browseModel.browseListCount?.commonCount ?: 0
        INT_DEFAULT_CALLING -> {
            if (isShowCallMorePage) {
                browseModel.browseListCount?.callerNameCount ?: 0
            } else {
                if (isGroupingByContact()) {
                    showGroupCallCount
                } else {
                    browseModel.browseListCount?.callCount ?: 0
                }
            }
        }
        INT_DEFAULT_RECENTLY_DELETED -> browseModel.browseListCount?.recycleCount ?: 0
        INT_DEFAULT_NONE -> currentGroup.value?.mGroupCount ?: 0
        else -> browseModel.browseListCount?.allCount ?: 0
    }

    fun getAllRecordCount(): Int = browseModel.browseListCount?.allCount ?: 0

    /**
     * 通话录音总数，仅用于首页副标题通话录音分组时显示总数
     */
    fun getShowCallTotalCount(): Int {
        return if (totalCallRecordCount != 0) {
            totalCallRecordCount
        } else {
            browseModel.browseListCount?.callCount ?: 0
        }
    }

    private fun checkFolderAndRegisterFileObserver() {
        val mContext = BaseApplication.getAppContext()
        val isHasFolderDeleted = mObserverController?.checkHasFolderRemoved() ?: false
        if (isHasFolderDeleted) {
            val isNotExist = RecordModeUtil.checkFoldersNotExist(mContext)
            RecordModeUtil.ensureFoldersExist(mContext)
            if (isNotExist) {
                DebugUtil.i(logTag, "folder has been deleted , stop and restartObserver")
                mObserverController?.startDeleteFileObserver()
            }
        }
    }

    fun startFileObserver(onFileEventListener: OnFileEventListener) {
        stopObserver()
        DebugUtil.i(logTag, "startFileObserver")
        if (mObserverController == null) {
            mObserverController = ObserverController()
        }
        mObserverController?.addFileEventListener(onFileEventListener)
        mObserverController?.startFileObserver()
    }

    fun stopObserver() {
        DebugUtil.i(logTag, "stopFileObserver")
        mObserverController?.stopObserver()
        mObserverController = null
    }

    fun release(playPath: String?, dataPath: String?, path: String?, allPath: String?) {
        if (!playPath.isNullOrBlank() && !path.isNullOrBlank()) {
            if (playPath == path && allPath == dataPath) {
                DebugUtil.w(logTag, "isDeletingFile player.release")
                releasePlayer()
            }
        }
        /*
        一边删除一边更新编辑模式，回收站需求去掉此操作
        ID:8224156 标题:【历史问题】【录音】【回收站】【必现】【22003】 录音删除大量录音，录音数据未被选中
        //removeDeletedSelectedRecord(allPath)
        */
        fileChange.postValue(allPath)
    }

    fun deleteMuteCache(allPath: String?) {
        allPath?.let {
            viewModelScope.launch(Dispatchers.IO) {
                DebugUtil.d(logTag, "deleteMuteCache, delete path is $it")
                playbackApi?.clearMuteCache(it)
            }
        }
    }

    fun deleteOShareDBData(allPath: String?) {
        allPath?.let {
            viewModelScope.launch(Dispatchers.IO) {
                OShareConvertUtil.deleteOShareDBData(allPath)
            }
        }
    }

    fun deleteAllOShareDBData(allPath: String?) {
        allPath?.let {
            viewModelScope.launch(Dispatchers.IO) {
                OShareConvertUtil.deleteAllOShareDBData(allPath)
            }
        }
    }

    private fun removeDeletedSelectedRecord(allPath: String?) {
        val selectedMap = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value
        selectedMap?.forEach { (key: Long, record: Record) ->
            if (record.data == allPath) {
                DebugUtil.i(logTag, " removeDeletedSelectedRecord >> ${record.displayName}")
                selectedMap.remove(key)
                ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.postValue(selectedMap)
                return@forEach
            }
        }
    }

    fun releasePlayer() {
        fastPlayHelper.reset()
    }

    fun onClickSetting(fragment: Fragment) {
        settingApi?.launchForResult(fragment, Constants.REQUEST_CODE_SETTING)
    }

    fun onClickStartRecorderActivity(
        fragment: BrowseFragment,
        isSmallHeight: Boolean,
        brenoFront: Boolean,
        checkIsCall: Boolean
    ): Boolean {
        checkPutSharedCurrentModeLayout(fragment, isSmallHeight)
        releasePlayer()
        checkFolderAndRegisterFileObserver()
        val model = StartRecordModel()
        fragment.activity?.intent?.let {
            model.setStartRecordModel(it)
            model.isFromCall = it.isFromCall()
        }
        return StartActivityUtils.startRecordActivity(
            fragment.activity,
            model,
            brenoFront,
            true,
            checkIsCall
        )
    }

    private fun checkPutSharedCurrentModeLayout(fragment: BrowseFragment, isSmallHeight: Boolean) {
        if (!RecordModeUtil.isSupportMultiRecordMode(BaseApplication.getAppContext())) {
            DebugUtil.i(logTag, "is not support multi record mode , not put.")
            return
        }
        if (isSmallHeight) {
            DebugUtil.i(logTag, "is not show multi record mode , not put.")
            return
        }

        TransitionUtils.panelHeight = fragment.mBinding.gradientBackground.height.toFloat()
    }

    /**
     * 退出编辑模式
     */
    fun exitEditMode() {
        if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value == true) {
            toggleEditMode()
            // 拖拽前提是编辑模式，so退出编辑模式的同时将拖拽置为false
            exitDragMode()
        }
        //退出编辑模式，这几个标记重置，否则可能导致新增记录列表无法刷新
        isClickDelete = false
        isClickDeleteAll = false
        isClickRename = false
        isClickRecover = false
    }

    fun exitDragMode() {
        if (ItemBrowseRecordViewModel.liveDragMode[taskId]?.value == true) {
            ItemBrowseRecordViewModel.liveDragMode[taskId]?.value = false
        }
    }

    fun toggleEditMode(isRecycle: Boolean = false) {
        val lastEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        DebugUtil.i(logTag, "toggleEditMode $lastEditMode >>> ${!lastEditMode}")
        if (lastEditMode || isRecycle) {
            clearSelected()
        }

        ItemBrowseRecordViewModel.editModeChangeAnim = true
        releasePlayer()
        ItemBrowseRecordViewModel.liveEditMode[taskId]?.postValueSafe(!lastEditMode)
        ItemBrowseRecordViewModel.liveAddFooter[taskId]?.postValueSafe(0)
    }

    fun selectAllOrNone() {
        val map = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value
            ?: ConcurrentHashMap<Long, Record>()
        val isSelectedAll = map.size >= getShowRecordCount()
        DebugUtil.d(logTag, "selectAllOrNone, mapSize:${map.size}, " +
                "isSelectedAll:$isSelectedAll, showCount:${getShowRecordCount()}")
        if (isSelectedAll) {
            map.clear()
        } else {
            if (isGroupingByContact()) {
                liveDataList.value?.forEach continuing@{ item: ItemBrowseRecordViewModel ->
                    item.recordList?.forEach {
                        map[it.mediaId] = it.getRecord()
                        DebugUtil.d(logTag, "item.record:${it.getRecord()}")
                    }
                }
            } else {
                liveDataList.value?.forEach continuing@{ item: ItemBrowseRecordViewModel ->
                    map[item.mediaId] = item.getRecord()
                }
            }
        }
        ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value = map
    }

    fun getRecycleAllRecordList(): ArrayList<Record>? {
        if (allRecycleRecordList.isNullOrEmpty()) {
            allRecycleRecordList = ArrayList<Record>()

            allRecycleRecordList?.clear()
            liveDataList.value?.forEach continuing@{ item: ItemBrowseRecordViewModel ->
                allRecycleRecordList?.add(item.getRecord())
            }
        }
        return allRecycleRecordList
    }

    /**
     * 矫正选中数据
     */
    private fun correctSelectRecordInEditMode(data: List<ItemBrowseRecordViewModel>) {
        if (ItemBrowseRecordViewModel.liveEditMode[taskId]?.value != true) {
            // 非编辑模式 return
            return
        }
        val originSelectMap = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value ?: return
        if (originSelectMap.isEmpty()) {
            // 勾选数据empty return
            return
        }
        val beRemoveList = mutableListOf<Long>().apply { addAll(originSelectMap.keys().toList()) }
        data.forEach { item: ItemBrowseRecordViewModel ->
            if (originSelectMap.containsKey(item.mediaId)) {
                beRemoveList.remove(item.mediaId)
            }
        }
        if (beRemoveList.isNotEmpty()) {
            beRemoveList.forEach {
                originSelectMap.remove(it)
            }
            DebugUtil.i(logTag, "updateSelectData  wrongKeyList: ${beRemoveList.size}")
            ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.postValueSafe(originSelectMap)
        }
    }

    fun clearSelected() {
        val map = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value
        map?.clear()
        ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.postValueSafe(map)
    }

    fun selectGroup() {
        DebugUtil.i(logTag, "call refresh, from selectGroup", true)
        refresh()
    }

    fun isPlaying(): Boolean {
        return fastPlayHelper.mediaPlayerManager.isPlaying()
    }

    fun pausePlay() {
        fastPlayHelper.mediaPlayerManager.pausePlay()
    }

    fun replay() {
        fastPlayHelper.mediaPlayerManager.replay()
    }

    /**
     * 耗时会有延时
     */
    fun refreshEnable(bounceLayout: BounceLayout) {
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value
        // 编辑模式下，首页下拉刷新不可用
        val isEdit = isEditMode ?: false
        // 正在同步中、查询中状态，首页下拉刷新不可用
        val isSyncingState = cloudTipManagerAction?.getCloudStatusLiveData()?.value?.state?.let {
            it in intArrayOf(
                ITipStatus.STATE_SYNCING,
                ITipStatus.STATE_QUERY
            )
        } ?: false

        DebugUtil.i(logTag, "refreshEnable: ${cloudTipManagerAction?.isCloudSwitchOn()}")
        // 同时满足：支持CK+非编辑模式+非查询同步状态+云同步开关打开+首页数据count大于0，且非回收站，才将首页下拉刷新置为可用
        val bNotSyncing = !isSyncingState && cloudTipManagerAction?.isCloudSwitchOn() == true
        if (!isEdit && bNotSyncing && (getShowRecordCount() > 0) && (!isRecycleFilter())) {
            bounceLayout.setRefreshEnable(true)
        } else {
            bounceLayout.setRefreshEnable(false)
        }
    }

    override fun onCleared() {
        super.onCleared()
        stopObserver()
        releasePlayer()
        releaseAllRecordList()
        clearNeedSmartNameMedias()
    }

    private fun releaseAllRecordList() {
        allRecycleRecordList?.clear()
        allRecycleRecordList = null
        callGroupingRecordList.clear()
        noGroupingRecordList.clear()
    }

    /**
     * 当便签被卸载或者不支持摘要时，需要删除所有的摘要数据
     */
    fun clearAllSummary() {
        DebugUtil.i(logTag, "clearAllSummary")
        val value = liveDataList.value
        value?.let {
            it.forEach { item ->
                item.noteId = null
                item.recordUUID = null
            }
            val data = it.toMutableList()
            liveDataList.postValueSafe(data)
        }
    }

    /**
     * 当跳转到便签返回该条录音的摘要不存在时，则需删掉该条摘要
     */
    fun clearSummaryByNoteId(noteId: String) {
        DebugUtil.i(logTag, "clearSummaryByNoteId")
        NoteDbUtils.deleteNoteByNoteId(noteId)
    }

    fun updateDataLiveDataByClearNoteId(noteId: String) {
        val value = liveDataList.value
        value?.let { list ->
            list.find {
                it.noteId == noteId
            }?.let { item ->
                item.noteId = null
                item.recordUUID = null
                val data = list.toMutableList()
                liveDataList.postValueSafe(data)
            }
        }
    }

    /**
     * 帮助与反馈回调的收集信息列表
     */
    fun addFeedbackRequestData(json: String?) {
        DebugUtil.d(logTag, "addFeedbackRequestData, json:$json")
        if (json?.isNotEmpty() == true) {
            val type: Type = object : TypeToken<List<CollectionInfo>>() {}.type
            val list: List<CollectionInfo> = GsonUtil.getGson().fromJson(json, type)

            val collectionInfoList = arrayListOf<CollectionInfo>()
            if (list.isNotEmpty()) {
                list.forEach { collectionInfo ->
                    collectionInfoList.add(
                        CollectionInfo(
                            type = collectionInfo.type,
                            content = collectionInfo.content,
                            dateCreated = collectionInfo.dateCreated,
                            count = collectionInfo.count
                        )
                    )
                }
                if (collectionInfoList.isNotEmpty()) {
                    viewModelScope.launch(Dispatchers.IO) {
                        CollectionInfoDbUtils.addCollectionInfos(collectionInfoList)
                    }
                }
            }
        }
    }

    fun queryAllGroups(context: Context) {
        DebugUtil.d(logTag, "queryAllGroups")
        viewModelScope.launch(Dispatchers.IO) {
            val result = GroupInfoDbUtil.getAllGroupInfoList(context)
            if (result.isEmpty()) {
                result.add(genDefaultAllGroupInfo())
                result.add(genDefaultCallGroupInfo())
                result.add(genDefaultCommonGroupInfo())
                result.add(genDefaultDeletedGroupInfo())
            }
            liveDataGroupList.postValueSafe(result)
        }
    }

    private fun updateRecordsCallerNameAvatarColor(callRecordGroupList: MutableList<ItemBrowseRecordViewModel>,  callBackFunction: () -> Unit): Job? {
        if (callRecordGroupList.isNotEmpty()) {
            return viewModelScope.launch(Dispatchers.IO) {
                DebugUtil.w(TAG, "updateRecordsCallerNameAvatarColor: start")
                run {
                    callRecordGroupList.forEach { callerRecordGroup ->
                        if (stopUpdateCallerNameAvatarColorFlag) {
                            DebugUtil.w(
                                TAG,
                                "updateRecordsCallerNameAvatarColor: stopUpdateCallerNameAvatarColor"
                            )
                            return@run
                        }

                        val updateFailedList = processRecordColorOrCallerName(callerRecordGroup)
                        updateFailedList?.let {
                            callerRecordGroup.callerName?.apply {
                                cachedDbUpdateFailedRecordMap.remove(callerRecordGroup.callerName)
                                cachedDbUpdateFailedRecordMap[callerRecordGroup.callerName!!] =
                                    updateFailedList
                            }
                        }
                    }
                }
                callBackFunction.invoke()
                DebugUtil.w(TAG, "updateRecordsCallerNameAvatarColor: end")
            }
        }
        return null
    }

    private fun processRecordColorOrCallerName(itemCallGroup: ItemBrowseRecordViewModel): CopyOnWriteArrayList<Record>? {
        val updateFailedList: CopyOnWriteArrayList<Record> = CopyOnWriteArrayList()
        run {
            itemCallGroup.recordList?.forEach { itemBrowseRecord ->
                if (stopUpdateCallerNameAvatarColorFlag) {
                    DebugUtil.w(TAG, "setRecordColorOrCallerName: stopUpdateCallerNameAvatarColor")
                    return@run
                }
                itemCallGroup.avatarColor?.let {
                    itemBrowseRecord.avatarColor = it
                }
                itemCallGroup.callerName?.let {
                    itemBrowseRecord.callerName = it
                }
                val bSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                    .updateRecordCallerNameAvatarColor(
                        itemBrowseRecord.getRecord(),
                        itemCallGroup.avatarColor,
                        itemCallGroup.callerName
                    )
                if (!bSuccess) {
                    updateFailedList.add(itemBrowseRecord.getRecord())
                }
            }
        }
        return updateFailedList
    }

    private fun processFailedRecordsCallerNameAvatarColor(failedList: CopyOnWriteArrayList<Record>): Job? {
        if (failedList.isNotEmpty()) {
            return viewModelScope.launch(Dispatchers.IO) {
                val startTime = System.currentTimeMillis()
                DebugUtil.w(TAG, "updateRecordsCallerNameAvatarColor: processFailedRecordsCallerNameAvatarColor start, size = ${failedList.size}")
                run {
                    failedList.forEach { itemBrowseRecord ->
                        if (stopUpdateCallerNameAvatarColorFlag) {
                            DebugUtil.w(TAG,
                                "updateRecordsCallerNameAvatarColor: processFailedRecordsCallerNameAvatarColor stopUpdateCallerNameAvatarColor"
                            )
                            return@run
                        }
                        ensureActive()
                        val result = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                            .updateRecordDbAvatarAndColor(itemBrowseRecord)
                        if (result > 0) {
                            val callerGroupList = cachedDbUpdateFailedRecordMap[itemBrowseRecord.callerName]
                            callerGroupList?.let {
                                val cachedRecord = callerGroupList.find { it.data == itemBrowseRecord.data }
                                val bRemove = callerGroupList.remove(cachedRecord)
                                DebugUtil.w(TAG, "updateRecordsCallerNameAvatarColor:"
                                        + " processFailedRecordsCallerNameAvatarColor, UPDATE result = $result, removed from cache = $bRemove,"
                                        + " path = ${itemBrowseRecord.data}"
                                )
                            }
                        }
                    }
                    DebugUtil.w(TAG,
                        "updateRecordsCallerNameAvatarColor: processFailedRecords end"
                                + ", cost ${System.currentTimeMillis() - startTime} ms"
                    )
                }
            }
        }
        return null
    }

    override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
        DebugUtil.d(logTag, "onShowShareWaitingDialog  type:$type")
        needShareWaitingDialog.postValue(Pair(true, type))
    }

    override fun onShareSuccess(mediaId: Long, type: ShareType) {
        DebugUtil.d(logTag, "onShareSuccess  type:$type")
        if (type is ShareTypeDoc) {
            pausePlay()
        }
        needShareWaitingDialog.postValue(Pair(false, null))
        unregisterShareListener()
        if (type is ShareTypeLink) {
            showShareLinkPanel.postValue(type.link)
        }
    }

    override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
        DebugUtil.d(logTag, "onShareFailed  type:$type")
        needShareWaitingDialog.postValue(Pair(false, null))
        unregisterShareListener()
        if (type is ShareTypeLink) {
            if (error == ERROR_CODE_CONTENT_RISK) {
                showShareToast.postValue(com.soundrecorder.common.R.string.content_risk_unable_generate_share_link)
            } else {
                showShareToast.postValue(com.soundrecorder.common.R.string.link_generation_failure_retry)
            }
        }
    }

    private fun unregisterShareListener() {
        Injector.injectFactory<ShareAction>()?.unregisterShareListener(this)
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        currentLifecycleState = Lifecycle.Event.ON_CREATE
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        currentLifecycleState = Lifecycle.Event.ON_START
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        currentLifecycleState = Lifecycle.Event.ON_RESUME
        showShareLinkPanel.value?.let {
            showShareLinkPanel.postValue(it)
        }
        showShareToast.value?.let {
            showShareToast.postValue(it)
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        currentLifecycleState = Lifecycle.Event.ON_PAUSE
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        currentLifecycleState = Lifecycle.Event.ON_STOP
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        currentLifecycleState = Lifecycle.Event.ON_DESTROY
    }

    fun initLifecycle(owner: LifecycleOwner) {
        owner.lifecycle.addObserver(this)
    }

    fun clearLifecycle(owner: LifecycleOwner) {
        owner.lifecycle.removeObserver(this)
    }

    fun clearSmartNameCacheIds(selectedMediaIdList: java.util.ArrayList<String>?) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        selectedMediaIdList.forEach {
            ConvertingInfo.removeSmartNameMediaId(it.toLong())
        }
    }

    fun clearNeedSmartNameMedias() {
        DebugUtil.d(logTag, "clearNeedSmartNameMedias")
        isNeedSmartName = false
        needSmartNameMediaList.clear()
    }

    fun addNeedSmartNameMedias(selectedMediaIdList: MutableList<Long>?) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        DebugUtil.d(logTag, "addNeedSmartNameMedias")
        isNeedSmartName = true
        needSmartNameMediaList.clear()
        needSmartNameMediaList.addAll(selectedMediaIdList)
    }

    fun onConvertStatusChanged(intent: Intent?) {
        DebugUtil.d(logTag, "onConvertStatusChanged")
        val mediaId = intent?.getLongExtra(KEY_NOTIFY_RECORD_ID, -1L) ?: -1
        val bStartConvert = intent?.getBooleanExtra(KEY_NOTIFY_CONVERT_STATUS, false) ?: false
        ConvertingInfo.onConvertStatusChanged(mediaId, bStartConvert)
        val map = ItemBrowseRecordViewModel.liveConvertStatus[taskId]?.value ?: ConcurrentHashMap<Long, LiveConvertStatus>()
        if (bStartConvert) {
            if (map.isNotEmpty()) {
                removeConvertComplteId(map)
            }
            map.put(mediaId, LiveConvertStatus(mediaId, bStartConvert))
            ItemBrowseRecordViewModel.liveConvertStatus[taskId]?.postValueSafe(map)
        } else {
            viewModelScope.launch(Dispatchers.IO) {
                val isConvertComplete = ConvertDbUtil.checkAlreadyConvertComplete(mediaId)
                DebugUtil.d(logTag, "onConvertStatusChanged, isConvertComplete:$isConvertComplete")
                map.put(mediaId, LiveConvertStatus(mediaId, bStartConvert, isConvertComplete))
                ItemBrowseRecordViewModel.liveConvertStatus[taskId]?.postValueSafe(map)
            }
        }
    }

    private fun removeConvertComplteId(map: ConcurrentHashMap<Long, LiveConvertStatus>) {
        map.forEach {
            if (!ConvertingInfo.isConverting(it.key) || it.value.isConvertComplte) {
                DebugUtil.d(logTag, "removeConvertComplteId, mediaId:${it.key}")
                map.remove(it.key)
            }
        }
    }

    fun clearConvertStatus() {
        val map = ItemBrowseRecordViewModel.liveConvertStatus[taskId]?.value ?: return
        if (map.isEmpty()) {
            return
        }
        removeConvertComplteId(map)
        ItemBrowseRecordViewModel.liveConvertStatus[taskId]?.postValueSafe(map)
    }
}
