/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : ItemBrowseView.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.item

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.graphics.Color
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.tintimageview.COUITintUtil
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.ItemBrowseBinding
import com.soundrecorder.browsefile.home.RecordItemDragDelegate
import com.soundrecorder.browsefile.home.load.ConvertingInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.ViewUtils.getUnDisplayViewHeight
import com.soundrecorder.common.utils.cancelAnimationExt
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.invisible
import com.soundrecorder.common.utils.playAnimationExt
import com.soundrecorder.common.utils.visible
import java.io.File
import java.util.concurrent.ConcurrentHashMap

open class ItemBrowseViewHolder(
    viewBinding: ItemBrowseBinding,
    private val mBrowseViewListener: IBrowseViewHolderListener,
    private val mIsGroupingByContact: Boolean
) : AbsItemBrowseViewHolder<ItemBrowseBinding, ItemBrowseRecordViewModel>(viewBinding, mBrowseViewListener, mIsGroupingByContact) {

    companion object {
        private const val FLOAT_0_DOT_1 = 0.1F
        private const val FLOAT_0_DOT_3 = 0.3F
        private const val FLOAT_0_DOT_92 = 0.92F
        private const val INT_30 = 30
        private const val ALPHA_0_DOT_6 = 153
        private const val START_TIPS_ANIMATOR_TIME = 200L
    }
    override var logTag: String = "ItemBrowseViewHolder"

    private var downEditMode: Boolean? = null
    private var animator: Animator? = null
    private var animatorTips: AnimatorSet? = null

    private val mLiveSelectedMap by lazy {
        Observer<ConcurrentHashMap<Long, Record>?> {
            updateItemSelected()
        }
    }

    private val mLiveEditMode by lazy {
        Observer<Boolean?> {
            updateCheckbox()
        }
    }

    private val dragModeObserve by lazy {
        Observer<Boolean?> {
            val view = mCardView ?: return@Observer
            if (it == true && ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.containsKey(mItemViewModel.mediaId) == true) {
                RecordItemDragDelegate.setViewAlpha(view)
            } else if (view.foreground != null) {
                view.foreground = null
            }
        }
    }

    private val mLiveConvertStatus by lazy {
        Observer<ConcurrentHashMap<Long, LiveConvertStatus>> {
            updateConvertStatus(it)
        }
    }

    private fun updateConvertStatus(convertStatusMap: ConcurrentHashMap<Long, LiveConvertStatus>) {
        if (convertStatusMap.isEmpty()) {
            return
        }
        if (convertStatusMap.containsKey(mItemViewModel.mediaId)) {
            val convertStatus = convertStatusMap.get(mItemViewModel.mediaId)
            val isConverting = convertStatus?.isConverting ?: false
            val isConvertComplte = convertStatus?.isConvertComplte ?: false
            DebugUtil.d(logTag, "updateConvertStatus, isConverting:$isConverting, isConvertComplte:$isConvertComplte")
            when {
                (isConverting && !isConvertComplte) -> {
                    mItemRecordInfo.itemConvertLoading.visible()
                    mItemRecordInfo.ivConvertCompleted.gone()
                }
                (isConvertComplte && !isConverting) -> {
                    mItemRecordInfo.itemConvertLoading.gone()
                    mItemRecordInfo.ivConvertCompleted.visible()
                }
                else -> {
                    mItemRecordInfo.itemConvertLoading.gone()
                    mItemRecordInfo.ivConvertCompleted.gone()
                }
            }
            mItemViewModel.converting = isConverting
            mItemViewModel.convertCompleted = isConvertComplte
        }
    }

    init {
        mItemRecordInfo = mBinding.itemInfo
        itemSummaryAndPlay = mBinding.itemSummaryAndPlay
        mItemPlayArea = itemSummaryAndPlay.itemPlayArea
        mItemPlayInfo = mBinding.itemPlayInfo
        mCardView = mBinding.checkboxLayout

        initBasicView()
    }

    override fun <D> setViewModel(data: D) {
        mItemViewModel = data as ItemBrowseRecordViewModel
        taskId = mItemViewModel.taskId
        val nameEqual = ItemBrowseRecordViewModel.addAnimatorDisplayName == mItemViewModel.displayName
        val isFirstPosition = adapterData?.getRealPosInViewType(bindingAdapterPosition) == 0
        if (nameEqual) {
            if (isFirstPosition && mBrowseViewListener.canShowAddAnimator(mItemViewModel)) {
                mBrowseViewListener.getPlayerController()?.let {
                    it.setPlayRecordItem(mItemViewModel)
                }
            } else {
                ItemBrowseRecordViewModel.addAnimatorDisplayName = null
            }
        }
    }

    override fun onRootViewClick(v: View) {
        DebugUtil.i(logTag, "onRootViewClick")
        browseViewListener?.onClickItem(mItemViewModel)
    }

    override fun onRootViewLongClick(v: View): Boolean {
        mBrowseViewListener.onLongClickItem(v, mItemViewModel)
        return true
    }

    override fun observeData(owner: LifecycleOwner) {
        super.observeData(owner)
        ItemBrowseRecordViewModel.liveEditMode[taskId]?.observe(owner, mLiveEditMode)
        ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.observe(owner, mLiveSelectedMap)
        ItemBrowseRecordViewModel.liveDragMode[taskId]?.observe(owner, dragModeObserve)
        ItemBrowseRecordViewModel.liveConvertStatus[taskId]?.observe(owner, mLiveConvertStatus)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initBasicView() {
        super.initBasicView()
        //点击item动效，要求longCLick事件进入编辑模式后，才弹起手指，此时改变footer高度
        mBinding.checkboxLayout.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    downEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value
                }
                MotionEvent.ACTION_CANCEL,
                MotionEvent.ACTION_OUTSIDE,
                MotionEvent.ACTION_UP -> {
                    if (downEditMode != ItemBrowseRecordViewModel.liveEditMode[taskId]?.value) {
                        DebugUtil.i(
                            logTag,
                            "checkboxLayout onTouchUp, editMode has changed"
                        )
                        ItemBrowseRecordViewModel.liveAddFooter[taskId]?.value = 0
                    }
                }
            }
            false
        }
    }

    override fun bindOtherInfo() {
        if (animator?.isRunning == true) {
            DebugUtil.w(logTag, "checkStartAddAnimator anim is running, displayName = ${mItemViewModel.displayName}")
            animator?.cancel()
            animator = null
        }
        itemView.removeCallbacks(tipsRunnable)
        if (animatorTips?.isRunning == true) {
            DebugUtil.w(
                logTag,
                "checkCallStartScrollAddTipsAnimator animatorTips is running, displayName = ${mItemViewModel.displayName}"
            )
            animatorTips?.cancel()
            animatorTips = null
        }
        checkStartAddAnimator()
        checkCallStartScrollAddTipsAnimator()
    }

    /**
     * 电话本通话记录有通话录音的跳转过来需要滚动到对应位置并且高亮提示
     */
    private fun checkCallStartScrollAddTipsAnimator() {
        if (ItemBrowseRecordViewModel.livePhoneBookMode[taskId] != true) {
            return
        }
        val needStartTipsAnimator =
            (ItemBrowseRecordViewModel.addScrollAndTipsFilePath == mItemViewModel.relativePath)
                    && (ItemBrowseRecordViewModel.addScrollAndTipsDisplayName == mItemViewModel.displayName)
        if (needStartTipsAnimator && ScreenUtil.isSmallScreen(itemView.context)) {
            DebugUtil.i(
                logTag,
                "checkCallStartScrollAddTipsAnimator needStartTipsAnimator = $needStartTipsAnimator ,displayName = ${mItemViewModel.displayName}"
            )
            itemView.postDelayed(tipsRunnable, START_TIPS_ANIMATOR_TIME)
        }
    }
    private fun setImageColor(color: Int) {
        if (mBinding.checkboxLayout.background == null) {
            DebugUtil.e(logTag, "setImageColor mBinding.checkboxLayout.background is null !")
        } else {
            COUITintUtil.tintDrawable(mBinding.checkboxLayout.background, color)
        }
    }

    private fun startTipsAnimator() {
        val startColor =
            itemView.context.resources.getColor(R.color.selector_record_item_background)
        val endColor = itemView.context.resources.getColor(com.support.appcompat.R.color.coui_color_card_pressed)
        val endColorAlpha = Color.argb(
            ALPHA_0_DOT_6,
            Color.red(endColor),
            Color.green(endColor),
            Color.blue(endColor)
        )
        val colorAnimStart = ValueAnimator.ofArgb(startColor, endColorAlpha).apply {
            duration = BrowseAdapter.TIPS_START
            this.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            this.addUpdateListener {
                val color = it.animatedValue as Int
                setImageColor(color)
            }
        }
        val colorAnimEnd = ValueAnimator.ofArgb(endColorAlpha, startColor).apply {
            duration = BrowseAdapter.TIPS_END
            startDelay = BrowseAdapter.TIPS_START
            this.interpolator = PathInterpolatorHelper.couiEaseInterpolator
            this.addUpdateListener {
                val color = it.animatedValue as Int
                setImageColor(color)
            }
        }
        val animatorList = mutableListOf<Animator>().also {
            it.add(colorAnimStart)
            it.add(colorAnimEnd)
        }
        animatorTips = AnimatorSet().also {
            it.playTogether(animatorList)
            it.addListener(object : AnimatorListenerAdapter() {
                var isCancel = false

                override fun onAnimationCancel(animation: Animator) {
                    isCancel = true
                }
                override fun onAnimationEnd(animation: Animator) {
                    animation.removeListener(this)
                    mBinding.checkboxLayout.background = ContextCompat.getDrawable(
                        mBinding.checkboxLayout.context,
                        R.drawable.item_round_rect_bg
                    )
                    if (!isCancel) {
                        ItemBrowseRecordViewModel.addScrollAndTipsFilePath = null
                        ItemBrowseRecordViewModel.addScrollAndTipsDisplayName = null
                        DebugUtil.i(logTag, "startTipsAnimator anim end")
                    }
                }
            })
            it.start()
        }
    }

    private val tipsRunnable = Runnable {
        startTipsAnimator()
    }

    private fun checkStartAddAnimator() {
        val needStartAddAnimator =
            (ItemBrowseRecordViewModel.addAnimatorDisplayName == mItemViewModel.displayName)
                    && (adapterData?.getRealPosInViewType(bindingAdapterPosition) == 0)
        DebugUtil.i(
            logTag, "checkStartAddAnimator needStartAddAnimator = $needStartAddAnimator ," +
                    "当前item名称=${mItemViewModel.displayName}," +
                    "addAnimatorDisplayName=${ItemBrowseRecordViewModel.addAnimatorDisplayName}," +
                    "getRealPosInViewType=${adapterData?.getRealPosInViewType(bindingAdapterPosition) == 0}, itemVIew = ${itemView.hashCode()}"
        )
        if (needStartAddAnimator && mBrowseViewListener.canShowAddAnimator(mItemViewModel)) {
            itemView.alpha = 0f
            ItemBrowseRecordViewModel.addAnimatorDisplayName = null
            animator = startAddAnimator(itemView, object : AnimatorListenerAdapter() {

                override fun onAnimationCancel(animation: Animator) {
                    itemView.alpha = 1f
                    itemView.scaleX = 1f
                    itemView.scaleY = 1f

                    DebugUtil.i(logTag, "onAnimationCancel")
                }

                override fun onAnimationEnd(animation: Animator) {
                    itemView.updateLayoutParams {
                        height = RecyclerView.LayoutParams.WRAP_CONTENT
                    }
                }
            })
        }
    }

    override fun onSeekBarAnimChanged(inAnim: Boolean) {
        if (!inAnim) {
            DebugUtil.i(logTag, "onSeekBarAnimChanged cancel, displayName = ${mItemViewModel.displayName}")
            animator?.cancel()
            animator = null
            ItemBrowseRecordViewModel.addAnimatorDisplayName = null
        }
    }

    private fun startAddAnimator(itemView: View, animatorListener: AnimatorListenerAdapter? = null): Animator {
        val cardHeightAnim = ValueAnimator.ofInt(1, itemView.getUnDisplayViewHeight()).apply {
            duration = BrowseAdapter.DURATION_CHANGE
            interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            addUpdateListener {
                itemView.updateLayoutParams {
                    this.height = it.animatedValue as Int
                }
            }
        }

        val alphaAnim = ObjectAnimator.ofFloat(itemView, "alpha", 0f, 1f).apply {
            duration = BrowseAdapter.DURATION_ADD
            startDelay =  BrowseAdapter.DURATION_CHANGE
            interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            addUpdateListener {
                itemView.alpha = it.animatedValue as Float
            }
        }
        val scaleAnim = ValueAnimator.ofFloat(FLOAT_0_DOT_92, 1f).apply {
            duration = BrowseAdapter.DURATION_ADD
            startDelay = BrowseAdapter.DURATION_CHANGE
            interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            addUpdateListener {
                itemView.scaleX = it.animatedValue as Float
                itemView.scaleY = it.animatedValue as Float
            }
        }

        val animatorList = mutableListOf<Animator>().also {
            it.add(cardHeightAnim)
            it.add(alphaAnim)
            it.add(scaleAnim)
        }
        return AnimatorSet().also {
            it.playTogether(animatorList)
            it.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationCancel(animation: Animator) {
                    animatorListener?.onAnimationCancel(animation)
                }

                override fun onAnimationStart(animation: Animator) {
                    animatorListener?.onAnimationStart(animation)
                }

                override fun onAnimationEnd(animation: Animator) {
                    animatorListener?.onAnimationEnd(animation)
                    animation.removeListener(this)
                }
            })
            it.start()
        }
    }

    override fun bindView(isLastPosition: Boolean?) {
        super.bindView(isLastPosition)
        updateItemSelected()
        updateCheckbox()
        updateContactGroupInfo(isLastPosition)
    }

    /**
     * 更新是否按联系人分组列表Item信息
     */
    private fun updateContactGroupInfo(isLastPosition: Boolean?) {
        if (isGroupingByContact) {
            mBinding.checkboxLayout.updateLayoutParams<RecyclerView.LayoutParams> {
                topMargin = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp0).toInt()
            }
            if (isLastPosition != null && isLastPosition == true) {
                mBinding.bottomSpaceDivider.gone()
            } else {
                mBinding.bottomSpaceDivider.visible()
            }
        } else {
            mBinding.checkboxLayout.updateLayoutParams<RecyclerView.LayoutParams> {
                topMargin = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp12).toInt()
            }
            mBinding.bottomSpaceDivider.gone()
        }
    }

    override fun updateItemInfo() {
        mBinding.itemRecord = mItemViewModel
        if (mItemViewModel.smartNaming || ConvertingInfo.isSmartNaming(mItemViewModel.mediaId)) {
            playSmartNameAnimation()
        } else {
            cancelSmartNameAnimation()
        }
        updateSmartName()
    }

    private fun updateSmartName() {
        val smartName = ConvertingInfo.getSmartNameResult(mItemViewModel.mediaId)
        val containsSmartName = ConvertingInfo.isContainsSmartNameResult(mItemViewModel.mediaId)
        DebugUtil.d(logTag, "updateSmartName, containsSmartName:$containsSmartName, smartName:$smartName")
        if (mItemViewModel.showSmartTextAnim && containsSmartName && !smartName.isNullOrEmpty()) {
            cancelSmartNameAnimation()
            DebugUtil.d(logTag, "updateSmartName, startAnimateText:$smartName")
            mItemRecordInfo.recordTitle.cancelAnimation()
            mItemRecordInfo.recordTitle.setAnimateText(smartName, true)
            mItemRecordInfo.recordTitle.setAnimationListener {
                DebugUtil.d(logTag, "updateSmartName, animateTextEnd")
                ConvertingInfo.removeSmartNameMediaId(mItemViewModel.mediaId)
                mItemViewModel.smartNaming = false
                ConvertingInfo.removeSmartNameResult(mItemViewModel.mediaId)
            }
            // 更新path(BaseItemRecordViewModel的data)
            val originalData = mItemViewModel.data
            val suffix = originalData.suffix()
            val newNameWithSuffix = smartName + suffix

            var newPath = originalData
            val lastIndexOfFileDescriptor = originalData?.lastIndexOf(File.separator)
            if (lastIndexOfFileDescriptor != null) {
                if (lastIndexOfFileDescriptor > 0) {
                    newPath = originalData.substring(
                        0,
                        lastIndexOfFileDescriptor
                    ) + File.separator + newNameWithSuffix
                } else {
                    DebugUtil.e(logTag, "updateSmartName lastIndexOfFileDescriptor is error !")
                }
            } else {
                DebugUtil.e(logTag, "updateSmartName lastIndexOfFileDescriptor is null !")
            }
            DebugUtil.d(logTag, "updateSmartName, newNameWithSuffix:$newNameWithSuffix, newPath:$newPath")
            mItemRecordInfo.itemRecord?.data = newPath
            mItemViewModel.title = smartName
            mItemViewModel.displayName = newNameWithSuffix
            mItemViewModel.data = newPath
        } else {
            mItemRecordInfo.recordTitle.cancelAnimation()
            mItemRecordInfo.recordTitle.text = mItemViewModel.title
        }
    }

    private fun playSmartNameAnimation() {
        mItemRecordInfo.recordTitle.invisible()
        mItemRecordInfo.aiTitleLoading.playAnimationExt()
    }

    private fun cancelSmartNameAnimation() {
        mItemRecordInfo.recordTitle.visible()
        mItemRecordInfo.aiTitleLoading.cancelAnimationExt()
    }

    private fun updateItemSelected() {
        val isChecked =
            ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value?.containsKey(mItemViewModel.mediaId)
        mBinding.checkboxLayout.isChecked = (isChecked == true)
    }

    private fun updateCheckbox() {
        val needAnim = ItemBrowseRecordViewModel.editModeChangeAnim
        val isEditMode = ItemBrowseRecordViewModel.liveEditMode[taskId]?.value ?: false
        if (isEditMode) {
            if (needAnim && mBinding.checkboxLayout.isCheckBoxNotCompleteVisible()) {
                val animList = arrayListOf<Animator>().apply {
                    getPlayBtnEditModeEnterAnimation()?.let { add(it) }
                }
                mBinding.checkboxLayout.startOrContinueEditModeEnterAnimation(
                        animList.toTypedArray(),
                    object : AnimatorListenerAdapter() {
                        override fun onAnimationStart(animation: Animator) {
                            if (mBrowseViewListener.getWindowType().value == WindowType.SMALL) {
                                mItemPlayArea.playButtonArea.visibility = View.VISIBLE
                            }
                        }

                        override fun onAnimationEnd(animation: Animator) {
                            ItemBrowseRecordViewModel.editModeChangeAnim = false
                            mItemPlayArea.playButtonArea.isVisible = false
                        }
                    })
            } else {
                mBinding.checkboxLayout.cancelAnimation()
                mBinding.checkboxLayout.setEditModeState()
                setEditModeState()
            }
        } else {
            if (needAnim && mBinding.checkbox.isVisible) {
                val animList = arrayListOf<Animator>().apply {
                    getPlayBtnEditModeExitAnimation()?.let { add(it) }
                }
                mBinding.checkboxLayout.startOrContinueEditModeExitAnimation(
                        animList.toTypedArray(),
                    object : AnimatorListenerAdapter() {
                        override fun onAnimationStart(animation: Animator) {
                            if (mBrowseViewListener.getWindowType().value == WindowType.SMALL) {
                                mItemPlayArea.playButtonArea.visibility = View.VISIBLE
                                mItemPlayArea.playButton.visibility = View.VISIBLE
                            }
                        }

                        override fun onAnimationEnd(animation: Animator) {
                            ItemBrowseRecordViewModel.editModeChangeAnim = false
                            if (mBrowseViewListener.getWindowType().value == WindowType.SMALL) {
                                mItemPlayArea.playButtonArea.visibility = View.VISIBLE
                                mItemPlayArea.playButton.visibility = View.VISIBLE
                            }
                        }
                    })
            } else {
                mBinding.checkboxLayout.cancelAnimation()
                mBinding.checkboxLayout.setNormalModeState()
                setNormalModeState()
            }
        }
    }

    private fun updateOtherViewWhenEdit(isEditMode: Boolean) {
        // 中大屏下无快捷播放按钮
        if (mBrowseViewListener.getWindowType().value != WindowType.SMALL) {
            mItemPlayArea.playButtonArea.isVisible = false
            return
        }
        mItemPlayArea.playButtonArea.isVisible = !isEditMode
        mItemPlayArea.playButton.alpha = if (isEditMode) 0F else 1f
    }

    protected open fun setEditModeState() {
        updateOtherViewWhenEdit(true)
    }

    protected open fun setNormalModeState() {
        updateOtherViewWhenEdit(false)
    }

    private fun getPlayBtnEditModeEnterAnimation(): Animator? {
        if (mBrowseViewListener.getWindowType().value != WindowType.SMALL) {
            // 中大屏为父子级结构，无快捷播放入口
            return null
        }
        return ObjectAnimator.ofFloat(
            mItemPlayArea.playButton,
            "alpha",
            mItemPlayArea.playButton.alpha,
            0f
        ).apply {
            duration = 180
            interpolator = PathInterpolatorHelper.couiEaseInterpolator
        }
    }

    private fun getPlayBtnEditModeExitAnimation(): Animator? {
        if (mBrowseViewListener.getWindowType().value != WindowType.SMALL) {
            // 中大屏为父子级结构，无快捷播放入口
            return null
        }
        return ObjectAnimator.ofFloat(
            mItemPlayArea.playButton,
            "alpha",
            mItemPlayArea.playButton.alpha,
            1f
        ).apply {
            startDelay = 167
            duration = 180
            interpolator = PathInterpolatorHelper.couiEaseInterpolator
        }
    }

    override fun onViewRecycled() {
        super.onViewRecycled()
        mBinding.checkboxLayout.onRelease()
        ItemBrowseRecordViewModel.liveEditMode[taskId]?.removeObserver(mLiveEditMode)
        ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.removeObserver(mLiveSelectedMap)
        ItemBrowseRecordViewModel.liveDragMode[taskId]?.removeObserver(dragModeObserve)
        ItemBrowseRecordViewModel.liveConvertStatus[taskId]?.removeObserver(mLiveConvertStatus)
        itemView.setOnDragListener(null)
        itemView.removeCallbacks(tipsRunnable)
        if (animator?.isRunning == true) {
            DebugUtil.i(logTag, "onViewRecycled cancel, itemVIew = ${itemView.hashCode()}")
            animator?.cancel()
            animator = null
        }
        if (animatorTips?.isRunning == true) {
            DebugUtil.i(
                logTag,
                "onViewRecycled cancel animatorTips cancel, itemVIew = ${itemView.hashCode()}"
            )
            animatorTips?.cancel()
            animatorTips = null
        }
    }
}