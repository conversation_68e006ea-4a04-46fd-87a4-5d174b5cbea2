<?xml version="1.0" encoding="utf-8"?><!-- oppoListItem -->
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="itemRecord"
            type="com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel" />
    </data>

    <com.soundrecorder.browsefile.home.view.CheckboxLayout
        android:id="@+id/checkbox_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        android:paddingHorizontal="@dimen/dp16"
        android:background="@drawable/item_round_rect_bg"
        android:descendantFocusability="blocksDescendants"
        android:elevation="0dp"
        android:forceDarkAllowed="false"
        app:exclude_view_tags="bg_play_area,item_play_info,checkbox,summary_button,item_play_area,play_button_area,play_button,record_duration">

        <com.coui.appcompat.checkbox.COUICheckBox
            android:id="@+id/checkbox"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:clickable="false"
            android:contentDescription="@{itemRecord.itemCheckBoxContentDescriptionText()}"
            android:focusable="false"
            android:onClick="@{()->itemRecord.onClickCheckBox()}"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/item_info"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/item_info"
            tools:visibility="gone" />
        <Space
            android:id="@+id/space_checkbox_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp12"
            app:layout_goneMarginEnd="0dp"
            app:layout_constraintTop_toTopOf="@+id/checkbox"
            app:layout_constraintBottom_toBottomOf="@+id/checkbox"
            app:layout_constraintEnd_toStartOf="@+id/checkbox" />

        <include
            android:id="@+id/item_info"
            layout="@layout/item_record_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp12"
            app:itemRecord="@{itemRecord}"
            app:layout_constraintEnd_toStartOf="@+id/barrier_right_buttons"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_right_buttons"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="start"
            app:constraint_referenced_ids="item_summary_and_play,checkbox,space_checkbox_start" />
        <include
            android:id="@+id/item_summary_and_play"
            layout="@layout/item_summary_and_play"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:itemRecord="@{itemRecord}"
            app:layout_constraintBottom_toBottomOf="@id/item_info"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/item_info"
            app:layout_constraintTop_toTopOf="@id/item_info" />

        <com.soundrecorder.browsefile.home.view.ItemBrowsePlayInfoLayout
            android:id="@+id/item_play_info"
            android:layout_width="0dp"
            android:layout_height="@dimen/seekbar_layout_height"
            android:tag="item_play_info"
            android:layout_marginTop="@dimen/dp12"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_info"
            tools:visibility="visible" />

        <Space
            android:id="@+id/bottom_space"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp12"
            app:layout_constraintBottom_toTopOf="@id/bottom_space_divider"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/item_play_info" />

        <View
            android:id="@+id/bottom_space_divider"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/coui_color_divider"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/bottom_space" />
    </com.soundrecorder.browsefile.home.view.CheckboxLayout>

</layout>