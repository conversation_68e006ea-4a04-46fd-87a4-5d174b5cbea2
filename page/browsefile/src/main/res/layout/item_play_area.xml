<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="itemRecord"
            type="com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel" />
    </data>

    <RelativeLayout
        android:id="@+id/play_button_area"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/play_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ripple_list_play"
            android:src="@drawable/ic_list_play"
            android:tag="play_button" />

    </RelativeLayout>

</layout>