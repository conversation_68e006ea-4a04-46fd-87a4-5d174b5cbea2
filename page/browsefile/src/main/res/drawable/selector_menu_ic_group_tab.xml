<selector xmlns:android="http://schemas.android.com/apk/res/android" >
    <item android:drawable="@null" android:state_enabled="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/record_group_label_item_radius" />
            <solid android:color="@color/coui_color_card" />
        </shape>
    </item>
    <item android:drawable="@null" android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/record_group_label_item_radius" />
            <solid android:color="@color/coui_color_press" />
        </shape>
    </item>
</selector>