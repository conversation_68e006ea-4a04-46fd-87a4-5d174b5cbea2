/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingApiTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.view

import android.content.Context
import android.os.Build
import androidx.lifecycle.coroutineScope
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.BrowseFile
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class QuestionnaireGuideTipViewTest {
    private var mActivity: BrowseFile? = null
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(BrowseFile::class.java).get()
    }

    @After
    fun release() {
        mContext = null
        mActivity = null
    }

    @Test
    fun should_notNull_when_init() {
        val mActivity = mActivity ?: return

        val guideTipView = QuestionnaireGuideTipView(mActivity, mActivity.lifecycle.coroutineScope, null, null)
        Assert.assertNotNull(guideTipView)
    }

    @Test
    fun verify_value_when_refreshViewData() {
        val guideTipView = Mockito.mock(QuestionnaireGuideTipView::class.java)
        guideTipView.refreshViewData()

        Mockito.verify(guideTipView, Mockito.times(1)).refreshViewData()
    }

    @Test
    fun verify_value_when_release() {
        val guideTipView = Mockito.mock(QuestionnaireGuideTipView::class.java)
        guideTipView.release()

        Mockito.verify(guideTipView, Mockito.times(1)).release()
    }
}