/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: StartActivityUtilsTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.browsefile

import android.app.Activity
import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class StartActivityUtilsTest {

    private var context: Context? = null
    private var mockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var activity: Activity? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        activity = Robolectric.buildActivity(Activity::class.java).get()
        mockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedBaseApplication?.`when`<Any> { BaseApplication.getAppContext() }
            ?.thenReturn(context)
    }

    @After
    fun tearDown() {
        mockedBaseApplication?.close()
        mockedBaseApplication = null
        context = null
        activity = null
    }

    @Test
    fun should_corrent_when_startRecordActivity() {
        val model = Mockito.mock(StartRecordModel::class.java)
        Mockito.`when`(model.isFromCall).thenReturn(false)
        Mockito.`when`(model.isNeedResult).thenReturn(false)
        Mockito.`when`(model.limitSize).thenReturn(1000L)
        Mockito.`when`(model.limitDuration).thenReturn(1000)
        StartActivityUtils.startRecordActivity(activity, model, false, true, false)
    }

    @Test
    fun should_corrent_when_startPlayback() {
        val record = Mockito.mock(ItemBrowseRecordViewModel::class.java)
        Mockito.`when`(record.data).thenReturn("hahahhahah")
        Mockito.`when`(record.displayName).thenReturn("测试")
        Mockito.`when`(record.recordType()).thenReturn(0)
        Mockito.`when`(record.mediaId).thenReturn(100)
        Mockito.`when`(record.mDuration).thenReturn(1000000)
        Mockito.`when`(record.data).thenReturn("hahahhahah")
        Mockito.`when`(record.data).thenReturn("hahahhahah")
        StartActivityUtils.startPlayback(record, activity, 1000, 1, "测试")
    }
}