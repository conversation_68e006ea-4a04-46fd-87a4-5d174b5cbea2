/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FastPlayerHelperTest
 * Description:
 * Version: 1.0
 * Date: 2022/11/17
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/11/17 1.0 create
 */

package com.soundrecorder.browsefile.home.view

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.home.item.FastPlayHelper
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel.Companion.PLAYER_STATE_AUTO_MARK
import com.soundrecorder.browsefile.shadows.ShadowCursorHelper
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.player.status.PlayStatus
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class FastPlayerHelperTest {

    @Test
    fun should_returnTrue_when_playBtnClick() {
        val browseRecordViewModel = Mockito.spy(ItemBrowseRecordViewModel())
        Mockito.`when`(browseRecordViewModel.fileIsExists()).thenReturn(true)
        val fastPlayer = FastPlayHelper()
        fastPlayer.taskId = 1000
        fastPlayer.setPlayRecordItem(browseRecordViewModel)
        fastPlayer.mediaPlayerManager.playerState.value = PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING
        fastPlayer.playBtnClick()
        Assert.assertTrue(fastPlayer.mediaPlayerManager.mTaskId == fastPlayer.taskId)
    }

    @Test
    @Config(shadows = [ShadowCursorHelper::class])
    @Throws(java.lang.Exception::class)
    fun should_returnFalse_when_ensurePlayerCreated() {
        val browseRecordViewModel = ItemBrowseRecordViewModel()
        val fastPlayer = FastPlayHelper()
        fastPlayer.setPlayRecordItem(browseRecordViewModel)
        Whitebox.invokeMethod<Any>(fastPlayer, "ensurePlayerCreated")
        Assert.assertNotNull(fastPlayer.mediaPlayerManager)
        fastPlayer.reset()
        Assert.assertFalse(fastPlayer.mediaPlayerManager.hasInit)
    }

    @Test
    fun should_returnNull_when_seekToPlay() {
        val browseRecordViewModel = ItemBrowseRecordViewModel()
        browseRecordViewModel.mediaId = 10
        val fastPlayer = FastPlayHelper()
        fastPlayer.setPlayRecordItem(browseRecordViewModel)
        Whitebox.invokeMethod<Any>(fastPlayer, "ensurePlayerCreated")
        fastPlayer.mediaPlayerManager.doSeekTime(1000)
        Assert.assertEquals(1000, fastPlayer.mediaPlayerManager.getCurrentPlayerTime())
    }

    @Test
    fun should_equals_when_reset() {
        val browseRecordViewModel = ItemBrowseRecordViewModel()
        val fastPlayer = FastPlayHelper()
        fastPlayer.setPlayRecordItem(browseRecordViewModel)
        fastPlayer.setPlayRecordItem(ItemBrowseRecordViewModel().apply {
            mediaId = 1L
        })
        Assert.assertTrue(PLAYER_STATE_AUTO_MARK == fastPlayer.mediaPlayerManager.playerState.value)
    }

    @Test
    fun should_equals_when_getNotificationPlayerState() {
        val browseRecordViewModel = ItemBrowseRecordViewModel()
        val fastPlayer = FastPlayHelper()
        fastPlayer.setPlayRecordItem(browseRecordViewModel)
        fastPlayer.changePlayerState(PlayStatus.PLAYER_STATE_PLAYING)
        Assert.assertFalse(fastPlayer.hasPaused())
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PLAYING, fastPlayer.getPlayerState())
    }

    @Test
    fun should_equals_when_getDuration() {
        val browseRecordViewModel = ItemBrowseRecordViewModel()
        browseRecordViewModel.mDuration = 1000
        val fastPlayer = FastPlayHelper()
        fastPlayer.setPlayRecordItem(browseRecordViewModel)
        Assert.assertEquals(1000, fastPlayer.getDuration())
    }

    @Test
    fun should_equals_when_getCurrentPlayerTime() {
        val browseRecordViewModel = ItemBrowseRecordViewModel()
        val fastPlayer = FastPlayHelper()
        fastPlayer.setPlayRecordItem(browseRecordViewModel)
        fastPlayer.mediaPlayerManager.currentTimeMillis.setValue(1000L)
        Assert.assertEquals(1000, fastPlayer.getCurrentPlayerTime())
    }

    @Test
    fun should_equals_when_getPlayerName() {
        val browseRecordViewModel = ItemBrowseRecordViewModel()
        browseRecordViewModel.displayName = "123"
        val fastPlayer = FastPlayHelper()
        fastPlayer.setPlayRecordItem(browseRecordViewModel)
        Assert.assertEquals("123", fastPlayer.getPlayerName().getValue())
    }

    @Test
    fun should_returnTrue_when_hasPlayingRecord() {
        val fastPlayer = FastPlayHelper()
        fastPlayer.setPlayRecordItem(ItemBrowseRecordViewModel())
        fastPlayer.changePlayerState(PlayStatus.PLAYER_STATE_PLAYING)
        Assert.assertTrue(fastPlayer.hasPlayingRecord())

        fastPlayer.changePlayerState(PlayStatus.PLAYER_STATE_PAUSE)
        Assert.assertTrue(fastPlayer.hasPlayingRecord())

        fastPlayer.changePlayerState(PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING)
        Assert.assertTrue(fastPlayer.hasPlayingRecord())

        fastPlayer.changePlayerState(PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE)
        Assert.assertTrue(fastPlayer.hasPlayingRecord())
    }

    @Test
    fun should_returnFalse_when_hasPlayingRecord() {
        val fastPlayer = FastPlayHelper()
        Assert.assertFalse(fastPlayer.hasPlayingRecord())

        fastPlayer.setPlayRecordItem(ItemBrowseRecordViewModel())
        Assert.assertTrue(fastPlayer.hasPlayingRecord())

        fastPlayer.changePlayerState(PlayStatus.PLAYER_STATE_HALTON)
        Assert.assertFalse(fastPlayer.hasPlayingRecord())
    }
}

