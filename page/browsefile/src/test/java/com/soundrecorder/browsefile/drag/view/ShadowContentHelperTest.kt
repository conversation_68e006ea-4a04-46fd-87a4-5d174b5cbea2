/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ShadowContentHelperTest
 * Description:
 * Version: 1.0
 * Date: 2024/2/21
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/21 1.0 create
 */

package com.soundrecorder.browsefile.drag.view

import android.content.Context
import android.os.Build
import android.view.View
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.browsefile.shadows.ShadowAppFeatureUtil
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowAppFeatureUtil::class])
class ShadowContentHelperTest {
    private var context: Context? = null
    private var mockScreenUtil: MockedStatic<ScreenUtil>? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mockScreenUtil = Mockito.mockStatic(ScreenUtil::class.java)
        mockScreenUtil?.`when`<Int> { ScreenUtil.screenHeight }?.thenReturn(1920)
    }

    @After
    fun tearDown() {
        context = null
        mockScreenUtil?.close()
        mockScreenUtil = null
    }

    @Test
    fun should_correct_when_getScale() {
        val context = context ?: return
        Assert.assertFalse(ShadowContentHelper.getScale(context, 0, 0).second)
        Assert.assertFalse(ShadowContentHelper.getScale(context, 240, 240).second)
        Assert.assertFalse(ShadowContentHelper.getScale(context, 400, 400).second)
    }

    @Test
    fun should_correct_when_getShowRect() {
        val view = Mockito.mock(View::class.java)
        Mockito.`when`(view.height).thenReturn(500)
        Assert.assertNotNull(ShadowContentHelper.getShowRect(view, 1f))
        Assert.assertNotNull(ShadowContentHelper.getShowRect(view, 3f))
    }
}