/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/9/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.localsync;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.browsefile.search.load.center.CenterDbUtils;
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertBean;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import static org.mockito.Mockito.mock;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = { ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class CenterSyncManagerTest {

    private List<SearchInsertBean> newItem = new ArrayList<>();
    private List<CenterLocalStorageItem> originItem = new ArrayList<>();

    @Before
    public void initNewItemList() {
        newItem.add(new SearchInsertBean("", "", "", 21, "", 100, 211, 100, "", 0));
        newItem.add(new SearchInsertBean("", "", "", 19, "", 100, 191, 100, "", 0));
        newItem.add(new SearchInsertBean("", "", "", 18, "", 100, 181, 100, "", 0));
        newItem.add(new SearchInsertBean("", "", "", 12, "", 100, 120, 100, "", 0));
        newItem.add(new SearchInsertBean("", "", "", 11, "", 100, 111, 100, "", 0));
    }

    @Before
    public void initOriginList() {
        originItem.add(new CenterLocalStorageItem(20, 202));
        originItem.add(new CenterLocalStorageItem(18, 182));
        originItem.add(new CenterLocalStorageItem(16, 162));
        originItem.add(new CenterLocalStorageItem(14, 142));
        originItem.add(new CenterLocalStorageItem(12, 120));
        originItem.add(new CenterLocalStorageItem(10, 102));
    }

    @Test
    public void should_oldSyncStr_when_first_run() {
        String oldSyncStr = CenterDbUtils.INSTANCE.getSyncedMediaInfo();
        Assert.assertNotEquals("", oldSyncStr);
    }

    @Test
    public void should_sortTotalList_when_list_ready() throws Exception {
        CenterSyncManager syncManager = mock(CenterSyncManager.class);
        List<SearchInsertBean> sortedList = Whitebox.invokeMethod(syncManager, "sortTotalList", newItem);
        Assert.assertTrue(sortedList.get(0).getId() > sortedList.get(1).getId());
    }

    @Test
    public void should_diffList_when_list_ready() throws Exception {
        CenterSyncManager syncManager = mock(CenterSyncManager.class);
        List<SearchInsertBean> updateList = new ArrayList<>();
        List<Long> deleteList = new ArrayList<>();
        Whitebox.invokeMethod(syncManager, "diffList", newItem, originItem, updateList, deleteList);
        Assert.assertEquals(4, updateList.size());
        Assert.assertEquals(18, updateList.get(2).getId());
        Assert.assertEquals(4, deleteList.size());
        Assert.assertEquals(16, (long) deleteList.get(1));
    }

    @Test
    public void should_NotSync_when_diffListAndSync() throws Exception {
        int[] newData = {919,918,916,909};
        int[] oldData = {919,918,916,909};
        CenterSyncManager syncManager = mock(CenterSyncManager.class);
        List<SearchInsertBean> newList = new ArrayList<>();
        List<CenterLocalStorageItem> oldList = new ArrayList<>();
        SearchInsertBean searchInsertBean;
        CenterLocalStorageItem storageItem;

        for (int id:newData){
            searchInsertBean = new SearchInsertBean();
            searchInsertBean.setId(id);
            searchInsertBean.setDate_modified(id);
            newList.add(searchInsertBean);
        }

        for (int id:oldData){
            storageItem = new CenterLocalStorageItem(id,id);
            oldList.add(storageItem);
        }

        Whitebox.invokeMethod(syncManager, "diffListAndSync", newList, oldList);
    }
}
