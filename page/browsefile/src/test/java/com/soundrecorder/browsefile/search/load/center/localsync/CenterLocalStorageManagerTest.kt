/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.localsync

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class CenterLocalStorageManagerTest {
    private val originalList by lazy {
        mutableListOf(
            CenterLocalStorageItem(9, 0),
            CenterLocalStorageItem(8, 0),
            CenterLocalStorageItem(6, 0),
            CenterLocalStorageItem(4, 0),
            CenterLocalStorageItem(3, 0),
            CenterLocalStorageItem(2, 0),
            CenterLocalStorageItem(0, 0)
        )
    }
    private val updateList by lazy {
        mutableListOf(
            CenterLocalStorageItem(8, 0),
            CenterLocalStorageItem(5, 0),
            CenterLocalStorageItem(3, 0),
            CenterLocalStorageItem(10, 0),
            CenterLocalStorageItem(1, 0)
        )
    }
    private val deleteList by lazy {
        mutableListOf(8L, 5L, 2L, 1L)
    }

    @Test
    @kotlin.jvm.Throws(Exception::class)
    fun should_updateSyncList_when_first_run() {
        val originList = mutableListOf<CenterLocalStorageItem>().apply { addAll(originalList) }
        val newBean = if (originList.isNullOrEmpty()) {
            val newList = CenterLocalStorageManager.createStorageItemList(updateList, true)
            CenterLocalStorageBean(newList)
        } else {
            CenterLocalStorageManager.updateList(updateList, originList)
            CenterLocalStorageBean(originList)
        }

        Assert.assertEquals(10, newBean.originList?.size ?: -1)
        Assert.assertEquals(6, newBean.originList!![3].mediaId)
    }

    @Test
    @kotlin.jvm.Throws(Exception::class)
    fun should_deleteSyncList_when_first_run() {
        val originList = mutableListOf<CenterLocalStorageItem>().apply { addAll(originalList) }

        CenterLocalStorageManager.deleteList(deleteList.toList(), originList)
        Assert.assertEquals(5, originList.size)
    }
}