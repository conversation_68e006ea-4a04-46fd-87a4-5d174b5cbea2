package com.soundrecorder.browsefile.search.load.center

import android.content.Context
import android.os.Build
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowCenterDbUtils
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.browsefile.shadows.ShadowRecorderLogger
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mockito
import org.powermock.reflect.Whitebox

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowCenterDbUtils::class, ShadowOS12FeatureUtil::class,
        ShadowFeatureOption::class, ShadowRecorderLogger::class]
)
class CenterDbUtilsTest {
    private val DMP_ENABLE_STATUS_AVAILABLE = "available"
    private val DMP_ENABLE_STATUS_FATAL = "fatal"

    private val FILE_RELATIVE_PATH_BASE: String = "Music/Recordings/"
    private val FILE_RELATIVE_PATH_STANDARD: String = "Music/Recordings/Standard Recordings/"
    private val FILE_RELATIVE_PATH_MEETING: String = "Music/Recordings/Meeting Recordings/"
    private val FILE_RELATIVE_PATH_INTERVIEW: String = "Music/Recordings/Interview Recordings/"
    private val FILE_RELATIVE_PATH_CALL: String = "Music/Recordings/Call Recordings/"

    private val BUCKET_VALUE_STANDARD = "1"
    private val BUCKET_VALUE_INTERVIEW = "2"
    private val BUCKET_VALUE_MEETING = "3"
    private val BUCKET_VALUE_CALL = "4"

    private var mContext: Context? = null


    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun should_success_when_saveDmpEnableStatus() {
        CenterDbUtils.saveDmpEnableStatus(DMP_ENABLE_STATUS_AVAILABLE)
        Assert.assertTrue(CenterDbUtils.getDmpEnableStatus())
    }

    @Test
    fun should_returnFalse_when_getDmpEnableStatus() {
        CenterDbUtils.saveDmpEnableStatus(DMP_ENABLE_STATUS_FATAL)
        Assert.assertFalse(CenterDbUtils.getDmpEnableStatus())

        CenterDbUtils.saveDmpEnableStatus(null)
        Assert.assertFalse(CenterDbUtils.getDmpEnableStatus())
    }

    @Test
    fun should_returnTrue_when_isCenterSearchUsable() {
        val mockedCenterDbUtils = Mockito.mockStatic(CenterDbUtils::class.java)
        Mockito.`when`(CenterDbUtils.isCenterSearchUsable()).thenReturn(true)
        assert(CenterDbUtils.isCenterSearchUsable())
        mockedCenterDbUtils.close()
    }

    @Test
    fun should_returnTrue_when_isVersionNameAbove() {
        val mockedCenterDbUtils = Mockito.mockStatic(CenterDbUtils::class.java)
        Mockito.`when`(CenterDbUtils.isVersionNameAbove(anyString(), anyString())).thenReturn(true)
        Assert.assertTrue(
            CenterDbUtils.isVersionNameAbove(
                "1.0",
                CenterDbConstant.CENTER_DMP_PKG_NAME
            )
        )
        mockedCenterDbUtils.close()
    }

    @Test
    fun should_correct_when_saveSyncedMediaInfo() {
        CenterDbUtils.saveSyncedMediaInfo("mediaStr")
        val mediaInfo = CenterDbUtils.getSyncedMediaInfo()
        Assert.assertNotNull(mediaInfo)
        Assert.assertEquals(mediaInfo, "mediaStr")
    }

    @Test
    fun should_returnStringIsNull_when_getSyncedMediaInfo() {
        CenterDbUtils.saveSyncedMediaInfo(null)
        Assert.assertNull(CenterDbUtils.getSyncedMediaInfo())
    }

    @Test
    fun should_returnCorrect_when_calBucketByRelativePath() {
        val bucket = CenterDbUtils.calBucketByRelativePath(FILE_RELATIVE_PATH_BASE)
        Assert.assertEquals(bucket, BUCKET_VALUE_STANDARD)

        val bucketStandard = CenterDbUtils.calBucketByRelativePath(FILE_RELATIVE_PATH_STANDARD)
        Assert.assertEquals(bucketStandard, BUCKET_VALUE_STANDARD)

        val bucketInterview = CenterDbUtils.calBucketByRelativePath(FILE_RELATIVE_PATH_INTERVIEW)
        Assert.assertEquals(bucketInterview, BUCKET_VALUE_INTERVIEW)

        val bucketMetting = CenterDbUtils.calBucketByRelativePath(FILE_RELATIVE_PATH_MEETING)
        Assert.assertEquals(bucketMetting, BUCKET_VALUE_MEETING)

        val bucketCall = CenterDbUtils.calBucketByRelativePath(FILE_RELATIVE_PATH_CALL)
        Assert.assertEquals(bucketCall, BUCKET_VALUE_CALL)

        val bucketWrong =
            CenterDbUtils.calBucketByRelativePath(FILE_RELATIVE_PATH_BASE + FILE_RELATIVE_PATH_CALL)
        Assert.assertNull(bucketWrong)
    }

    @Test
    fun should_recorderBaseDirNotNull_when_getRelativePathFromData() {
        val phoneDir = "/storage/emulated/0/"
        Whitebox.setInternalState(CenterDbUtils.javaClass, "recorderBaseDir", phoneDir)
        val recorderBaseDir =
            Whitebox.getInternalState<String?>(CenterDbUtils.javaClass, "recorderBaseDir")
        Assert.assertNotNull(recorderBaseDir)

        val relativePath =
            CenterDbUtils.getRelativePathFromData(phoneDir + "Music/Recordings/1.mp3")
        Assert.assertEquals("Music/Recordings", relativePath)

        val relativePathEmpty =
            CenterDbUtils.getRelativePathFromData(null)
        Assert.assertEquals(relativePathEmpty, "")
    }
}