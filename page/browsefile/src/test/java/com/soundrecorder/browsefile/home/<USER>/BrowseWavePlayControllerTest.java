package com.soundrecorder.browsefile.home.item;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import com.soundrecorder.browsefile.home.item.item.BrowseWavePlayController;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.base.PlayerHelperCallback;
import com.soundrecorder.common.buryingpoint.BuryingPoint;
import com.soundrecorder.player.status.PlayStatus;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class BrowseWavePlayControllerTest {
    @Test
    public void should_return_state_when_init() {
        PlayerHelperCallback helperCallback = Mockito.mock(PlayerHelperCallback.class);
        BrowseWavePlayController manager = new BrowseWavePlayController(helperCallback);
        manager.doInit();
        Assert.assertTrue(manager.getHasInit());
    }

    @Test
    public void should_return_state_when_onPlayStateChange() throws Exception {
        PlayerHelperCallback helperCallback = Mockito.mock(PlayerHelperCallback.class);
        BrowseWavePlayController manager = Mockito.spy(new BrowseWavePlayController(helperCallback));
        String funName = "onPlayStateChange";
        Whitebox.invokeMethod(manager, funName, PlayStatus.PLAYER_STATE_PLAYING);
        Whitebox.invokeMethod(manager, funName, PlayStatus.PLAYER_STATE_PAUSE);
        manager.doInit();
        Assert.assertTrue(manager.getHasInit());
        Whitebox.invokeMethod(manager, funName, PlayStatus.PLAYER_STATE_HALTON);
    }

    @Test
    public void should_equals_when_seekTime() {
        BrowseWavePlayController playController = new BrowseWavePlayController(null);
        playController.doSeekTime(1000,true);
        Assert.assertTrue(1000L == playController.getCurrentTimeMillis().getValue());

        playController.doSeekTime(2000, false);
        Assert.assertTrue(1000L == playController.getCurrentTimeMillis().getValue());
    }

    @Test
    public void should_return_state_when_release() {
        PlayerHelperCallback helperCallback = Mockito.mock(PlayerHelperCallback.class);
        BrowseWavePlayController manager = new BrowseWavePlayController(helperCallback);
        manager.releasePlay();
        Assert.assertEquals(PlayStatus.PLAYER_STATE_HALTON, (int) manager.getPlayerState().getValue());
    }

    @Test
    public void should_return_state_when_onRelease() {
        PlayerHelperCallback helperCallback = Mockito.mock(PlayerHelperCallback.class);
        BrowseWavePlayController manager = new BrowseWavePlayController(helperCallback);
        manager.onRelease(true);
        Assert.assertFalse(manager.getHasInit());
    }

    @Test
    public void should_correct_when_doPlayBtnClick() {
        PlayerHelperCallback helperCallback = Mockito.mock(PlayerHelperCallback.class);
        BrowseWavePlayController manager = new BrowseWavePlayController(helperCallback);
        manager.getMIsTouchSeekbar().setValue(true);
        manager.doPlayBtnClick();
        Assert.assertFalse(manager.getHasInit());

        manager.getMIsTouchSeekbar().setValue(false);
        manager.doPlayBtnClick();
        Assert.assertTrue(manager.getHasInit());
    }

    @Test
    public void should_correct_when_doContinuePlay() {
        MockedStatic<BuryingPoint> mockedStatic = Mockito.mockStatic(BuryingPoint.class);
        PlayerHelperCallback helperCallback = Mockito.mock(PlayerHelperCallback.class);
        BrowseWavePlayController manager = new BrowseWavePlayController(helperCallback);
        manager.doContinuePlay();
        manager.doPausePlay();
        mockedStatic.verify(() -> BuryingPoint.addRecordPlayState(anyString()), times(2));
    }

    @Test
    public void should_correct_when_getTimerPeriod() {
        PlayerHelperCallback helperCallback = Mockito.mock(PlayerHelperCallback.class);
        BrowseWavePlayController manager = new BrowseWavePlayController(helperCallback);
        Assert.assertTrue(manager.getTimerPeriod() > 0);
    }

    @Test
    public void should_correct_when_doOnRelease() {
        PlayerHelperCallback helperCallback = Mockito.mock(PlayerHelperCallback.class);
        BrowseWavePlayController manager = new BrowseWavePlayController(helperCallback);
        manager.doInit();
        Assert.assertTrue(manager.getHasInit());

        manager.doOnRelease(true);
        Assert.assertFalse(manager.getHasInit());
    }
}

