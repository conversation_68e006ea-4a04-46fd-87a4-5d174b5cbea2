apply from:"../../common_build.gradle"

android {
    sourceSets {
        main {
            aidl.srcDirs = ['src/main/java']
            assets.srcDirs = ['src/main/assets']
        }
    }
    namespace "com.soundrecorder.miniapp"
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.extensions
    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.panel

    // Koin for Android
    implementation(libs.koin)
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':component:recorderService')
    implementation project(':common:RecorderLogBase')
}
