/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: WaveAdapterTest
 * Description:
 * Version: 1.0
 * Date: 2023/4/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/4/18 1.0 create
 */

package com.soundrecorder.miniapp.view.wave

import android.os.Build
import android.widget.FrameLayout
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class WaveAdapterTest {
    @Test
    fun getItemViewTypeTest() {
        val waveRecyclerView =
            Mockito.spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        val waveAdapter = WaveAdapter(waveRecyclerView)
        Assert.assertTrue(waveAdapter.getItemViewType(0) == WaveAdapter.VIEW_TYPE_HEADER)
        Assert.assertTrue(waveAdapter.getItemViewType(Int.MAX_VALUE - 1) == WaveAdapter.VIEW_TYPE_FOOTER)
        Assert.assertTrue(waveAdapter.getItemViewType(1) == WaveAdapter.VIEW_TYPE_WAVE)
    }

    @Test
    fun onCreateViewHolderTest() {
        val waveRecyclerView =
            Mockito.spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        val waveAdapter = WaveAdapter(waveRecyclerView)
        waveAdapter.onCreateViewHolder(
            FrameLayout(ApplicationProvider.getApplicationContext()),
            WaveAdapter.VIEW_TYPE_HEADER
        )
        waveAdapter.onCreateViewHolder(
            FrameLayout(ApplicationProvider.getApplicationContext()),
            WaveAdapter.VIEW_TYPE_WAVE
        )
        waveAdapter.onCreateViewHolder(
            FrameLayout(ApplicationProvider.getApplicationContext()),
            WaveAdapter.VIEW_TYPE_FOOTER
        )
    }

    @Test
    fun onBindViewHolderTest() {
        val waveRecyclerView =
            Mockito.spy(WaveRecyclerView(ApplicationProvider.getApplicationContext()))
        val waveAdapter = WaveAdapter(waveRecyclerView)
        waveAdapter.onBindViewHolder(
            waveAdapter.onCreateViewHolder(
                FrameLayout(ApplicationProvider.getApplicationContext()), WaveAdapter.VIEW_TYPE_WAVE
            ), 1
        )
    }
}