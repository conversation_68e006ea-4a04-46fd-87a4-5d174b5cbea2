/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: SeedlingApi
 Description:
 Version: 1.0
 Date: 2023/3/6
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2023/3/6 1.0 create
 */

package com.soundrecorder.common.card.api

import android.content.Context
import android.os.Handler
import android.os.Looper
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.util.SeedlingTool
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.card.JsonUtils
import com.soundrecorder.common.card.RecordSeedlingCardWidgetProvider
import com.soundrecorder.common.card.RecordSeedlingSmallCardWidgetProvider
import com.soundrecorder.common.card.SeedingCardProcess
import com.soundrecorder.common.card.seedling.SeedlingConst
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardStateProcessor
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.modulerouter.STATUS_BAR_SUPPORT_FLUID_CARD
import com.soundrecorder.modulerouter.STATUS_BAR_SUPPORT_OLD
import com.soundrecorder.modulerouter.STATUS_BAR_SUPPORT_SEEDLING_CARD
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import org.jetbrains.annotations.VisibleForTesting
import org.json.JSONObject

object SeedlingApi : SeedingInterface {

    private const val TAG = "SeedlingApi"

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    @VisibleForTesting
    var mSeedingCardProcess: SeedingCardProcess? = null

    override fun init() {
        DebugUtil.d(TAG, "init  mSeedingCardProcess")
        mSeedingCardProcess = SeedingCardProcess()
        mSeedingCardProcess?.initHandlerThread()
    }

    override fun doActionInThread(callback: (() -> Unit)) {
        mSeedingCardProcess?.doActionInThread(callback)
    }

    override fun getStatusBarSupportType(context: Context, callback: ((Int) -> Unit)) {
        mSeedingCardProcess?.doActionInThread {
            getStatusBarSupportTypeDirectly(context, callback)
        }
    }

    /**
     * 获取支持的状态栏类型，未切换到子线程！！！
     */
    @JvmStatic
    fun getStatusBarSupportTypeDirectly(context: Context, callback: ((Int) -> Unit)) {
        if (SeedlingSdkApi.isSupportFluidCloud(context)) {
            callback.invoke(STATUS_BAR_SUPPORT_FLUID_CARD)
        } else if (SeedlingSdkApi.isSupportSystemSendIntent(context)) {
            callback.invoke(STATUS_BAR_SUPPORT_SEEDLING_CARD)
        } else {
            callback.invoke(STATUS_BAR_SUPPORT_OLD)
        }
    }

    /**
     * 设置流体云sdk支持与否的回调，未切换到子线程！！！
     */
    override fun initSupportFluidCardCallback(context: Context, callback: (Boolean?, Boolean) -> Unit) {
        SeedlingSdkApi.initSupportFluidCardCallback(context, callback)
    }

    /**
     * 获取是否支持流体云，未切换到子线程！！！
     */
    override fun isSupportFluidCloud(context: Context): Boolean {
        return SeedlingSdkApi.isSupportFluidCloud(context)
    }

    /**
     * 获取是否支持13.2余光交互胶囊，未切换到子线程！！！
     */
    override fun isSupportSystemSendIntent(context: Context): Boolean {
        return SeedlingSdkApi.isSupportSystemSendIntent(context)
    }

    override fun sendShowSeedlingStatusBar(
        originData: JSONObject?,
        callback: ((Boolean) -> Unit)?
    ) {
        DebugUtil.d(TAG, "sendShowSeedlingStatusBar")
        mSeedingCardProcess?.sendShowSeedlingStatusBar(originData, callback)
    }

    override fun sendHideSeedlingStatusBar(
        forceDismiss: Boolean,
        callback: ((Boolean) -> Unit)?
    ) {
        DebugUtil.d(TAG, "sendHideSeedlingStatusBar, forceDismiss= $forceDismiss")
        if (forceDismiss) {
            CoroutineUtils.doInIOThread({
                SeedlingSdkApi.hideSeedlingStatusBar(null)
            }, CoroutineUtils.mainScope)
        } else {
            mSeedingCardProcess?.sendHideSeedlingStatusBar(callback)
        }
    }

    override fun sendUpdateCardData(card: Any, jsonData: JSONObject, updateAll: Boolean) {
        DebugUtil.d(TAG, "sendUpdateCardData, updateAll = $updateAll")
        mSeedingCardProcess?.sendUpdateCardData(card, jsonData, updateAll)
    }

    /**
     * 调用14.0泛在胶囊卡片展示结果的回调，未切换到子线程！！！
     */
    override fun registerResultCallBack() {
        DebugUtil.d(TAG, "registerResultCallBack")
        SeedlingSdkApi.registerResultCallBack()
    }

    override fun unRegisterResultCallBack() {
        DebugUtil.d(TAG, "unRegisterResultCallBack")
        //恢复卡片数据
        val jsonData = JSONObject()
        JsonUtils.updateJsonDataForInitState(jsonData)
        // 添加延时，确保 done 停留一段时间
        Handler(Looper.getMainLooper()).postDelayed({
            RecordSeedlingSmallCardWidgetProvider.refreshSeedlingData(
                jsonData = jsonData,
                unRegister = true
            )
        }, RecordSeedlingSmallCardWidgetProvider.DONE_DELAY_MILLIS)


        mSeedingCardProcess?.unRegisterResultCallBack()
    }

    override fun refreshSeedlingData(jsonData: JSONObject?) {
        DebugUtil.d(TAG, "refreshSeedlingData, jsonData length = ${jsonData?.length()}")
        RecordSeedlingCardWidgetProvider.refreshSeedlingData(jsonData = jsonData)
        //借用流体云数据刷新
        RecordSeedlingSmallCardWidgetProvider.refreshSeedlingData(
            jsonData = jsonData,
            unRegister = false
        )
    }

    /**
     * 一加外销 2x1 小卡，在点击录音时及时刷新界面
     */
    override fun forceRefreshSeedlingData(
        seedlingCard: Any?,
        jsonData: JSONObject?,
        updateAll: Boolean
    ) {
        (seedlingCard as? SeedlingCard)?.let {
            SeedlingTool.updateAllCardData(it, jsonData)
        } ?: RecordSeedlingSmallCardWidgetProvider.getSeedlingCard()?.let { SeedlingTool.updateAllCardData(it, jsonData) }
    }

    override fun getCardServiceId(): String {
        DebugUtil.d(TAG, "getCardServiceId")
        return SeedlingConst.SEEDLING_CARD_SERVICE_ID
    }

    override fun release() {
        DebugUtil.d(TAG, "release")
        mSeedingCardProcess?.release()
        mSeedingCardProcess = null
    }

    override fun sendRecordDeleteEvent() {
        if (checkNeedSendCardEvent()) {
            AppCardStateProcessor.onRecordDelete()
        } else {
            DebugUtil.w(
                TAG,
                "sendRecordDeleteEvent ignore.isVOrLater=${BaseUtil.isAndroidVOrLater} exp=${BaseUtil.isEXP()}"
            )
        }
    }

    override fun sendRecordAddEvent() {
        if (checkNeedSendCardEvent()) {
            AppCardStateProcessor.onRecordAdd()
        } else {
            DebugUtil.w(
                TAG,
                "sendRecordDeleteEvent ignore.isVOrLater=${BaseUtil.isAndroidVOrLater} exp=${BaseUtil.isEXP()}"
            )
        }
    }

    override fun sendRecordFileInnerRename(mediaId: Long) {
        if (checkNeedSendCardEvent()) {
            AppCardStateProcessor.onRecordRename(mediaId)
        } else {
            DebugUtil.d(TAG, "sendRecordFileInnerRename ignore}")
        }
    }

    override fun sendRecordRecoverEvent() {
        if (checkNeedSendCardEvent()) {
            AppCardStateProcessor.onRecordRecover()
        } else {
            DebugUtil.w(
                TAG,
                "sendRecordDeleteEvent ignore.isVOrLater=${BaseUtil.isAndroidVOrLater} exp=${BaseUtil.isEXP()}"
            )
        }
    }

    override fun onConvertProgressChanged(
        mediaId: Long,
        uploadProgress: Int,
        convertProgress: Int,
        serverPlanCode: Int
    ) {
        AppCardStateProcessor.onConvertProgressChanged(
            mediaId,
            uploadProgress,
            convertProgress,
            serverPlanCode
        )
    }

    override fun onConvertStatusChange(
        mediaId: Long,
        uploadStatus: Int,
        convertStatus: Int,
        errorMessage: String
    ) {
        AppCardStateProcessor.onConvertStatusChange(
            mediaId,
            uploadStatus,
            convertStatus,
            errorMessage
        )
    }

    override fun onConvertTextReceived(mediaId: Long) {
        AppCardStateProcessor.onConvertTextReceived(mediaId)
    }

    override fun onSummaryStart(mediaId: Long?) {
        AppCardStateProcessor.onGenSummaryStart(mediaId)
    }

    override fun onSummaryProgressEnd(
        mediaId: Long?,
        noteId: String?,
        recordUUID: String?,
        asrErrorCode: Int?,
        summaryErrorCode: Int?
    ) {
        AppCardStateProcessor.onSummaryProgressEnd(
            mediaId,
            noteId,
            recordUUID,
            asrErrorCode,
            summaryErrorCode
        )
    }

    /**
     * android v 并且非外销，支持转摘要或者支持转文本
     */
    private fun checkNeedSendCardEvent(): Boolean {
        val isAndroidVOrLater = BaseUtil.isAndroidVOrLater
        val isEXP = BaseUtil.isEXP()
        val supportSummary = summaryApi?.getSupportRecordSummaryValue()
        val supportConvertText = FunctionOption.loadSpeechToTextFeature()
        DebugUtil.d(
            TAG,
            "checkNeedSendCardEvent isAndroidVOrLater=$isAndroidVOrLater isEXP=$isEXP " +
                    "supportSummary=$supportSummary supportConvertText=$supportConvertText"
        )
        return isAndroidVOrLater && !isEXP && (supportSummary?.value == true || supportConvertText)
    }

    override fun hasReleased(): Boolean {
        return mSeedingCardProcess == null
    }
}