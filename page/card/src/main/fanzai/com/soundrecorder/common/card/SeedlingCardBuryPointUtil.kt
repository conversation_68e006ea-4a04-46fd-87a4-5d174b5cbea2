/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingCardBuryPointUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction

object SeedlingCardBuryPointUtil {

    @JvmStatic
    fun addSeedlingCardFluidRecordBtnClickEvent() {
        val eventInfo = HashMap<String?, String?>()
        eventInfo[RecorderUserAction.KEY_SEEDLING_CARD_FLUID_RECORD_BTN_CLICK] =
            RecorderUserAction.DEFAULT_VALUE
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(),
            RecorderUserAction.USER_ACTION_SEEDLING_CARD,
            RecorderUserAction.EVENT_SEEDLING_CARD_FLUID,
            eventInfo, false
        )
        DebugUtil.i(CloudStaticsUtil.TAG, "addSeedlingCardFluidRecordBtnClickEvent")
    }

    @JvmStatic
    fun addSeedlingCardMarkBtnClickEvent() {
        val eventInfo = HashMap<String?, String?>()
        eventInfo[RecorderUserAction.KEY_SEEDLING_CARD_FLUID_MARK_BTN_CLICK] =
            RecorderUserAction.DEFAULT_VALUE
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(),
            RecorderUserAction.USER_ACTION_SEEDLING_CARD,
            RecorderUserAction.EVENT_SEEDLING_CARD_FLUID,
            eventInfo, false
        )
        DebugUtil.i(CloudStaticsUtil.TAG, "addSeedlingCardMarkRecordBtnClickEvent")
    }
}