/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  KeyMagicMessengerService.kt
 * * Description : 魔方按键交互service
 * * Version     : 1.0
 * * Date        : 2024/12/02
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import com.oplus.keyguard.OplusKeyguardStyleManager
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.utils.BrightScreenUtil
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector

class KeyMagicMessengerService : Service() {

    companion object {
        const val TAG = "KeyMagicMessengerService"
        private const val MSG_ACTION_BUTTON_CLICK = 0x01
        private const val MSG_ACTION_BUTTON_LONG_PRESS = 0x02

        private const val SIDE_TEXT = "side_text"
        private const val ONLY_SHOW_SIDE_ANIM = "only_show_side_anim"
        private const val APP_NAME = "app_name"
        private const val FLUID_CLOUD_TEXT = "fluid_cloud_text"
        private const val ICON_RES_ID = "icon_res_id"
        private const val START_RESULT = "startResult"

        private const val CLASSIC_AOD = 2
        private const val WORKSHOP_AOD = 3
        private const val PANORAMIC_AOD = 4
    }

    private var mContext: Context? = null

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    override fun onCreate() {
        super.onCreate()
        DebugUtil.d(TAG, "onCreate.")
        mContext = this.applicationContext
    }

    private val mMessenger: Messenger = Messenger(object : Handler(Looper.getMainLooper()) {

        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_ACTION_BUTTON_CLICK -> {
                    //Trace.traceBegin(1, "getGestureMsg")
                    val bundle = msg.data
                    val name = bundle.getString("key")
                    DebugUtil.d(TAG, "key= $name")
                    //Trace.traceEnd(1)

                    val replyMsg = Message.obtain()
                    replyMsg.what = MSG_ACTION_BUTTON_CLICK
                    val bundleToC = Bundle()
                    if (recorderViewModelApi?.isAlreadyRecording() == true) {
                        bundleToC.putString(SIDE_TEXT, mContext?.getString(R.string.cube_button_long_press_record_stop)) //侧边提醒文案
                        bundleToC.putString(ONLY_SHOW_SIDE_ANIM, "true")
                    } else {
                        bundleToC.putString(SIDE_TEXT, mContext?.getString(R.string.cube_button_long_press_record_start_v2)) //侧边提醒文案
                    }
                    bundleToC.putString(APP_NAME, BaseUtil.getPackageName())
                    bundleToC.putString(FLUID_CLOUD_TEXT, mContext?.getString(R.string.app_name_main)) //流体云文案
                    bundleToC.putInt(ICON_RES_ID, com.soundrecorder.common.R.drawable.ic_cube_record_icon) //流体云图标

                    DebugUtil.d(TAG, "MSG_ACTION_BUTTON_CLICK send to gesture：")
                    replyMsg.data = bundleToC
                    msg.replyTo.send(replyMsg)

                    if (OS12FeatureUtil.isColorOS16OrLater()) {
                        val manager = OplusKeyguardStyleManager.getInstance(mContext)
                        DebugUtil.d(TAG, "manager.aodType ： ${manager.aodType}")
                        //0:不开AOD 2:普通AOD 3:灵感AOD 4:全景AOD
                        if (manager.aodType == CLASSIC_AOD || manager.aodType == WORKSHOP_AOD || manager.aodType == PANORAMIC_AOD) {
                            return
                        }
                    }

                    val wakeLock = mContext?.let { BrightScreenUtil.acquireWakeLock(it, TAG) }
                    BrightScreenUtil.releaseWakeLock(wakeLock)
                }

                MSG_ACTION_BUTTON_LONG_PRESS -> {
                    val replyMsg = Message.obtain()
                    replyMsg.what = MSG_ACTION_BUTTON_LONG_PRESS
                    val bundleToC = Bundle()
                    val canStart = false
                    if (!canStart) {
                        /*当前场景无法启动 业务的流体云*/
                        bundleToC.putString(START_RESULT, "reply")
                        /*流体云图片*/
                        bundleToC.putString(APP_NAME, BaseUtil.getPackageName())
                        bundleToC.putInt(ICON_RES_ID, com.soundrecorder.common.R.drawable.ic_launcher_recorder)
                        bundleToC.putString(ONLY_SHOW_SIDE_ANIM, "true")
                        /*流体云展示的文字 默认为不可用*/
                        //bundleToC.putString("long_press_err_text", "关闭")
                        DebugUtil.d(TAG, "MSG_ACTION_BUTTON_LONG_PRESS send to gesture：")
                        replyMsg.data = bundleToC
                        msg.replyTo.send(replyMsg)
                    }
                }
            }
            super.handleMessage(msg)
        }
    })

    override fun onBind(intent: Intent?): IBinder {
        DebugUtil.d(TAG, "onBind, intent:$intent")
        return mMessenger.binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        return super.onUnbind(intent)
        //WakeLockManager.getInstance(mContext, null).releaseWakeLock()
    }
}

