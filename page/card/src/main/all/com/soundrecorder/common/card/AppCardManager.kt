/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardManager
 Description:
 Version: 1.0
 Date: 2023/3/22
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2023/3/22 1.0 create
 */

package com.soundrecorder.common.card

import android.content.Context
import com.oplus.cardwidget.domain.action.CardWidgetAction
import com.oplus.cardwidget.domain.pack.BaseDataPackByName
import com.oplus.smartenginehelper.dsl.DSLCoder

abstract class AppCardManager {

    private val baseDataPack by lazy {
        object : BaseDataPackByName() {
            override fun onPack(coder: DSLCoder, widgetCode: String): String {
                coder.setCustomData(getCustomDataId(), getCustomDataTag(), getCustomData(widgetCode))
                return ""
            }
        }
    }

    fun postUpdateCommand(ctx: Context, widgetCode: String) {
        CardWidgetAction.postUpdateCommand(ctx, baseDataPack, widgetCode)
    }

    /**
     * 速览卡片布局id
     */
    abstract fun getCustomDataId(): String

    /**
     * 速览卡片数据解析对应的key
     */
    abstract fun getCustomDataTag(): String

    /**
     * 速览卡片数据
     */
    abstract fun getCustomData(widgetCode: String): String
}