<?xml version="1.0" encoding="utf-8"?>
<card-provider xmlns:app="http://schemas.android.com/apk/res-auto"
    app:groupImage="@drawable/ic_launcher_recorder"
    app:groupTitle="@string/app_name_main"
    app:minUiEngineVer="12053"
    app:oplus_card_dragonfly_category="normal"
    app:oplus_card_dragonfly_secure="false"
    app:oplus_card_name="@string/app_name_main"
    app:oplus_card_protocol="PROTOBUF"
    app:oplus_card_size="n2four"
    app:oplus_card_type="777770016"
    app:oplus_card_replaced_by_miniapp="true"/>

    <!--oplus_card_replaced_by_miniapp:
    为避免火烈鸟资源库上同时显示，需做隔离处理;默认 false;
    当该属性为true、并且系统支持资源库（com.oplus.secondaryhome.enable_launcher_view），则该卡片配置信息不传给第二桌面。-->