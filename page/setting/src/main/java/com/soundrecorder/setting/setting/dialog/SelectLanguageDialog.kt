/**
 * Copyright (C), 2008-2025 OPLUS Mobile Comm Corp., Ltd.
 * File: SelectLanguageDialog
 * Description:
 * Version: 1.0
 * Date: 2025/6/4
 * Author: <EMAIL>
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 */
package com.soundrecorder.setting.setting.dialog

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.adapter.ChoiceListAdapter
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.support.dialog.R
import kotlin.concurrent.thread

/**
 * 语言选择弹窗
 */
class SelectLanguageDialog(val activity: Activity) {

    companion object {
        private const val TAG = "SelectLanguageDialog"
    }
    private var mDialog: AlertDialog? = null
    var dialogItemListener: DialogItemClickListener? = null
    private val recorderViewModelAction by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    fun showDialog(selectedLanguage: String) {
        DebugUtil.d(TAG, "showDialog")
        // 首先判断sp是否有语种列表，若没有，优先拿本地语种配置
        val spLanguageSet = PrefUtil.getStringSet(BaseApplication.getAppContext(), PrefUtil.OPLUS_AI_TEXT_ASR_SUPPORT_LANGUAGE_LIST, null)
        if (spLanguageSet == null || spLanguageSet.isEmpty()) {
            DebugUtil.d(TAG, "showDialog spLanguageList is null getSupportLanguageList from local")
            val regionSupportedLanguages = LanguageUtil.getRegionSupportedLanguages()
            if (regionSupportedLanguages.isNotEmpty()) {
                handleShow(regionSupportedLanguages.toList(), selectedLanguage)
            }
        }
        recorderViewModelAction?.getSupportLanguageList { languageCodes ->
            DebugUtil.d(TAG, "showDialog getSupportLanguageList langCodeList size=${languageCodes?.size}")
            if (languageCodes.isNullOrEmpty()) {
                return@getSupportLanguageList
            }
            handleShow(languageCodes, selectedLanguage)
        }
    }

    private fun handleShow(languageCodes: List<String>, selectedLanguage: String) {
        val languageNames = LanguageUtil.getAsrLangMap(activity, languageCodes).values.toTypedArray()
        DebugUtil.d(TAG, "showDialog languageNames size=${languageNames.size}")
        val checkboxStates = BooleanArray(languageCodes.size) { false }
        val disableStatus = BooleanArray(languageCodes.size) { false }

        val selectedPos = languageCodes.indexOf(selectedLanguage).coerceAtLeast(0)
        DebugUtil.d(TAG, "showDialog selectedPos=$selectedPos")
        checkboxStates[selectedPos] = true

        val singleChoiceListAdapter = ChoiceListAdapter(
            activity,
            R.layout.coui_select_dialog_singlechoice,
            languageNames,
            null,
            checkboxStates,
            disableStatus,
            false
        )
        activity.runOnUiThread {
            if (activity.isFinishing || activity.isDestroyed) {
                return@runOnUiThread
            }
            mDialog?.let {
                if (it.isShowing) {
                    DebugUtil.d(TAG, "showDialog dialog is showing, dismiss it")
                    it.dismiss()
                }
            }
            mDialog = COUIAlertDialogBuilder(
                activity,
                R.style.COUIAlertDialog_BottomAssignment
            )
                .setBlurBackgroundDrawable(true)
                .setTitle(activity.getString(com.soundrecorder.common.R.string.speech_language))
                .setAdapter(singleChoiceListAdapter) { _, which ->
                    dialogItemListener?.click(languageCodes[which])
                    release()
                }
                .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
                .show()
            ViewUtils.updateWindowLayoutParams(mDialog?.window)
        }
    }


    fun release() {
        mDialog?.dismiss()
        dialogItemListener = null
        mDialog = null
    }

    interface DialogItemClickListener {
        fun click(languageCode: String)
    }
}
