<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="@string/app_name_main">

    <androidx.preference.Preference
        android:layout="@layout/preference_header"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="pref_category_record_sync"
        app:isFirstCategory="true"
        android:title="@string/playback_audio">
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_setting_record_sync"
            android:persistent="false"
            android:summary=""
            app:couiAssignment="@string/did_not_open_of_settings"
            android:title="@string/sync_record" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_setting_record_format"
            android:persistent="false"
            android:title="@string/record_audio_format"
            app:coui_jump_mark="@drawable/coui_pop_up_next"
            app:isPreferenceVisible="false" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_setting_record_mode"
            android:persistent="false"
            android:title="@string/record_mode"
            app:isPreferenceVisible="false"
            app:coui_jump_mark="@drawable/coui_pop_up_next"
            app:endRedDotMode="0"/>

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_setting_record_for_third_app"
            android:persistent="false"
            android:title="@string/record_audio_for_third_app_v2"
            android:summary="@string/settings_main_summary_v1"
            app:isPreferenceVisible="false" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="pref_category_convert_text"
        android:title="@string/convert_text"
        app:isPreferenceVisible="false">

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_setting_convert_text"
            android:persistent="false"
            android:title="@string/speech_language"
            app:coui_jump_mark="@drawable/coui_pop_up_next"
            app:endRedDotMode="0"
            app:couiAssignment="@string/convert_record_default_simplified_chinese"/>

    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="pref_category_other"
        android:title="@string/setting_others">

        <com.soundrecorder.setting.widget.SmartNamePreference
            android:key="pref_setting_smart_generation"
            android:persistent="false"
            android:defaultValue="false"
            android:summary="@string/record_intelligent_generation_description"
            android:title="@string/record_intelligent_generation" />

        <com.coui.appcompat.preference.COUISwitchPreference
            android:defaultValue="true"
            android:key="pref_setting_record_picture_recommendation"
            android:persistent="false"
            android:summary="@string/photo_mark_recommend_setting_summary"
            android:title="@string/photo_mark_recommend_setting"
            app:isPreferenceVisible="false" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory android:key="pref_category_version" >

        <com.soundrecorder.setting.widget.UnClickablePreference android:key="versionCode" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_record_about"
            android:persistent="false"
            android:title="@string/record_about_new" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/privacy_policy_settings_collection_key"
            android:persistent="false"
            app:isPreferenceVisible="false"
            android:title="@string/privacy_policy_collection_of_personal_information_express_checklist" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/privacy_policy_settings_share_key"
            android:persistent="false"
            app:isPreferenceVisible="false"
            android:title="@string/privacy_policy_third_party_information_sharing_checklist" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/privacy_policy_settings_key"
            android:persistent="false"
            android:title="@string/privacy_policy_settings_title" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_record_feed_back"
            android:persistent="false"
            android:title="@string/feed_back_title" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <androidx.preference.Preference
        android:layout="@layout/preference_footer"
        android:selectable="false" />
</androidx.preference.PreferenceScreen>