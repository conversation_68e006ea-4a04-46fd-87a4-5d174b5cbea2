/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleRecyclerViewUtils.kt
 * Description:
 *     The helper class for inserting ui height.
 *
 * Version: 1.0
 * Date: 2025-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-06-04   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record

import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil

object SubtitleRecyclerViewUtils {
    private const val TAG = "SubtitleRecyclerViewUtils"
    @JvmStatic
    fun getVisibleHeight(recyclerView: RecyclerView): Int {
        return recyclerView.height - recyclerView.paddingTop - recyclerView.paddingBottom
    }

    @JvmStatic
    fun estimateTotalHeight(recyclerView: RecyclerView): Int {
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return 0
        var totalHeight = 0
        for (i in 0 until layoutManager.itemCount) {
            val view = layoutManager.findViewByPosition(i) ?: continue
            totalHeight += view.height
        }
        return totalHeight
    }

    /**
     * 滑动到RecyclerView的最底部
     * 如果最后一个item的高度大于屏幕高度，则滑动到该item的最底部
     * 使用立即滚动，无动画效果
     */
    @JvmStatic
    fun scrollToBottom(recyclerView: COUIRecyclerView, adapter: RecordSubtitleAdapter) {
        val itemCount = adapter.itemCount
        if (itemCount <= 0) {
            DebugUtil.d(TAG, "scrollToBottom: itemCount is 0, no need to scroll")
            return
        }

        val lastPosition = itemCount - 1
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager
        if (layoutManager == null) {
            DebugUtil.e(TAG, "scrollToBottom: layoutManager is null")
            return
        }

        val visibleHeight = getVisibleHeight(recyclerView)
        DebugUtil.d(TAG, "scrollToBottom: visibleHeight=$visibleHeight, lastPosition=$lastPosition, itemCount=$itemCount")

        // 在主线程的下一个循环中执行滚动操作
        recyclerView.post {
            try {
                // 先滚动到最后一个item，使其可见
                layoutManager.scrollToPosition(lastPosition)

                // 等待布局完成后进行精确的无动画滚动
                recyclerView.post {
                    scrollToBottomImmediatelyInternal(recyclerView, layoutManager, lastPosition, visibleHeight)
                }
            } catch (e: Exception) {
                DebugUtil.e(TAG, "scrollToBottom error: ${e.message}")
                // 异常时使用fallback方案
                try {
                    layoutManager.scrollToPositionWithOffset(lastPosition, Int.MIN_VALUE)
                } catch (fallbackException: Exception) {
                    DebugUtil.e(TAG, "scrollToBottom fallback error: ${fallbackException.message}")
                }
            }
        }
    }

    /**
     * 内部方法：立即滚动到底部的具体实现
     */
    @JvmStatic
    private fun scrollToBottomImmediatelyInternal(
        recyclerView: COUIRecyclerView,
        layoutManager: LinearLayoutManager,
        lastPosition: Int,
        visibleHeight: Int
    ) {
        try {
            val lastView = layoutManager.findViewByPosition(lastPosition)
            if (lastView == null) {
                DebugUtil.w(TAG, "scrollToBottomImmediatelyInternal: lastView is null, using fallback")
                layoutManager.scrollToPositionWithOffset(lastPosition, Int.MIN_VALUE)
                return
            }

            val lastItemHeight = lastView.height
            val lastItemTop = layoutManager.getDecoratedTop(lastView)
            val lastItemBottom = layoutManager.getDecoratedBottom(lastView)

            DebugUtil.d(TAG, "scrollToBottomImmediatelyInternal: lastItemHeight=$lastItemHeight, visibleHeight=$visibleHeight")
            DebugUtil.d(TAG, "scrollToBottomImmediatelyInternal: lastItemTop=$lastItemTop, lastItemBottom=$lastItemBottom")

            if (lastItemHeight > visibleHeight) {
                // 情况1：最后一个item高度大于可见区域高度
                // 滚动到该item的最底部，确保item的底边可见
                val offset = lastItemHeight - visibleHeight
                layoutManager.scrollToPositionWithOffset(lastPosition, -offset)
                DebugUtil.d(TAG, "scrollToBottomImmediatelyInternal: large item, offset=$offset")
            } else {
                // 情况2：最后一个item高度小于或等于可见区域高度
                // 将RecyclerView滚动到最底部，使最后一个item的底边与RecyclerView的底边对齐
                layoutManager.scrollToPositionWithOffset(lastPosition, Int.MIN_VALUE)
                DebugUtil.d(TAG, "scrollToBottomImmediatelyInternal: normal item, scroll to bottom")
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "scrollToBottomImmediatelyInternal error: ${e.message}")
            // 最终fallback方案
            try {
                layoutManager.scrollToPositionWithOffset(lastPosition, Int.MIN_VALUE)
            } catch (fallbackException: Exception) {
                DebugUtil.e(TAG, "scrollToBottomImmediatelyInternal final fallback error: ${fallbackException.message}")
            }
        }
    }
}