/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - RecordSubtitleAdapter.kt
 * Description:
 *     The helper class for ui showing subtitle in record pat.
 *
 * Version: 1.0
 * Date: 2025-05-30
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-30   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.widget.COUIAnimateTextView
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class RecordSubtitleAdapter(var context: Context) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var dataList = mutableListOf<DisplaySubtitleEntry>()
    private var listener: OnItemClickListener? = null
    companion object {
        const val TYPE_DATA_OK = 1
        const val TYPE_DATA_NOW = 2
        const val TYPE_NO_NET = 3
        const val TYPE_NO_SERVICE = 4
        const val TYPE_NO_DATA = -1
        private const val TIME_ZERO = 0
        private const val TIME_HOUR_24 = 24
        private const val TIME_HOUR = 3600000
        const val TAG = "RecordSubtitleAdapter"
        const val TYPE_VAD_FINAL = "VAD_FINAL"
        const val TYPE_INTERMEDIATE = "INTERMEDIATE"
        const val TYPE_NOT_NET = "TYPE_NO_NET"
        const val TYPE_NOT_SERVICE = "TYPE_NO_SERVICE"
        private val TIME_FORMATTER_HH_MM_SS = DateTimeFormatter.ofPattern("HH:mm:ss")
        private val TIME_FORMATTER_00_MM_SS = DateTimeFormatter.ofPattern("00:mm:ss")
        private const val INVALID_TIME_FORMAT_LONG = "--:--:--"
        private const val INVALID_TIME_FORMAT_SHORT = "--:--"

        @JvmStatic
        private fun formatTime(time: Long): String {
            return try {
                val duration = java.time.Duration.ofMillis(time)
                if (duration.toHours() > TIME_ZERO) {
                    TIME_FORMATTER_HH_MM_SS.format(Instant.ofEpochMilli(time).atZone(ZoneId.of("UTC"))
                            .withHour(duration.toHours().toInt() % TIME_HOUR_24))
                } else {
                    TIME_FORMATTER_00_MM_SS.format(Instant.ofEpochMilli(time).atZone(ZoneId.of("UTC")))
                }
            } catch (e: IllegalArgumentException) {
                DebugUtil.e(TAG, "Invalid timestamp format: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            } catch (e: ArithmeticException) {
                DebugUtil.e(TAG, "Timestamp calculation error: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            } catch (e: UnsupportedOperationException) {
                DebugUtil.e(TAG, "Unsupported date operation: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            }
        }
    }

    class ViewHolderData(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val speaker: TextView = itemView.findViewById(R.id.tv_speaker)
        val speakerLl: LinearLayout = itemView.findViewById(R.id.ll_speaker)
        val startTime: TextView = itemView.findViewById(R.id.start_time)
        val subtitle: TextView  = itemView.findViewById(R.id.item_content)
        fun updateData(data: DisplaySubtitleEntry, position: Int, listener: OnItemClickListener?) {
            speaker.text = data.originContent.roleName
            DebugUtil.e(TAG, "Unsupported speaker: ${speaker.text}")
            if (data.originContent.roleId == -1
                || data.originContent.roleName == null
            ) {
                speakerLl.visibility = View.GONE
            }
            speaker.setOnClickListener {
                listener?.onItemClickSpeaker(position)
                }
            startTime.text = formatTime(data.originContent.startTime)
            subtitle.text = if (data.displayContent.isNotEmpty()) {
                data.displayContent
                } else {
                data.originContent.textContent
                }
            }
        }
    class ViewHolderDataNow(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val speaker: TextView = itemView.findViewById(R.id.tv_speaker)
        val startTime: TextView = itemView.findViewById(R.id.start_time)
        val subtitle: COUIAnimateTextView = itemView.findViewById(R.id.item_content)
        fun updateData(data: DisplaySubtitleEntry, position: Int, listener: OnItemClickListener?) {
            speaker.text = data.originContent.roleName
            speaker.setOnClickListener {
                listener?.onItemClickSpeaker(position)
            }
            startTime.text = formatTime(data.originContent.startTime)
            val content = if (data.displayContent.isNotEmpty()) {
                data.displayContent
                } else {
                data.originContent.textContent
                }
            subtitle.setSubtitleAnimateText(content)
        }
    }

    class ViewHolderError(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val netError: TextView = itemView.findViewById(R.id.tv_error)
        val retryTv: TextView = itemView.findViewById(R.id.tv_retry)
        fun updateData(context: Context, data: DisplaySubtitleEntry) {
            if ((data.originContent.textType) == TYPE_NOT_NET) {
                netError.text = context.resources.getString(com.soundrecorder.base.R.string.subtitle_network_connect_error)
                retryTv.text = context.resources.getString(com.soundrecorder.base.R.string.retry)
                COUITextViewCompatUtil.setPressRippleDrawable(retryTv)
                retryTv.setOnClickListener {
                    DebugUtil.i(TAG, "ViewHolderError-RecorderViewModelApi.externalInitAsr")
                    RecorderViewModelApi.externalInitAsr()
                }
            } else if (data.originContent.textType == TYPE_NOT_SERVICE) {
                netError.text = context.resources.getString(com.soundrecorder.base.R.string.subtitle_service_connect_error)
                retryTv.text = context.resources.getString(com.soundrecorder.base.R.string.retry)
                COUITextViewCompatUtil.setPressRippleDrawable(retryTv)
                retryTv.setOnClickListener {
                    RecorderViewModelApi.externalInitAsr()
                }
            }
        }
    }

    fun getDataList(): List<DisplaySubtitleEntry> {
        return dataList
    }

    /**
     * 获取类型
     */
    override fun getItemViewType(position: Int): Int {
        val item = dataList.getOrNull(position) ?: return TYPE_DATA_OK
        return when (item.originContent.textType) {
            TYPE_VAD_FINAL -> TYPE_DATA_OK
            TYPE_INTERMEDIATE -> TYPE_DATA_NOW
            TYPE_NOT_NET -> TYPE_NO_NET
            TYPE_NOT_SERVICE -> TYPE_NO_SERVICE
            else -> TYPE_NO_DATA
        }
    }

    /**
     * 创建 ViewHolder
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_DATA_OK -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data, parent, false)
                ViewHolderData(item)
            }
            TYPE_DATA_NOW -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data_now, parent, false)
                ViewHolderDataNow(item)
            }
            TYPE_NO_NET -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_error, parent, false)
                ViewHolderError(item)
            }
            TYPE_NO_SERVICE -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_error, parent, false)
                ViewHolderError(item)
            }
            else -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data, parent, false)
                ViewHolderData(item)
            }
        }
    }

    /**
     * 绑定数据到 ViewHolder
     */
    @SuppressLint("ClickableViewAccessibility")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val data = dataList[position]
        DebugUtil.i(TAG, "onBindViewHolder")
        if (holder is ViewHolderData) {
            holder.updateData(data, position, listener)
        } else if (holder is ViewHolderDataNow) {
            holder.updateData(data, position, listener)
        } else if (holder is ViewHolderError) {
            holder.updateData(context, data)
        }
    }
    /**
     * 获取item总数
     */
    override fun getItemCount(): Int {
        return dataList.size
    }
    /**
     * 刷新数据
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<DisplaySubtitleEntry>) {
        DebugUtil.i(TAG, "setData =: " + data.size)
        dataList = data
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setNoNetWork(isNONetWork: Boolean) {
        val lastItem = dataList.lastOrNull()?.apply {
            originContent.textType = if (isNONetWork) "TYPE_NO_NET" else "TYPE_NO_SERVICE"
        } ?: run {
            DebugUtil.w(TAG, "Attempt to modify textType on empty dataList")
            null
        }
        notifyDataSetChanged()
    }

    fun setListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    interface OnItemClickListener {
        fun onItemLongClick(position: Int)
        fun onLongClickStop(position: Int)

        fun onItemClickSpeaker(position: Int)
    }
}