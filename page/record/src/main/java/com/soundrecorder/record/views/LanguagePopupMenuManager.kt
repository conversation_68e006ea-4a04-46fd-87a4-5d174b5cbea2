/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: LanguagePopupMenuManager
 * Description: LanguagePopupMenuManager
 * Version: 1.0
 * Date: 2025/6/6
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/6/6      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.record.views

import android.content.Context
import android.widget.AdapterView
import android.widget.TextView
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.recorderservice.api.RecorderViewModelApi

/**
 * 语言选择弹窗管理器
 * 负责管理录音界面的语言选择弹窗功能
 */
class LanguagePopupMenuManager(private var mContext: Context?) {
    companion object {
        private const val TAG = "LanguagePopupMenuManager"
    }

    private var mNormalPopupWindow: COUIPopupListWindow? = null
    private var mNormalItemList: MutableList<PopupListItem>? = null

    /**
     * 初始化弹窗菜单
     */
    private fun initPopMenu(mSelectLanguageTv: TextView) {
        mContext?.let { context ->
            mNormalPopupWindow = COUIPopupListWindow(context).apply {
                setDismissTouchOutside(true)
                setUseBackgroundBlur(true)
            }

            RecorderViewModelApi.getSupportLanguageList { asrSupportLanguageList ->
                if (asrSupportLanguageList.isNullOrEmpty()) {
                    return@getSupportLanguageList
                }

                val asrLanguages = LanguageUtil.getAsrLangMap(context, asrSupportLanguageList)
                initNormalItemList(asrLanguages)
                mNormalPopupWindow?.apply {
                    itemList = mNormalItemList
                    setOnItemClickListener(createItemClickListener(asrLanguages, mSelectLanguageTv))
                }
            }
        }
    }

    /**
     * 创建弹窗项目点击监听器
     * @param asrLanguages 语言映射
     * @return 点击监听器
     */
    private fun createItemClickListener(asrLanguages: Map<String, String>, mSelectLanguageTv: TextView): AdapterView.OnItemClickListener {
        return AdapterView.OnItemClickListener { _, _, position, _ ->
            if (mNormalPopupWindow == null || position == 0) {
                return@OnItemClickListener
            }

            val popupListItem = mNormalItemList?.get(position) ?: return@OnItemClickListener
            val title = popupListItem.title
            var code = LanguageUtil.getAsrDefaultLanguage()

            // 查找对应的语言代码
            for ((key, value) in asrLanguages) {
                if (value == title) {
                    code = key
                    break
                }
            }
            val curSelectedLanguage = RecorderViewModelApi.getCurSelectedLanguage()
            if (mNormalPopupWindow?.isShowing == true && curSelectedLanguage != code) {
                handleLanguageSelection(code, title, position, mSelectLanguageTv)
            } else {
                DebugUtil.d(TAG, "createItemClickListener ignore click, " +
                        "curSelectedLanguage=$curSelectedLanguage, code=$code, isShowing=${mNormalPopupWindow?.isShowing}")
                mNormalPopupWindow?.dismiss()
            }
        }
    }

    /**
     * 处理语言选择逻辑
     * @param languageCode 语言代码
     * @param languageName 语言名称
     * @param position 选择位置
     */
    private fun handleLanguageSelection(languageCode: String, languageName: String, position: Int, mSelectLanguageTv: TextView) {
        mNormalPopupWindow?.itemList?.forEach { item ->
            item.isChecked = false
        }
        mNormalPopupWindow?.itemList?.get(position)?.isChecked = true
        // 切换语种
        DebugUtil.d(TAG, "handleLanguageSelection changeAsrLanguage languageCode=$languageCode, languageName=$languageName")
        RecorderViewModelApi.changeAsrLanguage(languageCode)
        mSelectLanguageTv.text = languageName
        mNormalPopupWindow?.dismiss()
    }

    /**
     * 显示弹窗菜单
     */
    fun showPopupMenu(mSelectLanguageTv: TextView) {
        initPopMenu(mSelectLanguageTv)
        if (mNormalPopupWindow != null) {
            mNormalPopupWindow?.dismiss()
            mNormalPopupWindow?.show(mSelectLanguageTv)
        }
    }

    /**
     * 初始化普通项目列表
     * @param langMap 语言映射
     */
    private fun initNormalItemList(langMap: Map<String, String>) {
        mNormalItemList = mutableListOf()
        val builder = PopupListItem.Builder()

        // 添加标题项
        mContext?.getString(com.soundrecorder.common.R.string.speech_language)?.let { title ->
            builder.setTitle(title)
            builder.setItemType(PopupListItem.MENU_ITEM_TYPE_HEADER)
            mNormalItemList?.add(builder.build())
            builder.reset()
        }

        // 添加语言选项
        if (langMap.isNotEmpty()) {
            val currentSelectedLanguage = RecorderViewModelApi.getCurSelectedLanguage()
            /*排序*/
            val sortLanguageList = mContext?.let {
                LanguageUtil.sortLanguageList(it, langMap.keys.toList())
            } ?: langMap.keys
            sortLanguageList.forEach { languageCode ->
                builder.setTitle(langMap[languageCode])
                builder.setIsChecked(languageCode == currentSelectedLanguage)
                mNormalItemList?.add(builder.build())
                builder.reset()
            }
        }
    }

    /**
     * 销毁弹窗
     */
    fun dismiss() {
        mNormalPopupWindow?.dismiss()
    }

    /**
     * 释放资源
     */
    fun release() {
        dismiss()
        mNormalPopupWindow = null
        mNormalItemList = null
        mContext = null
    }
}