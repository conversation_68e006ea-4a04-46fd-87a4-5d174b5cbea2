/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.app.Application
import android.os.Bundle
import android.os.Environment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.share.ShareAction
import com.soundrecorder.common.share.IShareListener
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.share.ShareTypeText
import com.soundrecorder.common.share.ShareType
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.convertservice.util.ConvertToUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class ShareWithTxtViewModel(application: Application) : AndroidViewModel(application),
    SaveToLocalCallback, IShareListener {
    companion object {
        const val TAG = "ShareWithTxtViewModel"
        private const val TXT_SUFFIX = ".txt"
    }

    //转文本列表数据
    val mItemsListLiveData: MutableLiveData<MutableList<ConvertContentItem>> = MutableLiveData()

    //列表中讲话人显示状态
    val mShowSpeakerLiveData: MutableLiveData<Boolean> = MutableLiveData(true)

    //列表中时间显示状态
    val mShowDateLiveData: MutableLiveData<Boolean> = MutableLiveData(true)

    //列表中段落显示状态
    val mShowLineLiveData: MutableLiveData<Boolean> = MutableLiveData(true)

    //header标题名称
    val mTitleLiveData: MutableLiveData<String> = MutableLiveData("")

    //header时间
    val mDateLiveData: MutableLiveData<String> = MutableLiveData("")

    //header主题
    val mSubjectLiveData: MutableLiveData<String> = MutableLiveData("")

    //item中参会人
    val mRolesStringLiveData: MutableLiveData<String> = MutableLiveData("")

    //不显示段落的时候展示的文本内容
    val mContentStringLiveData: MutableLiveData<String> = MutableLiveData("")

    //显示dialog之前的数值，点击dialog的取消按钮后，恢复原来的值
    //段落
    private var mLastShowLine: Boolean = true

    //时间
    private var mLastShowDate: Boolean = true

    //讲话人
    private var mLastShowSpeaker: Boolean = true

    //是否需要重建waitingDialog
    private var mNeedRestoreWaitingDialog: Boolean = false

    //activity/fragment重建完成后是否需要显示snackBar
    private var mNeedShowSnackBar: Boolean = false

    //保存到本地文件的名称
    private var mSaveCallBackFileName: String = ""

    //保存到本地文件的完整路径
    private var mSaveCallBackFilePath: String = ""

    //保存到本地成功后的路径
    private var saveTxtToLocalCallback: SaveToLocalCallback? = null

    //保存到本地文件名称中的日期(2021-11-03 16-20-35)
    private var mSaveTxtTitleDate: String = ""

    //分享回调，当数据准备完成之后，开始分享
    private var shareListener: IShareListener? = null

    //录音文件创建时间
    private var mCreateTime: Long = -1

    private var mMediaRecordId: Long = -1

    //转文本文件的大小
    private var mConvertFileSize: Long = 0

    //录音文件的名称
    private var mOriginalFileName: String? = null

    //是否支持讲话人
    private var mCanShowSpeakerRole: Boolean = false

    private var mRepository: ShareWithTxtRepository? = null

    //保存txt到本地的文件夹路径
    val saveTxtFolder = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).absolutePath + File.separator +
            "SoundRecordDoc"

    //保存txt到本地的文件夹路径 加上 “/”
    val saveTxtFolderSeparator = saveTxtFolder + File.separator

    fun setRepository(repository: ShareWithTxtRepository?) {
        this.mRepository = repository
    }

    fun loadData() {
        viewModelScope.launch(Dispatchers.IO) {
            mRepository?.let { repository ->
                try {
                    val convertRecord = ConvertDbUtil.selectByRecordId(mMediaRecordId)
                    DebugUtil.i(TAG, "convertRecord >> $convertRecord")
                    val context = BaseApplication.getAppContext()
                    val convertFileName = repository.getConvertFileName(convertRecord, context)
                    val readConvertContent = if (convertRecord?.isOShareFile == true) {
                        ConvertToUtils.readOShareConvertContent(convertRecord.convertTextfilePath, convertRecord.serverPlanCode)
                    } else {
                        ConvertToUtils.readConvertContent(context, convertFileName, convertRecord?.serverPlanCode)
                    }
                    val resources = BaseApplication.getAppContext().resources
                    //标题名称，使用播放界面传递过来的数据，不使用转文本结果的文件名数据
                    mTitleLiveData.postValue(resources.getString(com.soundrecorder.common.R.string.export_save_file_name, mOriginalFileName))

                    //转文本内容大小,超过50M的时候，保存和分享需要弹窗,
                    //为了避免在预览界面删除录音无法获取转文本大小，只有当convertFileSize>0的时候才保存
                    val convertFileSize = NewConvertResultUtil.getConvertFileSize(convertRecord?.convertTextfilePath)
                    if (convertFileSize > 0) {
                        mConvertFileSize = convertFileSize
                    }
                    val date = repository.getRecordFileCreateDate(mCreateTime)
                    val string = repository.getTimeString(date)
                    mSaveTxtTitleDate = repository.getSaveTxtToLocalDate(date)
                    val timeString = resources.getString(com.soundrecorder.common.R.string.time) + "  $string"
                    mDateLiveData.postValue(timeString)
                    val subject = resources.getString(com.soundrecorder.common.R.string.export_content_theme) + "  $mOriginalFileName"
                    mSubjectLiveData.postValue(subject)
                    val speakerStringBuilder = StringBuilder(resources.getString(com.soundrecorder.common.R.string.export_content_person, "  "))
                    speakerStringBuilder.append(repository.getRolesString(readConvertContent))
                    mRolesStringLiveData.postValue(speakerStringBuilder.toString())
                    mContentStringLiveData.postValue(repository.getItemsContentString(readConvertContent))
                    mItemsListLiveData.postValue(readConvertContent)
                } catch (e: Exception) {
                    DebugUtil.e(TAG, "readConvertContent error.$e", e)
                    mItemsListLiveData.postValue(mutableListOf())
                }
            }
        }
    }

    fun initArgFromBundle(arguments: Bundle?) {
        try {
            arguments?.let {
                mMediaRecordId = it.getLong("mediaRecordId")
                mCanShowSpeakerRole = it.getBoolean("canShowSpeaker")
                mCreateTime = it.getLong("createTime", -1)

                val isShowSpeaker = it.getBoolean("isShowSpeaker")
                mShowSpeakerLiveData.value = isShowSpeaker
                val playFileName = it.getString("playFileName", "")
                //只有当mOriginalFileName为空的时候才更新数据
                //在文件管理重命名之后，监听名称变化，然后修改mOriginalFileName，这个时候再切暗色等重建界面，不需要重新赋arguments的值，因为是改名之前的
                if (mOriginalFileName.isNullOrBlank()) {
                    mOriginalFileName = playFileName.subSequence(0, playFileName.lastIndexOf(".")).toString()
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "initArgFromBundle error >> ${e.message}")
        }
        DebugUtil.i(TAG, "playFileName >> $mOriginalFileName")
    }

    /**
     * If click save button on dialog, save the result.
     */
    fun saveResultChoice() {
        mLastShowLine = mShowLineLiveData.value ?: true
        mLastShowDate = mShowDateLiveData.value ?: true
        mLastShowSpeaker = mShowSpeakerLiveData.value ?: true
        DebugUtil.i(TAG, "saveResultChoice $mLastShowLine ,$mLastShowDate,$mLastShowSpeaker")
    }

    /**
     * If click cancel button on dialog, restore the result.
     */
    fun restoreChoice() {
        DebugUtil.i(TAG, "restoreChoice >>$mLastShowLine ,$mLastShowDate,$mLastShowSpeaker")
        if ((mShowLineLiveData.value == true) != mLastShowLine) {
            mShowLineLiveData.postValue(mLastShowLine)
        }
        if ((mShowDateLiveData.value == true) != mLastShowDate) {
            mShowDateLiveData.postValue(mLastShowDate)
        }
        if ((mShowSpeakerLiveData.value == true) != mLastShowSpeaker) {
            mShowSpeakerLiveData.postValue(mLastShowSpeaker)
        }
    }

    /**
     * 点击底部分享按钮，弹窗系统分享弹窗
     */
    fun shareTxt(activity: FragmentActivity) {
        mTitleLiveData.value?.let {
            val txtString = getTxtFileContent()
            val filePath = it + TXT_SUFFIX
            ShareAction.share(
                activity,
                ShareTextContent(mMediaRecordId, false, filePath, mConvertFileSize, emptyList()),
                ShareTypeText(txtString),
                viewModelScope,
                this
            )
        }
    }

    /**
     * 点击底部保存按钮，保存转文本内容到本地
     * 1.先获取
     */
    fun saveTxtToLocal() {
        if (mMediaRecordId < 0 || mOriginalFileName.isNullOrBlank()) {
            DebugUtil.e(TAG, "File not exist......")
            saveTxtToLocalCallback?.onSaveFailed("mMediaRecordId < 0 or file not exist......")
            return
        }
        SaveTxtUtil.instance.saveTxtToLocal(
            this,
            saveTxtFolderSeparator,
            mOriginalFileName!!,
            mSaveTxtTitleDate,
            mConvertFileSize,
            this,
            viewModelScope
        )
    }

    /**
     * 获取保存到本地文件的文本内容
     * 将标题、时间、主题、参会人、讲话内容等信息按格式组合
     */
    private fun getTxtFileContent(): String {
        val lineSeparator = System.lineSeparator()
        val txtString = StringBuilder()
        txtString.append("${mTitleLiveData.value}$lineSeparator$lineSeparator")
        txtString.append("${mDateLiveData.value}$lineSeparator")
        txtString.append("${mSubjectLiveData.value}$lineSeparator")

        val showLine = mShowLineLiveData.value == true
        //显示分段的情况
        if (showLine) {
            val showDate = mShowDateLiveData.value == true
            val showSpeaker = mCanShowSpeakerRole && mShowSpeakerLiveData.value == true
            if (showSpeaker) {
                //添加参会人信息
                txtString.append("${mRolesStringLiveData.value}$lineSeparator")
            }
            txtString.append(lineSeparator)
            //添加讲话人、时间和讲话内容
            mItemsListLiveData.value?.forEach {
                if (showSpeaker) {
                    txtString.append(it.roleName)
                    if (showDate) {
                        txtString.append("  ")
                    } else {
                        txtString.append(lineSeparator)
                    }
                }
                if (showDate) {
                    txtString.append("${it.startTime.durationInMsFormatTimeExclusive()}$lineSeparator")
                }
                txtString.append("${it.textContent}$lineSeparator$lineSeparator")
            }
        } else {
            //不显示分段
            txtString.append("$lineSeparator${mContentStringLiveData.value}")
        }
        return txtString.toString()
    }

    fun getLastShowLine() = mLastShowLine

    fun getLastShowDate() = mLastShowDate

    fun getLastShowSpeaker() = mLastShowSpeaker

    fun getCanShowSpeakerRole() = mCanShowSpeakerRole

    fun setCanShowSpeakerRole(canShow: Boolean) {
        mCanShowSpeakerRole = canShow
    }

    fun setMediaRecorderId(mediaRecordId: Long) {
        mMediaRecordId = mediaRecordId
    }

    fun setCreateTime(createTime: Long) {
        mCreateTime = createTime
    }

    fun setOnSaveTxtToLocalResultCallback(callback: SaveToLocalCallback?) {
        saveTxtToLocalCallback = callback
    }

    fun setOnShareTxtCallbackResultCallback(listener: IShareListener?) {
        shareListener = listener
    }

    /**
     * 转文本文件的大小，用于判断是否需要显示waitingDialog
     */
    fun getConvertFileSize() = mConvertFileSize

    fun getNeedRestoreDialog() = mNeedRestoreWaitingDialog

    /**
     * 设置是否需要重建waitingDialog
     */
    fun setNeedRestoreDialog(needShow: Boolean) {
        mNeedRestoreWaitingDialog = needShow
    }

    /**
     * 已经显示了snackbar
     */
    fun setAlreadyShowSnackBar() {
        mNeedShowSnackBar = false
    }

    /**
     * 是否需要显示snackbar，主要用于activity重建后继续执行保存流程，显示snackbar
     */
    fun getNeedShowSnackBar() = mNeedShowSnackBar

    fun getSaveCallBackFileName() = mSaveCallBackFileName

    /**
     * 保存TXT文件路径
     */
    fun getSaveCallBackFilePath() = mSaveCallBackFilePath

    /**
     * 保存TXT到本地需要显示waitingDialog
     */
    override fun onShowSaveFileWaitingDialog() {
        DebugUtil.i(TAG, "onShowShareWaitingDialog...")
        saveTxtToLocalCallback?.onShowSaveFileWaitingDialog()
    }

    /**
     * 保存txt到本地，获取到了保存文件的名称和路径
     */
    override fun onGetFileName(fileName: String, fileAbsPath: String) {
        mSaveCallBackFileName = fileName
        mSaveCallBackFilePath = fileAbsPath
        saveTxtToLocalCallback?.onGetFileName(fileName, fileAbsPath)
    }

    /**
     * 保存成功后，此刻可能activity正在重建，需要先保存状态值和数据，等到重建完成之后，再判断是否需要显示snackBar
     */
    override fun onSaveSuccess(fileName: String, fileAbsPath: String) {
        mNeedShowSnackBar = true
        //保存成功则不需要重建waitingDialog，直接展示snackBar
        mNeedRestoreWaitingDialog = false
        saveTxtToLocalCallback?.onSaveSuccess(fileName, fileAbsPath)
    }

    /**
     * 保存txt到本地失败
     */
    override fun onSaveFailed(message: String) {
        mNeedShowSnackBar = false
        mNeedRestoreWaitingDialog = false
        saveTxtToLocalCallback?.onSaveFailed(message)
    }

    /**
     * 分享前置流程完成
     */
    override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
        shareListener?.onShowShareWaitingDialog(mediaId, type)
    }

    /**
     * 分享前置流程失败
     */
    override fun onShareSuccess(mediaId: Long, type: ShareType) {
        mNeedRestoreWaitingDialog = false
        shareListener?.onShareSuccess(mediaId, type)
    }

    /**
     * 需要显示waitingDialog
     */
    override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
        shareListener?.onShareFailed(mediaId, type, error, message)
    }

    /**
     * 添加埋点
     */
    fun addBuryPoint() {
        if (mShowLineLiveData.value == false) {
            ConvertStaticsUtil.addClickSegmentedSwitchEventOnShare(RecorderUserAction.VALUE_SHARE_TXT_SETTING_SWITCH)
        } else {
            ConvertStaticsUtil.addClickSegmentedSwitchEventOnShare(RecorderUserAction.VALUE_SHARE_TXT_SETTING_SWITCH_REPORT)
        }
        if (mShowDateLiveData.value == false) {
            ConvertStaticsUtil.addClickDateSwitchEventOnShare(RecorderUserAction.VALUE_SHARE_TXT_SETTING_SWITCH)
        } else {
            ConvertStaticsUtil.addClickDateSwitchEventOnShare(RecorderUserAction.VALUE_SHARE_TXT_SETTING_SWITCH_REPORT)
        }
        if (mShowSpeakerLiveData.value == false) {
            ConvertStaticsUtil.addClickSpeakerSwitchEventOnShare(RecorderUserAction.VALUE_SHARE_TXT_SETTING_SWITCH)
        } else {
            ConvertStaticsUtil.addClickSpeakerSwitchEventOnShare(RecorderUserAction.VALUE_SHARE_TXT_SETTING_SWITCH_REPORT)
        }
    }
}