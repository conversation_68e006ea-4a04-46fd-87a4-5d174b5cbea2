/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert

import android.animation.Animator
import android.animation.ValueAnimator
import android.graphics.Rect
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.appcompat.uiutil.ShadowUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.RtlUtils
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.ConvertDbUtil.SERVER_PLAN_ASR
import com.soundrecorder.common.utils.ConvertDbUtil.SERVER_PLAN_BEIYAN
import com.soundrecorder.common.utils.ConvertDbUtil.SERVER_PLAN_XUNFEI
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.common.utils.DensityHelper
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.LandScapeUtil
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.TipUtil.Companion.TYPE_ROLE_NAME
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.convertservice.util.RoleNameUtil
import com.soundrecorder.modulerouter.BrowseFileAction
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.R
import com.soundrecorder.playback.convert.IConvertViewController
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipClickListener
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import com.soundrecorder.playback.newconvert.search.ConvertSearchHelper
import com.soundrecorder.playback.newconvert.ui.ContentScrollHelper
import com.soundrecorder.playback.newconvert.ui.ConvertRenameBottomSheetDialog
import com.soundrecorder.playback.newconvert.ui.ISelectSpeakerListener
import com.soundrecorder.playback.newconvert.ui.TextImageItemAdapter
import com.soundrecorder.playback.newconvert.util.ListUtil
import com.soundrecorder.playback.newconvert.view.CustomLinearLayoutManager
import java.util.Objects
import java.util.concurrent.CopyOnWriteArrayList
import java.util.function.Supplier

@Suppress("LargeClass")
class ConvertViewController(
    private var mActivity: AppCompatActivity?,
    private val container: ViewGroup?,
    private val lifecycleOwner: LifecycleOwner?
) :
    TextImageItemAdapter.OnSpeakerNameClick,
    KeyWordChipClickListener, IConvertViewController, ISelectSpeakerListener {
    var isNeedShowAnimView: Boolean = true
    var onConvertCancelClickListener: OnConvertCancelClickListener? = null
    var onConvertStartClickListener: OnConvertStartClickListener? = null
    var mRenameSpeakerDialog: ConvertRenameBottomSheetDialog? = null
    var mContinuetranDialog: AlertDialog? = null

    private var mToolbar: COUIToolbar? = null
    private var mMovePointView: View? = null
    private var mViewModel: PlaybackActivityViewModel? = null
    private var mConvertViewModel: PlaybackConvertViewModel? = null

    private var mInflater: LayoutInflater? = null

    private var mConvertInitView: View? = null

    //录音转文本
    private var mConvertInitTitle: View? = null

    //支持将录音转为文本
    private var mConvertInitDesc: View? = null
    private var mEmptyConvertImage: OSImageView? = null
    private var mEmptyConvertText: View? = null
    private var mSvConvertEmpty: ScrollView? = null
    private var mConvertContentView: View? = null
    private var mConvertContent: COUIRecyclerView? = null
    private var mConvertContentLayout: ConstraintLayout? = null

    private var mConvertBackButton: ImageView? = null
    private var backBtnLayout: RelativeLayout? = null

    private var mConvertingView: View? = null
    private var mInitConvertImage: OSImageView? = null
    private var mSvConvertInit: ScrollView? = null
    private var mConvertInitTextLayout: View? = null
    private var mConvertCancel: TextView? = null

    //转写中进度
    private var mConvertProgress: TextView? = null

    //转写 时间 + 描述
    private var mConvertingDesc: TextView? = null
    private var mLoadingView: OSImageView? = null
    private var mSvConverting: ScrollView? = null
    private var mConvertingTextLayout: View? = null

    private var mConvertEmptyView: View? = null
    private var mConvertContentAdapter: TextImageItemAdapter? = null

    @VisibleForTesting
    var mLastConvertInitScale = -1F

    @VisibleForTesting
    var mLastConvertConvertingScale = -1F

    private var mLinearLayoutManager: CustomLinearLayoutManager? = null
    private var mContentScrollHelper: ContentScrollHelper? = null

    private val duration = 180L
    private val pathInterpolator = PathInterpolatorHelper.couiEaseInterpolator

    private val MIN_HEIGHT = 140

    private var searchHelper: ConvertSearchHelper? = null
    private var mValueAnimator: ValueAnimator? = null
    private var mClickListener = object : ConvertRenameBottomSheetDialog.OnClickListener {

        override fun onPositiveClick(
            recordId: Long,
            checked: Boolean,
            roleName: String,
            isClickHistory: Boolean
        ) {
            DebugUtil.d(TAG, "showRenameDialog $recordId $checked")
            renameSpeaker(recordId, checked, roleName, isClickHistory)
            releaseRenameSpeakerDialog()
        }

        override fun onNegtiveClick() {
            releaseRenameSpeakerDialog()
        }

        override fun onClearClick(recordId: Long) {
            deleteSpeaker(recordId, true, "")
        }

        override fun onHistoryItemDeleteClick(recordId: Long, checked: Boolean, roleName: String) {
            deleteSpeaker(recordId, false, roleName)
        }
    }

    // 获取高频词的接口
    var keyWordSupplier: Supplier<Boolean>? = null

    //转文本底部渐隐遮罩
    var viewSpace: View? = null

    //沉浸态动画
    private var viewSpaceImmersiveAnimation: COUISpringAnimation? = null

    //调用沉浸态动画时布局还未初始化，在初始化完成后继续动画
    private var isImmersiveAnimationWait: Boolean = false

    //调用退出沉浸态动画时布局还未初始化，在初始化完成后继续动画
    private var isRecoverAnimationWait: Boolean = false

    private var smartNaming: Boolean? = null
    private var smartResultName: String? = null

    init {
        mInflater = mActivity?.layoutInflater
    }

    override fun release() {
        dismissDialog()

        DebugUtil.i(TAG, "release")
        mConvertContentView?.removeOnLayoutChangeListener(mLayoutChangeListener)
        mConvertEmptyView?.removeOnLayoutChangeListener(mEmptyLayoutChangeListener)
        mConvertInitView?.removeOnLayoutChangeListener(mInitLayoutChangeListener)
        mConvertingView?.removeOnLayoutChangeListener(mConvertingLayoutChangeListener)
        mContentScrollHelper?.removeOnScrollListener()
        mContentScrollHelper?.release()
        mContentScrollHelper = null
        /*空页面动效图*/
        mEmptyConvertImage?.release()
        /*初始动效图*/
        mInitConvertImage?.release()
        /*转写中动效图*/
        mLoadingView?.release()
        mValueAnimator?.cancel()
        mValueAnimator = null
        onConvertCancelClickListener = null
        mToolbar = null
        mMovePointView = null
        mInflater = null
        mActivity = null
        mConvertContentView = null
        mConvertContent = null
        mConvertCancel = null
        mConvertProgress = null
        mConvertingDesc = null
        mConvertEmptyView = null
        mConvertContentAdapter?.release()
        mConvertContentAdapter = null
        keyWordSupplier = null
        onConvertStartClickListener = null
        mConvertViewModel?.viewPagerScrollEnable?.value = null
    }

    private fun dismissDialog() {
        mRenameSpeakerDialog?.dismiss()
        mRenameSpeakerDialog = null
        mContinuetranDialog?.dismiss()
        mContinuetranDialog = null
    }

    private var mConvertStart: View? = null

    private fun initConvertInitView() {
        if (mConvertInitView != null) {
            return
        }
        mConvertInitView = mInflater?.inflate(R.layout.fragment_convert_init, null)
        mConvertInitTitle =  mConvertInitView?.findViewById(R.id.tv_convert_init_title)
        mConvertInitDesc =  mConvertInitView?.findViewById(R.id.tv_convert_init_desc)
        mInitConvertImage =  mConvertInitView?.findViewById(R.id.img_init_convert)
        mSvConvertInit =  mConvertInitView?.findViewById(R.id.sv_convert_init)
        mConvertInitTextLayout =  mConvertInitView?.findViewById(R.id.ll_convert_init)
        mConvertStart = mConvertInitView?.findViewById(R.id.tv_start_convert)
        mConvertStart?.setOnClickListener {
            DebugUtil.d(TAG, "click start convert $onConvertStartClickListener")
            onConvertStartClickListener?.onClick()
            val duration = mViewModel?.playerController?.getDuration()
            ConvertStaticsUtil.addConvertFileDuration(duration)
        }
    }

    private fun releaseConvertInitView() {
        mConvertInitView = null
        mConvertInitTitle = null
        mConvertInitDesc = null
        mConvertStart = null
    }

    private fun initConvertContentView() {
        if (mConvertContentView != null) {
            return
        }
        mConvertContentView = mInflater?.inflate(R.layout.fragment_convert_content, null)
        mConvertContent = mConvertContentView?.findViewById(R.id.convert_content)
        //手动设置constraintSet，按照不同屏幕宽度设置widthPercent达到相关显示宽度动态适配屏幕宽度的效果
        mConvertContentLayout = mConvertContentView?.findViewById(R.id.converrt_content_layout)
        val constrainSet = ConstraintSet()
        constrainSet.connect(R.id.convert_content, ConstraintSet.TOP, R.id.converrt_content_layout, ConstraintSet.TOP)
        constrainSet.connect(R.id.convert_content, ConstraintSet.BOTTOM, R.id.converrt_content_layout, ConstraintSet.BOTTOM)
        constrainSet.connect(R.id.convert_content, ConstraintSet.START, R.id.converrt_content_layout, ConstraintSet.START)
        constrainSet.connect(R.id.convert_content, ConstraintSet.END, R.id.converrt_content_layout, ConstraintSet.END)
        val ratioTypedValue = TypedValue()
        mActivity?.resources?.getValue(R.dimen.screen_width_percent_parentchild, ratioTypedValue, true)
        val ratio = ratioTypedValue.float
        constrainSet.constrainPercentWidth(R.id.convert_content, ratio)
        constrainSet.applyTo(mConvertContentLayout)
        mConvertBackButton = mConvertContentView?.findViewById(R.id.back_btn)
        backBtnLayout = mConvertContentView?.findViewById(R.id.back_btn_layout)
        viewSpace = mConvertContentView?.findViewById(R.id.view_space)
        mLinearLayoutManager = CustomLinearLayoutManager(BaseApplication.getAppContext())
        //这里设置recycleChildrenOnDetach的值为true是为了，让界面退出时，recyclerView将所有的viewholder都recycle掉，优化部分内存占用
        mLinearLayoutManager?.recycleChildrenOnDetach = true
        mConvertContent?.layoutManager = mLinearLayoutManager
        mConvertContentAdapter = TextImageItemAdapter(mActivity)
        mConvertContentAdapter?.mOnSpeakerNameClick = this
        mConvertContentAdapter?.keyWordClickListener = this
        mConvertContentAdapter?.speakerSelectListener = this
        mConvertContent?.adapter = mConvertContentAdapter
        DebugUtil.i(
            TAG, "===>initConvertContentView: ratio=$ratio" +
                    " container = $container, mViewModel = $mViewModel," +
                    " mConvertViewModel = $mConvertViewModel, mConvertContent = $mConvertContent"
        )
        mContentScrollHelper =
            ContentScrollHelper(container, mViewModel, mConvertViewModel, mConvertContent)
        mContentScrollHelper?.addOnScrollListener()
        mConvertContentAdapter?.mContentScrollHelper = mContentScrollHelper
        if (isImmersiveAnimationWait) {
            isImmersiveAnimationWait = false
            startImmersiveAnimation()
        } else if (isRecoverAnimationWait) {
            isRecoverAnimationWait = false
            reverseImmersiveAnimation()
        }
    }

    fun setViewModel(viewModel: PlaybackActivityViewModel?) {
        DebugUtil.i(TAG, "===>setViewModel: $viewModel")
        mViewModel = viewModel
        mContentScrollHelper?.setViewModel(viewModel)
    }

    override fun getViewModel(): PlaybackActivityViewModel? {
        return mViewModel
    }

    override fun getConvertViewModel(): PlaybackConvertViewModel? {
        return mConvertViewModel
    }

    fun setConvertViewModel(viewModel: PlaybackConvertViewModel?) {
        DebugUtil.i(TAG, "===>setConvertViewModel: $viewModel")
        mConvertViewModel = viewModel
        mContentScrollHelper?.setConvertViewModel(viewModel)
    }

    private fun initConvertingView() {
        if (mConvertingView != null) {
            return
        }
        mConvertingView = mInflater?.inflate(R.layout.fragment_converting, null)
        mConvertCancel = mConvertingView?.findViewById(R.id.convert_cancel)
        mConvertProgress = mConvertingView?.findViewById(R.id.convert_progress)
        mConvertingDesc = mConvertingView?.findViewById(R.id.convert_desc)
        mLoadingView = mConvertingView?.findViewById(R.id.loadingView)
        mSvConverting = mConvertingView?.findViewById(R.id.sv_converting)
        mConvertingTextLayout = mConvertingView?.findViewById(R.id.ll_converting)
        mConvertCancel?.setOnClickListener {
            showContinueTranscribingDialog()
            ConvertStaticsUtil.addConvertCancelEvent()
        }
        updateProgress(0)
        COUITextViewCompatUtil.setPressRippleDrawable(mConvertCancel)
    }

    private fun showContinueTranscribingDialog() {
        if (mActivity == null) {
            DebugUtil.e(TAG, "showContinueTranscribingDialog mActivity is null")
            return
        }
        if (mContinuetranDialog?.isShowing == true) {
            return
        }
        val builder = COUIAlertDialogBuilder(mActivity!!, com.support.dialog.R.style.COUIAlertDialog_Bottom)
        builder.setBlurBackgroundDrawable(true)
        builder.setTitle(com.soundrecorder.common.R.string.cancel_transcribing)
        builder.setMessage(com.soundrecorder.common.R.string.record_cancel_convert_dialog_content)
        builder.setNegativeButton(com.soundrecorder.common.R.string.continue_transcribing) { dialog, which ->
            ConvertStaticsUtil.addConvertContinueDialog()
        }
        builder.setPositiveButton(com.soundrecorder.common.R.string.cancel_transcribing) { dialog, which ->
            ConvertStaticsUtil.addConvertCancelDialog()
            onConvertCancelClickListener?.onClick()
        }
        mContinuetranDialog = builder.create()
        mContinuetranDialog?.setCancelable(true)
        mContinuetranDialog?.show()
        ViewUtils.updateWindowLayoutParams(mContinuetranDialog?.window)
    }

    /**
     * 加载标记数据
     * 1.划线标记所在短句，以句号为分割。多个标记在同一段句，划一次线添加一个小旗子
     * 2.标记分属句逻辑-标记处于两短句之间，该标记归属上一句，该标记处于两段之间，该标记属于上一文段
     * 3.句中只含有图片标记-该句无下划线，无小旗子图标，图文混排插入图片，其他有标记情况都要展示下划线和小旗子
     */
    //todo 调整图片标记的相关分段逻辑
    private fun loadSubSentenceMarkBean(data: List<ConvertContentItem>?) {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) {
            return
        }

        if (data == null || data.isEmpty()) {
            DebugUtil.i(TAG, "loadSubSentenceMarkBean convert data is null or empty return")
            return
        }

        val markList = mViewModel?.markHelper?.getMarkDatas()?.value ?: mutableListOf()
        //这里的return去掉是为了之前如果没有标记数据，后续的图文混排的相关逻辑不会走，导致转文本页面中不会显示文字
        /*if (markList == null || markList.isEmpty()) {
            DebugUtil.i(TAG, "loadSubSentenceMarkBean mark list is null or empty return")
            return
        }*/
        val copyOnWriteArrayList = CopyOnWriteArrayList<MarkDataBean>()
        copyOnWriteArrayList.addAll(markList)
        for (convertIndex in data.indices) {
            var contentItem = data[convertIndex]
            //子句列表
            var listSubSentences = contentItem.listSubSentence
            //time 是子句开始时间
            if (!listSubSentences.isNullOrEmpty()) {
                //循环子句列表
                for (sentencesIndex in listSubSentences.indices) {
                    val subSentence = listSubSentences[sentencesIndex]
                    subSentence.onlyHasSimpleMark = false
                    var startTime = if (sentencesIndex == 0) {
                        //如果是当前item的第一个分句，时间从item的开始时间来计算
                        contentItem.startTime.toFloat()
                    } else {
                        //非第一个分句，开始时间从当前item来计算
                        subSentence.time
                    }

                    var endTime = if (sentencesIndex < listSubSentences.size - 1) {
                        listSubSentences[sentencesIndex + 1].time
                    } else {
                        if (convertIndex < data.size - 1) {
                            var nextConvert = data[convertIndex + 1]
                            nextConvert.startTime.toFloat()
                        } else {
                            //如果是最后一段  以外的标记信息都记入最后一段
                            //录音尾部静音片段的标记会记录到最后一句文本上，即使最后一句文本原本没有标记
                            mViewModel?.playerController?.getDuration()?.toFloat()
                                ?: contentItem.endTime.toFloat()
                        }
                    }
                    // 判断第一个item的第一个分句时，需要将之前的图片标记分配在当前句的列表中
                    val needAddPictureMarkBeforeFirstSentce = (convertIndex == 0) && (sentencesIndex == 0)
                    val isLastSentenceInAudioFile =
                        (convertIndex == (data.size - 1)) && (sentencesIndex == (listSubSentences.size - 1))
                    getMarkListOfTime(
                        subSentence,
                        needAddPictureMarkBeforeFirstSentce,
                        startTime,
                        endTime,
                        isLastSentenceInAudioFile,
                        copyOnWriteArrayList)
                    //文段中是否有文本标记信息
                }
            }
            //第一个convertItem不需要再段落内部将第一句分句之前的ImageMark人为调整到第一句之后，非第一个ConvertItem默认执行分句调整逻辑
            val needJustFirstImage = (convertIndex != 0)
            contentItem.parceNewTextOrImageItems(needJustFirstImage)
        }
    }

    /**
     * 查询结果
     */
    private fun queryKeyWord(data: List<ConvertContentItem>?) {
        if (!FunctionOption.IS_SUPPORT_CONVERT_SEARCH || data.isNullOrEmpty()) {
            return
        }
        val searchWord = mViewModel?.browseSearchWord ?: return
        if (searchHelper == null) {
            searchHelper = ConvertSearchHelper(data)
        } else {
            searchHelper?.convertTextItems = data
        }
        searchHelper?.queryByKeyWord(searchWord)
    }


    private fun getMarkListOfTime(
        subSentence: ConvertContentItem.SubSentence,
        needAddPictureMarkBeforeFirstSentce: Boolean = false,
        startTime: Float,
        endTime: Float,
        lastSentenceInTheFile: Boolean = false,
        copyMarkList: CopyOnWriteArrayList<MarkDataBean>
    ) {
        var newMarkList: MutableList<MarkDataBean> = mutableListOf()
        val iterator: Iterator<MarkDataBean> = copyMarkList.iterator()
        while (iterator.hasNext()) {
            val markDataBean = iterator.next()
            val endTimeSmallerThanEnd = if (lastSentenceInTheFile) {
                //最后一段最后一句需要endTime需要包含，
                markDataBean.correctTime <= endTime
            } else {
                //非最后一段最后一句，endTime不包含
                markDataBean.correctTime < endTime
            }
            //起点时间>=,包含当前起点时间
            val startTimeBiggerThanStart = markDataBean.correctTime >= startTime
            if (endTimeSmallerThanEnd && startTimeBiggerThanStart) {
                //文本标记
                if (!markDataBean.fileExists() && !subSentence.onlyHasSimpleMark) {
                    subSentence.onlyHasSimpleMark = true
                }
                newMarkList.add(markDataBean)
                //减少循环
                copyMarkList.remove(markDataBean)
            } else {
                //是否需要将当前Sentence之前的图片标记放在markList列表中
                if (needAddPictureMarkBeforeFirstSentce &&
                    (markDataBean.correctTime < startTime && markDataBean.correctTime >= 0) &&
                    markDataBean.fileExists()
                ) {
                    newMarkList.add(markDataBean)
                    //减少循环
                    copyMarkList.remove(markDataBean)
                }
            }
        }
        DebugUtil.i(TAG, "getMarkListOfTime needAddPictureMarkBeforeFirstSentce: $needAddPictureMarkBeforeFirstSentce" +
                ", lastSentenceInTheFile $lastSentenceInTheFile, startTime: $startTime  endTime: $endTime" +
                " size: ${newMarkList.size}, inputMarkList: $copyMarkList")
        subSentence.markDataBeanList = newMarkList
    }


    private fun initConvertEmptyView() {
        if (mConvertEmptyView != null) {
            return
        }
        mConvertEmptyView = mInflater?.inflate(R.layout.fragment_convert_empty, null)
        mEmptyConvertImage = mConvertEmptyView?.findViewById(R.id.mEmptyConvertImage) as? OSImageView
        mEmptyConvertText = mConvertEmptyView?.findViewById(R.id.empty_text)
        mSvConvertEmpty = mConvertEmptyView?.findViewById(R.id.sv_convert_empty)
    }

    private fun loadContent(data: List<ConvertContentItem>?, timeString: String) {
        DebugUtil.i(TAG, "updateContent ${data?.size} activity is $mActivity, mViewModel $mViewModel" +
                ", mIsInConvertSearch.Boolean ${mConvertViewModel?.mIsInConvertSearch?.value}, mContentScrollHelper $mContentScrollHelper")
        if (mActivity == null) {
            return
        }
        loadSubSentenceMarkBean(data)
        val convertViewModel = mConvertViewModel ?: return
        convertViewModel.timeItems = ListUtil.getTimeMetaDataList(data)
        convertViewModel.mTextItems = ListUtil.getTextMetaDataList(data)
        if (FunctionOption.IS_SUPPORT_CONVERT_SEARCH) {
            mConvertContentAdapter?.searchHelper = searchHelper
        }
        /*1. 加载所有讲话人数据*/
        mConvertContentAdapter?.loadAllSpeakers(data)
        /*2.根据筛选的讲话人去设置对应UI列表数据*/
        val contentDataList = if (!convertViewModel.selectSpeakerList.isNullOrEmpty()) {
            mConvertContentAdapter?.selectSpeakersData = convertViewModel.selectSpeakerList!!.toMutableList()
            data?.filter { convertViewModel.selectSpeakerList?.contains(it.roleName) == true }
        } else {
            data
        }
        // 解析搜索词
        queryKeyWord(contentDataList)
        mConvertContentAdapter?.setContentData(contentDataList, ListUtil.getMetaDataList(contentDataList))
        mConvertContentAdapter?.mPlayer = mViewModel?.playerController
        mConvertContentAdapter?.setHeaderData(mViewModel?.playName?.value ?: "", timeString)
        smartNaming = mViewModel?.recordId?.let { BrowseFileAction.isSmartNaming(it) }
        mConvertContentAdapter?.refreshSmartNameStatusChange(smartNaming, smartResultName, false)
        //需要调用notify
        mConvertContentAdapter?.notifyDataSetChanged()
        if (convertViewModel.mIsInConvertSearch.value != true) {
            //非搜索情况下走restore逻辑
            mContentScrollHelper?.restoreForRebuild()
        }
        roleControl(convertViewModel.isSpeakerRoleShowing.value == true, true)
    }

    private fun dismissContinuetranDialog() {
        if (mContinuetranDialog?.isShowing == true) {
            mContinuetranDialog?.dismiss()
            mContinuetranDialog = null
        }
    }

    fun switchConvertInitView(needAnimation: Boolean = false) {
        DebugUtil.i(TAG, "switchConvertInitView = $mConvertInitView, needAnimation = $needAnimation")
        if (needAnimation) {
            /*需要动画说明是转文本失败/取消的场景，这时候需要执行alpha动画*/
            doViewAlphaInAndOut(
                alphaInViewList = listOf(mConvertInitTitle, mConvertInitDesc, mConvertStart),
                alphaOutViewList = listOf(mConvertProgress, mConvertingDesc, mConvertCancel),
                actionWhenInAlphaStart = {
                    doStartSwitchConvertInitView()
                }, null
            )
        } else {
            doStartSwitchConvertInitView()
        }
    }

    @VisibleForTesting
    fun doStartSwitchConvertInitView() {
        cancelConvertAnim()
        releaseConvertInitView()
        container?.let {
            it.removeAllViews()
            initConvertInitView()
            it.addView(mConvertInitView)
            startConvertInitAnimation()
        }
        mConvertInitView?.removeOnLayoutChangeListener(mInitLayoutChangeListener)
        mConvertInitView?.addOnLayoutChangeListener(mInitLayoutChangeListener)
        dismissContinuetranDialog()
    }

    fun switchConvertEmptyView() {
        DebugUtil.i(TAG, "switchConvertEmptyView = $mConvertEmptyView")
        doViewAlphaInAndOut(
            listOf(mEmptyConvertText),
            listOf(mConvertProgress, mConvertingDesc, mConvertCancel),
            { doSwitchConvertEmptyView() },
            null
        )
    }

    @VisibleForTesting
    fun doSwitchConvertEmptyView() {
        cancelConvertAnim()
        container?.let { group ->
            group.removeAllViews()
            initConvertEmptyView()
            mConvertEmptyView?.let {
                group.addView(it)
                startConvertEmptyAnimation()
                it.addOnLayoutChangeListener(mEmptyLayoutChangeListener)
            }
        }
        dismissContinuetranDialog()
    }

    fun switchConvertingView() {
        DebugUtil.i(TAG, "switchConvertingView = $mConvertingView")
        cancelConvertInitAnimation()
        doSwitchConvertingView()
    }

    @VisibleForTesting
    fun doSwitchConvertingView() {
        container?.let {
            setViewAlpha(1F, listOf(mConvertProgress, mConvertingDesc, mConvertCancel))
            it.removeAllViews()
            initConvertingView()
            mConvertingView?.let { view ->
                it.addView(view)
            }
            startConvertAnim()
        }
        mConvertingView?.addOnLayoutChangeListener(mConvertingLayoutChangeListener)
    }

    fun switchConvertContentView() {
        DebugUtil.i(TAG, "switchConvertContentView = $mConvertContentView")
        mViewModel?.hasConvertContent?.postValue(true)
        doViewAlphaInAndOut(listOf(mConvertContentView), listOf(mConvertingView), { doSwitchConvertContentView() }, null)
    }

    /**
     * 当白天鹅或者孔雀开合折叠系统多次执行生命周期时
     * Alpha动画执行有延时   mConvertContentView可能会为null
     */
    @VisibleForTesting
    fun doSwitchConvertContentView() {
        cancelConvertAnim()
        val timeString = getTimeString()
        container?.let {
            it.removeAllViews()
            initConvertContentView()
            mConvertContentView?.let { contentView ->
                it.addView(contentView)
            }
        }
        mConvertContentView?.addOnLayoutChangeListener(mLayoutChangeListener)
        dismissContinuetranDialog()
        initConvertContentViewBackButton()
        loadContent(mConvertViewModel?.convertContentData, timeString)
        update(mViewModel?.playerController?.currentTimeMillis?.value ?: 0L)
    }

    /**
     * 当切换转文本页面时候需要执行的alpha切换动效
     * 比如从 转文本初始页面 -> 转写中页面，则需要隐藏初始界面，显示转写中界面
     * 此时
     * alphaInViewList 就包含初始页面的文字和按钮
     * alphaInViewList 就包含转写中页面的文字
     *
     * @param alphaInViewList 需要显示的view list
     * @param alphaInViewList 需要隐藏的view list
     * @param actionWhenInAlphaStart 当显示动效开始执行的时候需要执行的操作
     * @param actionWhenOutAlphaEnd 当隐藏动效结束执行的时候需要执行的操作
     */
    @VisibleForTesting
    fun doViewAlphaInAndOut(
        alphaInViewList: List<View?>,
        alphaOutViewList: List<View?>,
        actionWhenInAlphaStart: (() -> Unit)?,
        actionWhenOutAlphaEnd: (() -> Unit)?
    ) {
        //view退出时候alpha动效
        startFloatValueAnimation(1.0F, ALPHA_ANIMATION_DURATION, { animator ->
            //updateListener
            val animatedValue = 1.0F - (animator.animatedValue) as Float
            setViewAlpha(animatedValue, alphaOutViewList.filter { it != null && it.isVisible })
        }, null, {
            //doOnEnd
            setViewAlpha(1F, alphaOutViewList)
            actionWhenOutAlphaEnd?.invoke()
        })

        //view进入时候alpha动效
        startFloatValueAnimation(1.0F, ALPHA_ANIMATION_DURATION, { animator ->
            //updateListener
            val animatedValue = (animator.animatedValue) as Float
            setViewAlpha(animatedValue, alphaInViewList)
        }, {
            //doOnStart,这里不过滤 isVisible 因为刚开始执行动画的时候，可能view还没添加到页面上去
            setViewAlpha(0F, alphaInViewList.filterNotNull())
            actionWhenInAlphaStart?.invoke()
        }, null, ALPHA_ANIMATION_DELAY)
    }

    /**
     * @param destFloat 目标的Float，从0F开始，到 destFloat 结束
     * @param duration 动画的时长 ms
     * @param listener 动画更新的回调，处理view的alpha
     * @param startAction 动画开始时候需要执行的操作
     * @param endAction 动画结束时候需要执行的操作
     * @param delayStart 需要延迟执行的时长，默认 0ms
     */
    @VisibleForTesting
    fun startFloatValueAnimation(
        destFloat: Float,
        duration: Long,
        listener: ValueAnimator.AnimatorUpdateListener?,
        startAction: ((animator: Animator) -> Unit)?,
        endAction: ((animator: Animator) -> Unit)?,
        delayStart: Long = 0
    ) {
        if (mValueAnimator != null && mValueAnimator?.isRunning == true) {
            mValueAnimator?.cancel()
        }
        mValueAnimator = ValueAnimator.ofFloat(destFloat)
        mValueAnimator?.let {
            it.duration = duration
            it.startDelay = delayStart
            it.addUpdateListener(listener)
            if (startAction != null) {
                it.doOnStart(startAction)
            }
            if (endAction != null) {
                it.doOnEnd(endAction)
            }
            it.start()
        }
    }

    @VisibleForTesting
    fun setViewAlpha(alpha: Float, views: List<View?>) {
        val needAlpha = if (alpha < 0) {
            0F
        } else if (alpha > 1F) {
            1F
        } else {
            alpha
        }
        views.forEach { it?.alpha = needAlpha }
    }

    override fun startConvertInitAnimation() {
        mInitConvertImage?.initImageResource()
    }

    @VisibleForTesting
    fun cancelConvertInitAnimation() {
        mInitConvertImage?.cancelJsonAnimation()
    }

    @VisibleForTesting
    fun startConvertAnim() {
        mLoadingView?.initImageResource()
    }

    @VisibleForTesting
    override fun cancelConvertAnim() {
        mLoadingView?.cancelJsonAnimation()
    }

    @VisibleForTesting
    fun startConvertEmptyAnimation() {
        mEmptyConvertImage?.initImageResource()
    }

    @VisibleForTesting
    fun cancelConvertEmptyAnimation() {
        mEmptyConvertImage?.cancelJsonAnimation()
    }

    private fun scrollToBottom(scrollView: ScrollView?) {
        scrollView?.apply {
            if (isVisible) {
                postDelayed({
                    fullScroll(ScrollView.FOCUS_DOWN)
                }, 100)
            }
        }
    }

    fun updateProgress(progress: Int) {
        var percent = if (progress > 0) "$progress%" else ""
        if (BaseApplication.sIsRTLanguage && (progress > 0)) {
            percent = RtlUtils.addDirectionSymbolForRtl(percent).toString()
        }
        mActivity?.let {
            val progressText = it.getString(com.soundrecorder.common.R.string.transfer_text_progress, percent)
            val descText = it.getString(com.soundrecorder.common.R.string.transferring_content_v2)
            mConvertProgress?.text = progressText
            mConvertingDesc?.text = descText
        }
    }

    fun updateProgress(progress: Int, serverPlanCode: Int) {
        DebugUtil.i(TAG, "updateProgress: progress = $progress, serverPlanCode = $serverPlanCode")
        if (progress <= 0) {
            return
        }
        val context = BaseApplication.getAppContext()
        when (serverPlanCode) {
            SERVER_PLAN_XUNFEI -> {
                val totalTime = TimeUtils.seconds2min(progress)
                val progressText = context.getString(com.soundrecorder.common.R.string.transfer_text_progress, "")
                val text2 = context.getString(com.soundrecorder.common.R.string.total_transfer_time, totalTime)
                val text3 = context.getString(com.soundrecorder.common.R.string.transferring_content_v2)
                val descText = text2 + "\n" + text3
                mConvertProgress?.text = progressText
                mConvertingDesc?.text = descText
            }
            SERVER_PLAN_BEIYAN, SERVER_PLAN_ASR -> {
                val textProgress: String
                val descText = context.getString(com.soundrecorder.common.R.string.transferring_content_v2)
                var percent = if (progress > 0) "$progress% " else ""

                if (BaseApplication.sIsRTLanguage) {
                    percent = RtlUtils.addDirectionSymbolForRtl(percent).toString()
                }
                textProgress = context.getString(com.soundrecorder.common.R.string.transfer_text_progress, percent)
                mConvertProgress?.text = textProgress
                mConvertingDesc?.text = descText
            }
            else -> DebugUtil.w(TAG, "updateProgress serverPlanCode not support:$serverPlanCode")
        }
    }

    private val mLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                if (container?.context?.let {
                        DensityHelper.px2dip(
                            it,
                            rect.height()
                        )
                    } ?: 0 < MIN_HEIGHT) {
                    if (backBtnLayout?.visibility != View.GONE) {
                        backBtnLayout?.visibility = View.GONE
                    }
                }
            }
        }

    private val mEmptyLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                DebugUtil.i(TAG, "mEmptyLayoutChangeListener ${rect.width()}   ${rect.height()}  ${container?.width}  ${container?.height}")
                mActivity?.let {
                    mEmptyConvertImage?.setScaleByEmptySize(
                        it.px2dp(rect.width()).toInt(),
                        it.px2dp(rect.height()).toInt(),
                        "mEmptyLayoutChangeListener"
                    )
                    scrollToBottom(mSvConvertEmpty)
                }
            }
        }

    private val mInitLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                DebugUtil.d(TAG, "mInitLayoutChangeListener ${rect.width()}   ${rect.height()}  ${container?.width}  ${container?.height}")
                mActivity?.let {
                    mInitConvertImage?.let { osImageView ->
                        val width = it.px2dp(rect.width()).toInt()
                        val height = it.px2dp(rect.height()).toInt()
                        val scale = osImageView.getNeedScale(width, height)
                        DebugUtil.d(TAG, "onLayoutChange, width:$width, height:$height, scale:$scale")
                        mConvertInitTextLayout?.apply {
                            updateLayoutParams<ConstraintLayout.LayoutParams> {
                                if (scale == 0F) {
                                    topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                                    bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                                } else {
                                    topToTop = R.id.guide_convert_init
                                    bottomToBottom = ConstraintLayout.LayoutParams.UNSET
                                }
                            }
                        }
                        osImageView.setScaleByEmptySize(width, height, "mConvertingLayoutChangeListener")
                        scrollToBottom(mSvConvertInit)
                        mLastConvertInitScale = scale
                    }
                }
            }
        }

    private val mConvertingLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                DebugUtil.d(TAG, "mConvertingLayoutChangeListener ${rect.width()}   ${rect.height()}  ${container?.width}  ${container?.height}")
                mActivity?.let {
                    mLoadingView?.let { osImageView ->
                        val width = it.px2dp(rect.width()).toInt()
                        val height = it.px2dp(rect.height()).toInt()
                        val scale = osImageView.getNeedScale(width, height)
                        if (mLastConvertConvertingScale != scale) {
                            mConvertingTextLayout?.apply {
                                updateLayoutParams<ConstraintLayout.LayoutParams> {
                                    if (scale == 0F) {
                                        topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                                        bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                                    } else {
                                        topToTop = R.id.guide_converting
                                        bottomToBottom = ConstraintLayout.LayoutParams.UNSET
                                    }
                                }
                            }
                            osImageView.setScaleByEmptySize(width, height, "mConvertingLayoutChangeListener")
                        } else {
                            DebugUtil.d(TAG, "mConvertingLayoutChangeListener scale一样，不需要更新 = $scale")
                        }
                        mLastConvertConvertingScale = scale
                        scrollToBottom(mSvConverting)
                    }
                }
            }
        }

    private fun showBackButtonAnimate() {
        backBtnLayout?.apply {
            mConvertContent?.post {
                val h = DensityHelper.px2dip(context, mConvertContent?.height ?: 0)
                if (h < MIN_HEIGHT) {
                    DebugUtil.i(TAG, "showBackButtonAnimate h is too small, do not show button")
                    return@post
                }
                isVisible = true
                isClickable = true
                updateBackButtonDrawabe(mConvertViewModel?.mContentBackDirection?.value == true)
                animate().setDuration(duration).alpha(1f)
                        .setInterpolator(pathInterpolator).start()
            }
        }
    }

    private fun hideBackButtonAnimate() {
        backBtnLayout?.apply {
            isVisible = true
            isClickable = false
            animate().setDuration(duration).alpha(0f)
                .setInterpolator(pathInterpolator)
                .withEndAction {
                    isGone = true
                }.start()
        }
    }

    private fun initConvertContentViewBackButton() {
        backBtnLayout?.setOnClickListener {
            scrollToLastPositionByManual()
        }
        backBtnLayout?.apply {
            ShadowUtils.setElevationToView(
                this,
                ShadowUtils.SHADOW_LV3,
                ViewUtils.dp2px(NumberConstant.NUM_F26_0).toInt(),
                ViewUtils.dp2px(NumberConstant.NUM_F2_0).toInt(),
                resources.getColor(com.support.snackbar.R.color.coui_snack_bar_background_shadow_color)
            )
        }

        lifecycleOwner?.let {
            mConvertViewModel?.mHasUserDraggedText?.observe(it, androidx.lifecycle.Observer { it1 ->
                if (it1) {
                    showBackButtonAnimate()
                } else {
                    if (backBtnLayout?.visibility == View.VISIBLE) {
                        hideBackButtonAnimate()
                    }
                }
            })

            mViewModel?.playerController?.mIsTouchSeekbar?.observe(it) { isTouching ->
                if (isTouching) {
                    mConvertViewModel?.mHasUserDraggedText?.value = false
                }
            }



            mConvertViewModel?.mContentBackDirection?.observe(
                it,
                androidx.lifecycle.Observer { it1 ->
                    updateBackButtonDrawabe(it1)
                })

            mViewModel?.lastMarkAction?.observe(it) { action ->
                DebugUtil.i(TAG, "lastMarkAction changed $action")
                when (action) {
                    //目前只处理增删
                    PlaybackActivityViewModel.MARK_ACTION_DELETE,
                    PlaybackActivityViewModel.MARK_ACTION_ADD -> {
                        //图片标记删除或增加时，更新viewModel中的图文混排列表和
                        val contentList = mConvertContentAdapter?.mContentItemList
                        loadSubSentenceMarkBean(contentList)
                        mConvertViewModel?.mTextItems = ListUtil.getTextMetaDataList(mConvertViewModel?.convertContentData)
                        mConvertViewModel?.timeItems = ListUtil.getTimeMetaDataList(mConvertViewModel?.convertContentData)
                        mConvertContentAdapter?.setContentData(contentList, ListUtil.getMetaDataList(contentList))
                        mConvertContentAdapter?.notifyDataSetChanged()
                    }
                }
            }

            mConvertViewModel?.searchAnimEnd?.observe(it) { value ->
                if (value) { //进入搜索动画结束
                    // 转文本界面的关键词需要隐藏
                    hideKeyWordView()
                }
            }
        }
    }

    private fun updateBackButtonDrawabe(isDown: Boolean) {
        if (backBtnLayout?.visibility != View.VISIBLE) {
            return
        }
        if (isDown) {
            mConvertBackButton?.setImageResource(R.drawable.ic_back_btn_down)
        } else {
            mConvertBackButton?.setImageResource(R.drawable.ic_back_btn_up)
        }
    }


    fun getTimeString(): String {
        return ListUtil.getTimeString(mViewModel?.recordId, mViewModel?.playName?.value)
    }

    fun updatePlayName() {
        mConvertContentAdapter?.setHeaderData(mViewModel?.playName?.value ?: "", getTimeString())
        mConvertContentAdapter?.notifyDataSetChanged()
    }

    fun removeAllViews() {
        container?.removeAllViews()
    }

    interface OnConvertCancelClickListener {
        fun onClick()
    }

    interface OnConvertStartClickListener {
        fun onClick()
    }

    fun setIsNeedShowAnimView(needShowAnimView: Boolean) {
        isNeedShowAnimView = needShowAnimView
    }


    override fun onClick(pos: Int) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        if (mViewModel?.recordId == null) {
            return
        }
        mViewModel?.dataPosition = pos
        val content =
            mViewModel?.dataPosition?.let { mConvertContentAdapter?.mContentItemList?.get(it)?.roleName }
                ?: ""
        mConvertViewModel?.originContent = content
        val originContent = mConvertViewModel?.originContent ?: ""
        showRenameSpeakerDialog(content, true, originContent)
        //add click speaker tips point
        ConvertStaticsUtil.addSpeakerClickSpeakerTipEvent()
    }

    private fun renameSpeaker(
        recordId: Long,
        checked: Boolean,
        roleName: String,
        isClickHistory: Boolean
    ) {
        if (mViewModel?.dataPosition == null) {
            return
        }
        DebugUtil.i(TAG, "renameSpeaker recordId $recordId, dataPosition ${mViewModel?.dataPosition}")
        if (checked) {
            renameSpeakerAll(recordId, mViewModel?.dataPosition!!, roleName)
        } else {
            renameSpeakerOne(recordId, mViewModel?.dataPosition!!, roleName)
        }
        //add rename speaker point
        if (isClickHistory) {
            ConvertStaticsUtil.addSpeakerRenameByHistoryEvent()
        }
    }

    private fun deleteSpeaker(recordId: Long, isClear: Boolean, roleName: String) {
        if (isClear) {
            deleteHistoryRoleNameAll(recordId)
        } else {
            deleteHistoryRoleNameOne(recordId, roleName)
        }
    }

    private fun renameSpeakerOne(recordId: Long, pos: Int, roleName: String) {
        val item = mConvertContentAdapter?.mContentItemList?.get(pos)
        val oldName = item?.roleName ?: ""
        mConvertViewModel?.convertContentData?.find {
            it.startTime == item?.startTime && it.endTime == item.endTime
        }?.run {
            this.roleName = roleName
            this.changeRoleNameForTimeDivider(roleName)
        }

        mConvertContentAdapter?.let {
            val isSelectAllBeforeRename = it.isRealSelectAllSpeaker()
            // 更新所有讲话人名称列表
            it.loadAllSpeakers(mConvertViewModel?.convertContentData)
            it.renameSpeakerSuccess(roleName, oldName, isSelectAllBeforeRename)
        }
        CoroutineUtils.doInIOThread(
            {
                reWriteConvertFile(recordId, mConvertViewModel?.convertContentData)
                RoleNameUtil.addHistoryRoleName(recordId, roleName)
            },
            mConvertViewModel?.viewModelScope!!
        )

        //add rename current speaker point
        ConvertStaticsUtil.addSpeakerRenameCurrentEvent()
    }

    private fun renameSpeakerAll(recordId: Long, pos: Int, roleName: String) {
        val item = mConvertContentAdapter?.mContentItemList?.get(pos)
        val oldName = item?.roleName
        mConvertViewModel?.convertContentData?.forEach { i ->
            if (i.roleName == oldName) {
                i.roleName = roleName
                i.changeRoleNameForTimeDivider(roleName)
            }
        }
        mConvertContentAdapter?.let {
            val isSelectAllBeforeRename = it.isRealSelectAllSpeaker()
            // 更新所有讲话人名称列表
            it.loadAllSpeakers(mConvertViewModel?.convertContentData)
            it.renameSpeakerSuccess(roleName, oldName ?: "", isSelectAllBeforeRename)
        }
        CoroutineUtils.doInIOThread(
            {
                reWriteConvertFile(recordId, mConvertViewModel?.convertContentData)
                RoleNameUtil.addHistoryRoleName(recordId, roleName)
            },
            mConvertViewModel?.viewModelScope!!
        )
        //add rename all speaker point
        ConvertStaticsUtil.addSpeakerRenameAllEvent()
    }

    private fun deleteHistoryRoleNameOne(recordId: Long, roleName: String) {
        CoroutineUtils.doInIOThread(
            {
                RoleNameUtil.deleteHistoryRoleName(recordId, roleName)
            },
            mViewModel?.viewModelScope!!
        )
    }

    private fun deleteHistoryRoleNameAll(recordId: Long) {
        CoroutineUtils.doInIOThread(
            {
                RoleNameUtil.clearHistoryRoleName(recordId)
            },
            mViewModel?.viewModelScope!!
        )
    }


    private fun reWriteConvertFile(recordId: Long, dataList: List<ConvertContentItem>?) {
        val convertRecord = ConvertDbUtil.selectByRecordId(recordId)
        val textFilePath = convertRecord?.convertTextfilePath
        DebugUtil.e(TAG, "reWriteConvertFile textFilePath: $textFilePath")
        if (dataList != null) {
            for (i in dataList) {
                DebugUtil.i(TAG, "==>reWriteConvertFile item: $i")
            }
        }
        if (convertRecord?.isOShareFile == true) {
            ConvertToUtils.reWriteOShareConvertFile(convertRecord.recordId, textFilePath, dataList)
        } else {
            ConvertToUtils.reWriteConvertFile(
                textFilePath,
                dataList
            )
        }
    }

    fun showRenameSpeakerDialog(content: String, isModifyAll: Boolean, originContent: String) {
        if (mViewModel == null) {
            return
        }
        val recordId = mViewModel!!.recordId
        DebugUtil.i(TAG, "showRenameDialog recordId:$recordId")
        val list = RoleNameUtil.getHistoryRoleName(recordId)
        for (i in list) {
            DebugUtil.e(TAG, "showRenameDialog i:$i")
        }
        if (mActivity == null) {
            return
        }
        mRenameSpeakerDialog?.dismiss()
        mRenameSpeakerDialog = ConvertRenameBottomSheetDialog(mActivity!!)
        mRenameSpeakerDialog?.content = content
        mRenameSpeakerDialog?.originContent = originContent
        mRenameSpeakerDialog?.recordId = recordId
        mRenameSpeakerDialog?.roleNameList = list
        mRenameSpeakerDialog?.mIsModifyAll = isModifyAll
        mRenameSpeakerDialog?.setClickListener(mClickListener)
        mRenameSpeakerDialog?.show()

        /*讲话人编辑次数 累加*/
        RecorderUserActionKt.sEditCount++
        /* 判断 是否应用到全部讲话人 */
        if (isModifyAll) {
            RecorderUserActionKt.sIsAppliedAll = RecorderUserAction.VALUE_APPLY_ALL_YES
        }
    }

    fun getRenameSpeakerDialog(): ConvertRenameBottomSheetDialog? {
        return mRenameSpeakerDialog
    }

    fun releaseRenameSpeakerDialog() {
        mRenameSpeakerDialog?.setClickListener(null)
        mRenameSpeakerDialog = null
    }

    /**
     * @param refresh true -> notifyDataChange()  false -> speaker animate
     */
    fun roleControl(needShowRole: Boolean, refresh: Boolean) {
        DebugUtil.i(TAG, "roleControl needShowRole $needShowRole, refresh: $refresh")
        mConvertContentAdapter?.roleControl(needShowRole, refresh)
        /*关闭讲话人，若已选择讲话人，则重置讲话人数据*/
        if (!needShowRole && mConvertContentAdapter?.selectSpeakersData?.isEmpty() == false) {
            mConvertContentAdapter?.selectSpeakersData?.clear()
            updateAdapterContent(mConvertViewModel?.convertContentData)
        }
    }

    fun addSpeakerTipTask(isOnConvertWhenViewPager2IDLE: () -> Boolean) {
        val activity = mActivity
        if (activity == null) {
            restorePageScroll()
            return
        }
        if (activity.isFinishing) {
            DebugUtil.w(TAG, "addSpeakerTipTask return by activity isFinishing")
            return
        }
        val lifecycle = activity.lifecycle
        TipUtil.checkShow(
            {
                if (isOnConvertWhenViewPager2IDLE.invoke()) {
                    val parent = mConvertContent?.layoutManager?.getChildAt(1)
                    parent?.findViewById<LinearLayout>(R.id.ll_speaker)
                } else {
                    null
                }
            },
            TYPE_ROLE_NAME,
            DELAY_TIME,
            lifecycle,
            activity.isInMultiWindowMode,
            onFinish = {
                restorePageScroll()
            }
        )
    }

    private fun restorePageScroll() {
        DebugUtil.i(TAG, "restorePageScroll")
        getCustomLinearLayoutManager()?.mCanScrollVertically = true
        scrollToLastPositionByManual()
        mViewModel?.mIsPageStopScroll?.postValue(false)
        mConvertViewModel?.viewPagerScrollEnable?.value = true
    }

    fun removeSpeakerTipTask() {
        DebugUtil.i(TAG, "removeSpeakerTipTask")
        TipUtil.dismissSelf(TYPE_ROLE_NAME)
    }

    fun postShowSwitch(boolean: Boolean) {
        mViewModel?.mShowSwitch?.postValue(boolean)
    }

    fun postRoleNumber(roleNumber: Int) {
        mViewModel?.mSpeakerNumber?.postValue(roleNumber)
    }

    fun getConvertContentData(): List<ConvertContentItem>? {
        return mConvertViewModel?.convertContentData
    }

    companion object {
        private const val TAG = "ConvertViewController"
        const val NUMBER_100F = 100F
        const val DELAY_TIME = 300L
        const val ALPHA_ANIMATION_DURATION = 180L
        const val ALPHA_ANIMATION_DELAY = 163L
        private const val RESPONSE_VIEW_SPACE_IMMERSIVE_ANIMATION: Float = 0.16f
    }

    override fun getCustomLinearLayoutManager(): CustomLinearLayoutManager? {
        return this.mLinearLayoutManager
    }


    /**
     * 显示讲话人新手提示
     */
    fun stopScroll() {
        if (mActivity?.isInMultiWindowMode == true) {
            //在分屏下，触发了新手提示，那么直接保存，后续都不再显示了
            TipUtil.saveShowedTip(TYPE_ROLE_NAME)
            return
        }
        if (LandScapeUtil.spitWindowHeightLessThanForPlay450(mActivity)) {
            //横屏-设备高度小于450dp，触发了新手提示，那么直接保存，后续都不再显示了
            TipUtil.saveShowedTip(TYPE_ROLE_NAME)
            return
        }
        mConvertViewModel?.viewPagerScrollEnable?.value = false
        mContentScrollHelper?.stopScrollForce()
    }

    override fun scrollToLastPositionByManual() {
        mContentScrollHelper?.scrollToLastPositionByManual()
    }

    fun update(currentTimeMillis: Long) {
        mContentScrollHelper?.update(currentTimeMillis)
    }

    override fun setNeedShowRoleName(needShowRole: Boolean) {
        mContentScrollHelper?.setNeedShowRoleName(needShowRole)
    }

    /**
     * 设置关键词
     * @param keyWords 关键词列表，有可能为空，为空时UI自动转成“提取关键词”
     */
    fun setKeyWords(keyWords: List<String>) {
        val state = mConvertViewModel?.extractState ?: KeyWordChipGroup.DEFAULT_STATE
        mConvertContentAdapter?.setKeyWords(keyWords, state)
    }

    /**
     * 隐藏关键词的View
     */
    private fun hideKeyWordView() {
        DebugUtil.d(TAG, "隐藏关键词View")
        val viewHolder = findConvertHeader() ?: return
        viewHolder.hideKeyWordView()
    }

    /**
     * 显示关键词的View
     */
    private fun showKeyWordView() {
        DebugUtil.d(TAG, "显示关键词View")
        val viewHolder = findConvertHeader() ?: return
        viewHolder.showKeyWordView()
    }

    /**
     * 查找转文本的Header
     */
    private fun findConvertHeader(): TextImageItemAdapter.ConvertHeaderViewHolder? {
        val viewHolder = mConvertContent?.findViewHolderForAdapterPosition(0)
        if (Objects.isNull(viewHolder)) {
            DebugUtil.e(TAG, "findConvertHeader position 0 holder is null")
            return null
        }
        if (viewHolder is TextImageItemAdapter.ConvertHeaderViewHolder) {
            return viewHolder
        }
        return null
    }

    /**
     * 退出搜索动效
     */
    override fun animSearchOut() {
        val inScreen = mConvertViewModel?.isHeaderInScreen()
        DebugUtil.d(TAG, "关键词View 是否在当前屏幕：$inScreen")
        if (inScreen == true) {
            DebugUtil.d(TAG, "退出搜索动效")
            mConvertContent?.post { // 防止从其他位置滚动到0，会出现闪一下的效果
                hideKeyWordView() // 防止之前的View没有Gone，导致View显示->View从0到高显示出来，会出现闪一下的效果
                mConvertContentAdapter?.triggerSearchAnim()
            }
        } else {
            showKeyWordView()
        }
    }

    /**
     * 获取搜索view的高度
     */
    override fun saveKeyWordViewHeight() {
        val header = findConvertHeader()
        header?.saveKeyWordViewHeight()
    }

    /**
     * 提取关键词
     */
    override fun extractKeyWord(): Boolean {
        // 调用网络接口提取关键词
        val success = keyWordSupplier?.get() ?: false
        // 记录加载按钮的状态
        mConvertViewModel?.extractState = if (success) {
            KeyWordChipGroup.LOADING_STATE
        } else {
            KeyWordChipGroup.DEFAULT_STATE
        }
        return success
    }

    /**
     * 点击跳转到转文本搜索界面
     */
    override fun onClickKeyWord(chip: COUIChip, keyWord: String) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        // 记录滚动位置
        saveScrollPosition("keyword_chip")
        mConvertViewModel?.let {
            it.mConvertSearchValue = keyWord // 搜索内容
            it.autoSearch = true //开启自动搜索
            it.clickChipCount++ //点击关键词标签次数
            it.mIsInConvertSearch.value = true // 跳转到文本搜索页面
        }
    }

    /**
     * 记录滚动的位置
     * @param from
     */
    override fun saveScrollPosition(from: String) {
        val layoutManager = getCustomLinearLayoutManager()
        layoutManager?.let {
            mConvertViewModel?.saveScrollPosition(from, it)
        }
    }

    /**
     * 退出搜索检测播放位置与当前位置
     * 是否要显示返回气泡
     */
    override fun checkOutSearchShowBackOfLocation() {
        mContentScrollHelper?.updateContentBackButton(true)
    }

    override fun dismissRenameSpeakerDialog() {
        mRenameSpeakerDialog?.dismissPop()
    }

    override fun onSpeakerSelect(speakers: List<String>, selectAll: Boolean) {
        DebugUtil.d(TAG, "onSpeakerSelect= ${speakers.size},selectAll=$selectAll")
        mConvertViewModel?.selectSpeakerList = speakers

        val originAllContent = mConvertViewModel?.convertContentData
        if (speakers.isEmpty() || selectAll) {
            queryKeyWord(originAllContent)
            updateAdapterContent(originAllContent)
            updatePlayTimeSegmentList(null)
            return
        }
        originAllContent?.filter { speakers.contains(it.roleName) }?.run {
            queryKeyWord(this)
            updateAdapterContent(this)
            updatePlayTimeSegmentList(this)
        }
    }

    /**
     * 更新播放时间片
     */
    private fun updatePlayTimeSegmentList(contentItemList: List<ConvertContentItem>?) {
        DebugUtil.d(TAG, "updatePlayTimeSegmentList ${contentItemList?.size}")
        if (contentItemList.isNullOrEmpty()) {
            mViewModel?.targetPlaySegment?.value = null
            return
        }
        val timeSegmentList = arrayListOf<Pair<Long, Long>>()
        var pair: Pair<Long, Long>?
        contentItemList.forEach {
            pair = Pair(it.startTime, it.endTime)
            timeSegmentList.add(pair!!)
        }
        mViewModel?.targetPlaySegment?.value = timeSegmentList
    }

    /**
     * 更新UI数据
     */
    private fun updateAdapterContent(contentItemList: List<ConvertContentItem>?) {
        mConvertContentAdapter?.let {
            it.setContentData(contentItemList, ListUtil.getMetaDataList(contentItemList))
            it.notifyDataSetChanged()
        }
    }

    private fun initImmersiveAnimation() {
        viewSpaceImmersiveAnimation = COUISpringAnimation(viewSpace, COUIDynamicAnimation.ALPHA, 1f)
        val spring = COUISpringForce()
            .setFinalPosition(0f)
            .setBounce(0f)
            .setResponse(RESPONSE_VIEW_SPACE_IMMERSIVE_ANIMATION)
        viewSpaceImmersiveAnimation?.setSpring(spring)
    }

    override fun startImmersiveAnimation() {
        //布局还未初始化，等初始化完成后再执行动画
        if (viewSpace == null) {
            isImmersiveAnimationWait = true
            DebugUtil.i(TAG, "startImmersiveAnimation  viewSpace is null")
            return
        }
        if (viewSpaceImmersiveAnimation == null) {
            initImmersiveAnimation()
        } else {
            viewSpaceImmersiveAnimation?.apply {
                spring.finalPosition = 0f
            }
        }
        viewSpaceImmersiveAnimation?.start()
    }

    override fun reverseImmersiveAnimation() {
        //viewSpaceImmersiveAnimation为空表示界面不在沉浸态
        if (viewSpaceImmersiveAnimation == null) {
            DebugUtil.i(TAG, "reverseImmersiveAnimation  viewSpaceImmersiveAnimation is null")
            return
        }
        //布局还未初始化，等初始化完成后再执行动画
        if (viewSpace == null) {
            isRecoverAnimationWait = true
            DebugUtil.i(TAG, "reverseImmersiveAnimation  viewSpace is null")
            return
        }
        viewSpaceImmersiveAnimation?.animateToFinalPosition(1f)
    }

    fun updateSmartNameStatus(display: Boolean?, resultName: String?) {
        smartNaming = display ?: false
        smartResultName = resultName
        mConvertContentAdapter?.refreshSmartNameStatusChange(display, resultName)
    }
}