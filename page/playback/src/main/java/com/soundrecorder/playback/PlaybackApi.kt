/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback

import android.content.Context
import android.content.Intent
import com.inno.ostitch.annotation.Action
import com.inno.ostitch.annotation.Component
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.modulerouter.playback.PlaybackAction
import com.soundrecorder.playback.cloudconfig.CloudConfigUtils
import oplus.multimedia.soundrecorder.playback.mute.MuteCacheManager
import com.soundrecorder.playback.newconvert.ConvertUtils
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import com.soundrecorder.playback.smartname.SmartNameManagerImpl
import com.soundrecorder.wavemark.uti.CallNameParseHelper
import oplus.multimedia.soundrecorder.playback.mute.detector.MuteDataDetectorWorker
import java.util.ArrayList

@Component(PlaybackAction.COMPONENT_NAME)
object PlaybackApi {

    private const val TAG = "PlaybackApi"

    @Action(PlaybackAction.CREATE_PLAYBACK_INTENT)
    @JvmStatic
    fun createPlayBackIntent(context: Context): Intent {
        val jumpIntent = Intent()
        jumpIntent.setClassName(context.packageName, PlaybackActivity::class.java.name)
        return jumpIntent
    }

    @Action(PlaybackAction.IS_PLAYBACK_ACTIVITY)
    @JvmStatic
    fun isPlaybackActivity(context: Context): Boolean = context is PlaybackActivity

    @Action(PlaybackAction.CANCEL_ALL_CONVERT_TASK)
    @JvmStatic
    fun cancelAllConvertTask() {
        ConvertUtils.cancelAllConvertTask()
    }

    @Action(PlaybackAction.STOP_CONVERT_SERVICE)
    @JvmStatic
    fun stopConvertService(context: Context) {
        ConvertUtils.stopConvertService(context)
    }

    @Action(PlaybackAction.CLEAR_MUTE_CACHE)
    @JvmStatic
    fun clearMuteCache(filePath: String) {
        MuteCacheManager.delete(filePath)
    }

    @Action(PlaybackAction.GET_PLAYBACK_ACTIVITY_CLASS)
    @JvmStatic
    fun getPlaybackActivityClass(): Class<*> {
        return PlaybackActivity::class.java
    }

    @Action(PlaybackAction.START_MUTE_DETECT_IF_NECESSARY)
    @JvmStatic
    fun startMuteDetectIfNecessary(fullPath: String?) {
        MuteDataDetectorWorker.startMuteDetectIfNecessary(fullPath)
    }

    @Action(PlaybackAction.UPDATE_CONVERT_CONFIG)
    @JvmStatic
    fun updateConvertConfig() {
        CloudConfigUtils.updateConvertConfig()
    }

    @Action(PlaybackAction.IS_SUPPORT_WPS_EXPORT)
    @JvmStatic
    fun isSupportWpsExport(): Boolean {
        return CloudConfigUtils.isSupportWpsExport
    }

    @Action(PlaybackAction.READ_CONVERT_CONTENT)
    @JvmStatic
    fun readConvertContent(
        appContext: Context?,
        filename: String?,
        serverPlanCode: Int?
    ): ArrayList<ConvertContentItem>? {
        kotlin.runCatching {
            return ConvertToUtils.readConvertContent(appContext, filename, serverPlanCode)
        }.onFailure {
            DebugUtil.e(PlaybackConvertViewModel.TAG, "readConvertContent error.$it", it)
        }
        return null
    }

    @Action(PlaybackAction.READ_OPPO_SHARE_CONVERT_CONTENT)
    @JvmStatic
    fun readOShareConvertContent(filePath: String?, serverPlanCode: Int?): ArrayList<ConvertContentItem>? {
        kotlin.runCatching {
            return ConvertToUtils.readOShareConvertContent(filePath, serverPlanCode)
        }.onFailure {
            DebugUtil.e(PlaybackConvertViewModel.TAG, "readConvertContent error.$it", it)
        }
        return null
    }

    @Action(PlaybackAction.INIT_SMART_NAME_CONVERT_MANAGER)
    @JvmStatic
    fun initSmartNameManager(): ISmartNameManager? {
        return SmartNameManagerImpl()
    }

    @Action(PlaybackAction.PARSE_CALL_NAME)
    @JvmStatic
    fun parCallName(mContext: Context, path: String? = null, mediaId: Long = -1L, mimeType: String): String? {
        var name: String? = null
        when (mimeType) {
            RecordConstant.MIMETYPE_ACC,
            RecordConstant.MIMETYPE_ACC_ADTS -> name = path?.let { CallNameParseHelper.parseCallName(it) }
            RecordConstant.MIMETYPE_MP3 -> {
                // 通过mimeType判断是否为mp3，拦截 非mp3文件
                name = CallNameParseHelper.parseCallName(mContext, mediaId)
            }
        }
        return name
    }

    @Action(PlaybackAction.RELEASE_MP3_FILE)
    @JvmStatic
    fun releaseMp3File() {
        CallNameParseHelper.releaseMp3()
    }
 }