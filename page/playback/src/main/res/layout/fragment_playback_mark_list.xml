<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <com.coui.appcompat.toolbar.COUIToolbar
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:id="@+id/mark_list_toolbar"
        style="@style/Widget.COUI.Toolbar.Panel"
        android:background="@null"/>

    <androidx.recyclerview.widget.COUIRecyclerView
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mark_list_toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:id="@+id/mark_list_recycler_view"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>