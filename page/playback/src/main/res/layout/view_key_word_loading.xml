<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.button.COUILoadingButton xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/key_word_loading"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp28"
    android:layout_centerHorizontal="true"
    android:background="@drawable/shape_loading_bg"
    android:ellipsize="end"
    android:textStyle="bold"
    android:maxWidth="@dimen/dp220"
    android:minWidth="@dimen/dp52"
    android:paddingStart="@dimen/dp12"
    android:paddingEnd="@dimen/dp12"
    android:text="@string/extract_keywords"
    android:textAlignment="center"
    android:textColor="@color/keyword_text_color"
    android:textSize="@dimen/dp14"
    app:animEnable="true"
    app:animType="0"
    app:brightness="0.8"
    app:drawableRadius="@dimen/dp14" />
