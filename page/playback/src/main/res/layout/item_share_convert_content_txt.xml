<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_roles"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black_color"
        android:lineSpacingExtra="3sp"
        android:textSize="@dimen/sp14" />

    <LinearLayout
        android:id="@+id/ll_share_top_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_below="@id/tv_roles"
        android:orientation="horizontal" />

    <LinearLayout
        android:id="@+id/layout_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/item_text_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!--使用要根据实际的XML来进行适配-->
        <com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout
            android:id="@+id/animator_speaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_speaker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:lines="1"
                android:maxWidth="@dimen/playback_tv_speaker_maxLength"
                android:paddingEnd="@dimen/dp8"
                android:textColor="@color/percent_85_black"
                android:textSize="@dimen/sp14"
                android:fontFamily="sans-serif-medium"
                tools:text="@string/convert_speaker" />

        </com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout>

        <!--使用要根据实际的XML来进行适配-->
        <com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout
            android:id="@+id/animator_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/start_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:lines="1"
                android:maxWidth="@dimen/playback_tv_speaker_maxLength"
                android:textColor="@color/percent_85_black"
                android:textSize="@dimen/sp14"
                android:fontFamily="sans-serif-medium"
                tools:text="00:02" />

        </com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/item_text_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_share_top_divider"
        android:layout_marginTop="@dimen/dp4"
        android:lineSpacingExtra="3sp"
        android:paddingBottom="@dimen/dp4"
        android:textColor="@color/percent_85_black"
        android:textIsSelectable="true"
        android:textSize="@dimen/sp14"
        tools:text="@string/convert_statement_content" />
</RelativeLayout>