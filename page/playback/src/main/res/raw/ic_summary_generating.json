{"v": "5.9.6", "fr": 60, "ip": 0, "op": 300, "w": 72, "h": 72, "nm": "Star-20dp", "ddd": 0, "assets": [{"id": "image_0", "w": 60, "h": 60, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_1", "w": 150, "h": 150, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "star-glow_00000.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.341], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.171], "y": [0]}, "t": 39.754, "s": [100]}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.171], "y": [0]}, "t": 46, "s": [100]}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 133, "s": [0]}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.341], "y": [0]}, "t": 150, "s": [0]}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.171], "y": [0]}, "t": 189.754, "s": [100]}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.171], "y": [0]}, "t": 196, "s": [100]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.001], "y": [0]}, "t": 283, "s": [0]}, {"t": 300, "s": [0]}]}, "p": {"a": 0, "k": [36, 36, 0]}, "a": {"a": 0, "k": [30, 30, 0]}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"p": {"a": 0, "k": [36, 36, 0]}, "s": {"a": 0, "k": [-100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.436, 1.415], [0, 0], [0.178, -0.577], [0, 0], [1.415, -0.436], [0, 0], [-0.577, -0.178], [0, 0], [-0.436, -1.415], [0, 0], [-0.178, 0.577], [0, 0], [-1.415, 0.436], [0, 0], [0.577, 0.178], [0, 0]], "o": [[0, 0], [-0.178, -0.577], [0, 0], [-0.436, 1.415], [0, 0], [-0.577, 0.178], [0, 0], [1.415, 0.436], [0, 0], [0.178, 0.577], [0, 0], [0.436, -1.415], [0, 0], [0.577, -0.178], [0, 0], [-1.415, -0.436]], "v": [[0.748, -3.707], [0.586, -4.234], [-0.586, -4.234], [-0.748, -3.707], [-3.707, -0.748], [-4.234, -0.586], [-4.234, 0.586], [-3.707, 0.748], [-0.748, 3.707], [-0.586, 4.234], [0.586, 4.234], [0.748, 3.707], [3.707, 0.748], [4.234, 0.586], [4.234, -0.586], [3.707, -0.748]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "BG", "td": 1, "sr": 1, "ks": {"p": {"a": 0, "k": [36, 36, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [20, 20]}, "p": {"a": 0, "k": [0, 0]}, "nm": "Ellipse Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0.10000000149]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [300, 300]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "圆 黑色", "bm": 0, "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "NoiseTex_150px_00000000.png", "cl": "png", "tt": 1, "refId": "image_1", "sr": 1, "ks": {"r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 299, "s": [360]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [60.5, 65.25, 0], "to": [20.333, -15.75, 0], "ti": [20.411, 18.53, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.161}, "t": 88.016, "s": [64, 2.25, 0], "to": [-32.241, -21.151, 0], "ti": [1.083, -32.75, 0]}, {"i": {"x": 0.833, "y": 0.762}, "o": {"x": 0.167, "y": 0.164}, "t": 193.506, "s": [-6.449, 31.093, 0], "to": [-1.473, 44.524, 0], "ti": [-26.741, 18.599, 0]}, {"t": 299, "s": [60.5, 65.25, 0]}]}, "a": {"a": 0, "k": [75, 75, 0]}, "s": {"a": 0, "k": [120, 120, 100]}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": []}