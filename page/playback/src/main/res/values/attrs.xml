<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="RestrictChipGroup">
        <attr name="max_height" format="dimension" />
    </declare-styleable>

    <declare-styleable name="BackgroundTextView">
        <attr name="background_color" format="color" />
    </declare-styleable>

    <attr name="animViewWidth" format="dimension" />
    <attr name="animViewHeight" format="dimension" />
    <attr name="anim_rawRes_light" format="reference" />
    <attr name="anim_rawRes_night" format="reference" />
    <attr name="default_image_res" format="reference" />
    <attr name="bottom_text" format="string" />
    <attr name="bottom_text_margin_top" format="dimension" />
    <attr name="bottom_text_paddingBottom" format="dimension" />
    <attr name="bottom_text_paddingHorizontal" format="dimension" />
    <attr name="bottom_text_minWidthStyle" format="boolean" />

    <declare-styleable name="RepeatAnimationTextView">
        <attr name="animViewWidth" />
        <attr name="animViewHeight" />
        <attr name="anim_rawRes_light" />
        <attr name="anim_rawRes_night" />
        <attr name="default_image_res" />
        <attr name="bottom_text" />
        <attr name="bottom_text_margin_top"/>
        <attr name="bottom_text_paddingBottom" />
        <attr name="bottom_text_paddingHorizontal" />
        <attr name="bottom_text_minWidthStyle" />
    </declare-styleable>

    <declare-styleable name="TransferAnimationTextView">
        <attr name="animViewWidth" />
        <attr name="animViewHeight" />
        <attr name="anim_rawRes_light" />
        <attr name="anim_rawRes_night" />
        <attr name="default_image_res" />
        <attr name="bottom_text" />
        <attr name="bottom_text_margin_top"/>
        <attr name="bottom_text_paddingBottom" />
        <attr name="bottom_text_paddingHorizontal" />
        <attr name="bottom_text_minWidthStyle" />
        <attr name="animFrameRate" format="integer" />
        <attr name="animRepeatRateStart" format="integer" />
        <attr name="animRepeatRateEnd" format="integer" />
    </declare-styleable>

    <declare-styleable name="TextImageMixLayout">
        <attr name="first_item_top_margin" format="integer|reference" />
        <attr name="last_item_buttom_margin" format="integer|reference" />
        <attr name="margin_between_image_and_text" format="integer|reference" />
        <attr name="margin_between_text_and_image" format="integer|reference" />
        <attr name="margin_between_image_and_image" format="integer|reference" />
        <attr name="image_round_corner_radis" format="integer|reference" />
        <attr name="text_background_color" format="color|reference" />
    </declare-styleable>

    <!--  播放界面转文本tab页，向上滑动出现分割线。divider_depend_id：分割线需要依赖的view的recyclerView的id  -->
    <declare-styleable name="PlayConvertTitleDivider">
        <attr name="divider_depend_id" format="integer|reference" />
    </declare-styleable>

    <declare-styleable name="PlaySettingSwitchPreference">
        <attr name="isDataReady" format="boolean"/>
    </declare-styleable>
</resources>