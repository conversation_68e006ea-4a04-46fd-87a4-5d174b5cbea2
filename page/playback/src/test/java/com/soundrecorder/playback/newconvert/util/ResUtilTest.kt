/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ResUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.util

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ResUtilTest {

    @Test
    fun should_init_success_when_getColorArray() {
        Assert.assertTrue(ResUtil.getColorArray().isNotEmpty())
    }

    @Test
    fun should_init_success_when_getDrawableList() {
        Assert.assertTrue(ResUtil.getDrawableList().isNotEmpty())
    }
}