/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MarkListBottomLineHelperTest
 * Description:
 * Version: 1.0
 * Date: 2023/8/3
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/3 1.0 create
 */

package com.soundrecorder.playback.audio

import android.content.Context
import android.os.Build
import android.view.LayoutInflater
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.PlaybackActivity
import com.soundrecorder.playback.databinding.FragmentPlaybackAudioBinding
import com.soundrecorder.playback.databinding.IncludeAudioFragmentBottomButtonBinding
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.R
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
class MarkListBottomLineHelperTest {

    private var activityController: ActivityController<PlaybackActivity>? = null
    private var activity: PlaybackActivity? = null
    private var context: Context? = null
    private var fragment: PlaybackAudioFragment? = null
    private var layoutBinding: FragmentPlaybackAudioBinding? = null
    private var bottomButtonBinding: IncludeAudioFragmentBottomButtonBinding? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        activityController =
            Robolectric.buildActivity(PlaybackActivity::class.java).create().resume()
        activity = activityController?.get()
        layoutBinding = FragmentPlaybackAudioBinding.bind(
            LayoutInflater.from(activity).inflate(R.layout.fragment_playback_audio, null, false)
        ).apply {
            bottomButtonBinding = IncludeAudioFragmentBottomButtonBinding.bind(root)
        }
    }

    @After
    fun tearDown() {
        fragment = null
        activityController?.stop()
        activity = null
        activityController = null
    }

    @Test
    fun should_notNull_when_fragmentCreate() {
        Assert.assertNotNull(bottomButtonBinding)
        Assert.assertNotNull(layoutBinding)
    }

    @Test
    fun should_hideLine_when_handleBottomLineVisible() {
        val bottomButtonBinding = bottomButtonBinding ?: return
        val layoutBinding = layoutBinding ?: return
        layoutBinding.markListView.run {
            layoutManager = LinearLayoutManager(context)
        }
        val markBottomLineHelper = MarkListBottomLineHelper(
            bottomButtonBinding.viewLineMarkBottom,
            bottomButtonBinding.spaceBottomViewTop,
            layoutBinding.markListView
        )
        layoutBinding.markListView.isVisible = false
        markBottomLineHelper.handleBottomLineVisible("")
        Assert.assertFalse(bottomButtonBinding.viewLineMarkBottom.isVisible)

        layoutBinding.markListView.isVisible = true
        markBottomLineHelper.handleBottomLineVisible("")
        Assert.assertFalse(bottomButtonBinding.viewLineMarkBottom.isVisible)
    }

    @Test
    fun should_null_when_onDestroyView() {
        val bottomButtonBinding = bottomButtonBinding ?: return
        Assert.assertNotNull(bottomButtonBinding.viewLineMarkBottom)
        Assert.assertNotNull(bottomButtonBinding.spaceBottomViewTop)

        val markBottomLineHelper = MarkListBottomLineHelper(
            bottomButtonBinding.viewLineMarkBottom,
            bottomButtonBinding.spaceBottomViewTop,
            layoutBinding?.markListView
        )
        markBottomLineHelper.onDestroyView()
        Assert.assertNull(Whitebox.getInternalState(markBottomLineHelper, "bottomLine"))
        Assert.assertNull(Whitebox.getInternalState(markBottomLineHelper, "bottomLinePanel"))
    }
}