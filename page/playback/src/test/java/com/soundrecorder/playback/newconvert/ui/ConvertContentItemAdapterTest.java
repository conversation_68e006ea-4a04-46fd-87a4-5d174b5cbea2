/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui;

import android.app.Activity;
import android.os.Build;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.utils.MarkSerializUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ConvertContentItemAdapterTest {

    private Activity mContext;
    private ConvertContentItemAdapter mAdapter;
    private List<ConvertContentItem> data = new ArrayList<>();
    private String TEXT = "AABBCCDDEE";

    @Before
    public void setUp() {
        mContext = Robolectric.buildActivity(PlaybackActivity.class).get();
        mAdapter = new ConvertContentItemAdapter();
        for (int i = 0; i < 10; i++) {
            ConvertContentItem item = new ConvertContentItem();
            List<ConvertContentItem.SubSentence> sentences = new ArrayList<>();
            for (int j = 0; j < 2; j++) {
                ConvertContentItem.SubSentence subSentence = new ConvertContentItem.SubSentence(0,0,0,"",false,false,null);
                List<MarkDataBean> markDataBeans = new ArrayList<>();
                for (int k = 0; k < 2; k++) {
                    MarkDataBean markDataBean = new MarkDataBean(0, MarkSerializUtil.VERSION_PICTURE);
                    if (k == 0) {
                        markDataBean.setPictureFilePath(TEXT);
                        markDataBean.setVersion(MarkSerializUtil.VERSION_PICTURE);
                    } else {
                        markDataBean.setPictureFilePath("");
                        markDataBean.setVersion(MarkSerializUtil.VERSION_NEW);
                    }
                    markDataBeans.add(markDataBean);
                }
                subSentence.setMarkDataBeanList(markDataBeans);
                sentences.add(subSentence);
            }
            item.setListSubSentence(sentences);
            data.add(item);
        }
        mAdapter.setData(data);
    }

    @Test
    public void should_returnExpectInt_when_onCreateViewHolder() {
        RecyclerView.ViewHolder holder_list = null;
        try {
            holder_list = Whitebox.invokeMethod(mAdapter, "onCreateViewHolder", new RelativeLayout(mContext), ConvertContentItemAdapter.TYPE_LIST);
            Assert.assertTrue(holder_list instanceof ConvertContentItemAdapter.ShareConvertContentTextViewHolder);
            RecyclerView.ViewHolder holder_head = Whitebox.invokeMethod(mAdapter, "onCreateViewHolder", new RelativeLayout(mContext), ConvertContentItemAdapter.TYPE_HEADER);
            Assert.assertTrue(holder_head instanceof ConvertContentItemAdapter.ConvertShareTxtHeaderViewHolder);
            RecyclerView.ViewHolder holder_foot = Whitebox.invokeMethod(mAdapter, "onCreateViewHolder", new RelativeLayout(mContext), ConvertContentItemAdapter.TYPE_FOOTER);
            Assert.assertTrue(holder_foot != null);
            RecyclerView.ViewHolder search_holder_list = Whitebox.invokeMethod(mAdapter, "onCreateViewHolder", new RelativeLayout(mContext), ConvertContentItemAdapter.TYPE_LIST);
            Assert.assertTrue(search_holder_list instanceof ConvertContentItemAdapter.ShareConvertContentTextViewHolder);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void should_returnExpectInt_when_getItemCount(){
        Assert.assertTrue(data.size() + 2 == mAdapter.getItemCount());
    }

    @Test
    public void should_returnExpectInt_when_getHeaderSize() {
        Assert.assertTrue(1 == mAdapter.getHeaderSize());
    }

    @Test
    public void should_returnExpectInt_when_getItemViewType() {
        Assert.assertTrue(ConvertContentItemAdapter.TYPE_HEADER == mAdapter.getItemViewType(0));
        Assert.assertTrue(ConvertContentItemAdapter.TYPE_LIST == mAdapter.getItemViewType(5));
        Assert.assertTrue(ConvertContentItemAdapter.TYPE_FOOTER == mAdapter.getItemViewType(data.size() + 1));
    }

    @Test
    public void should_returnExpectInt_when_setHeaderData() {
        mAdapter.setHeaderData("测试1", "2022-12-20", "测试2", "角色名");
        String playName = Whitebox.getInternalState(mAdapter, "mPlayName");
        String createTime = Whitebox.getInternalState(mAdapter, "mCreateTime");
        String subject = Whitebox.getInternalState(mAdapter, "mSubject");
        String roles = Whitebox.getInternalState(mAdapter, "mRoles");
        Assert.assertEquals(playName, "测试1");
        Assert.assertEquals(createTime, "2022-12-20");
        Assert.assertEquals(subject, "测试2");
        Assert.assertEquals(roles, "角色名");
    }



   @Test
    public void should_returnExpectInt_when_onAttachedToRecyclerView() {
        RecyclerView recyclerView = new RecyclerView(mContext);
        mAdapter.onAttachedToRecyclerView(recyclerView);
        int headCount = recyclerView.getRecycledViewPool().getRecycledViewCount(ConvertContentItemAdapter.TYPE_HEADER);
        int footCount = recyclerView.getRecycledViewPool().getRecycledViewCount(ConvertContentItemAdapter.TYPE_FOOTER);
        Assert.assertEquals(0,headCount);
        Assert.assertEquals(0,footCount);
    }

    @Test
    public void should_returnExpectInt_when_onViewAttachedToWindow() {
        RelativeLayout viewGroup = new RelativeLayout(mContext);
        ConvertContentItemAdapter.ShareConvertContentTextViewHolder shareVH = (ConvertContentItemAdapter.ShareConvertContentTextViewHolder)
                mAdapter.onCreateViewHolder(viewGroup, ConvertContentItemAdapter.TYPE_LIST);
        mAdapter.onViewAttachedToWindow(shareVH);
        Assert.assertTrue(shareVH.getItemTextContent().isEnabled());


        mAdapter.onViewAttachedToWindow(new RecyclerView.ViewHolder(viewGroup) {
        });

    }

    @Test
    public void should_when_onViewDetachedFromWindow() {
        RelativeLayout viewGroup = new RelativeLayout(mContext);
        RecyclerView.ViewHolder holder = new RecyclerView.ViewHolder(viewGroup) {};
        mAdapter.onViewDetachedFromWindow(holder);
    }



    @Test
    public void should_returnExpectInt_when_onDetachedFromRecyclerView() {
        RecyclerView recyclerView = new RecyclerView(mContext);
        List<ConvertContentItemAdapter.ShareConvertContentTextViewHolder> shareHolderList = new ArrayList<>();
        ConvertContentItemAdapter.ShareConvertContentTextViewHolder shareConvertContentTextViewHolder = Mockito.mock(ConvertContentItemAdapter.ShareConvertContentTextViewHolder.class);
        shareHolderList.add(shareConvertContentTextViewHolder);


        Whitebox.setInternalState(mAdapter, "shareViewHolders", shareHolderList);

        mAdapter.onDetachedFromRecyclerView(recyclerView);

        List<ConvertContentItemAdapter.ShareConvertContentTextViewHolder> shareHolders = Whitebox.getInternalState(mAdapter, "shareViewHolders");
        Assert.assertTrue(shareHolders.size() == 0);
    }


    @Test
    public void should_returnExpectInt_when_onBindViewHolder(){
        Whitebox.setInternalState(mAdapter, "mPlayName", TEXT);
        Whitebox.setInternalState(mAdapter, "mCreateTime", "2022");
        Whitebox.setInternalState(mAdapter, "mSubject", "录音测试");
        ConvertContentItemAdapter.ConvertShareTxtHeaderViewHolder shareTxtHeaderViewHolder = Mockito.mock(ConvertContentItemAdapter.ConvertShareTxtHeaderViewHolder.class);
        Mockito.doReturn(new TextView(mContext)).when(shareTxtHeaderViewHolder).getPlayName();
        Mockito.doReturn(new TextView(mContext)).when(shareTxtHeaderViewHolder).getCreateTime();
        Mockito.doReturn(new TextView(mContext)).when(shareTxtHeaderViewHolder).getSubject();
        mAdapter.onBindViewHolder(shareTxtHeaderViewHolder, 1);
        Assert.assertEquals(shareTxtHeaderViewHolder.getPlayName().getText(), TEXT);
    }


}
