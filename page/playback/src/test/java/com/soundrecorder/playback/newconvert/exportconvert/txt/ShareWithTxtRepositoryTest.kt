/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import java.text.SimpleDateFormat
import java.util.*

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowFeatureOption::class]
)
class ShareWithTxtRepositoryTest {

    private var mContext: Context? = null
    private val repository = ShareWithTxtRepository()

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext<Context>()
    }

    @Test
    fun getTimeStringTest() {
        val date = Date()

        val timeString = repository.getTimeString(date)
        Assert.assertNotEquals(timeString, null)
    }

    @Test
    fun getConvertFileNameTest() {
        var convertFileName = repository.getConvertFileName(null, BaseApplication.getAppContext())
        Assert.assertNull(convertFileName)

        val convertRecord = ConvertRecord()
        convertFileName = repository.getConvertFileName(convertRecord, BaseApplication.getAppContext())
        Assert.assertNull(convertFileName)

        convertRecord.convertTextfilePath = null
        convertFileName = repository.getConvertFileName(convertRecord, BaseApplication.getAppContext())
        Assert.assertNull(convertFileName)

        convertRecord.convertTextfilePath = ""
        convertFileName = repository.getConvertFileName(convertRecord, BaseApplication.getAppContext())
        Assert.assertNull(convertFileName)

        convertRecord.convertTextfilePath = "/data/user/0/com.coloros.soundrecorder/files/convert/convert_103_标准录音 3 1.txt"
        convertFileName = repository.getConvertFileName(convertRecord, BaseApplication.getAppContext())
        print("convertFileName = $convertFileName")
        Assert.assertNotNull(convertFileName)
    }

    @Test
    fun getFileNameTest() {
        var convertFileName = repository.getFileName(null)
        Assert.assertEquals(convertFileName, "")

        val convertRecord = ConvertRecord()
        convertRecord.mediaPath = null
        Assert.assertEquals(convertFileName, "")

        convertFileName = repository.getFileName(convertRecord)
        Assert.assertEquals(convertFileName, "")

        convertRecord.mediaPath = "/data/user/0/com.coloros.soundrecorder/files/convert/convert_103_标准录音 3 1.txt"
        convertFileName = repository.getFileName(convertRecord)
        Assert.assertNotEquals(convertFileName, "")
    }

    @Test
    fun getRecordFileCreateDateTest() {
        val currentTimeMillis = System.currentTimeMillis()
        var recordFileCreateDate = repository.getRecordFileCreateDate(-1)

        Assert.assertNotNull(recordFileCreateDate)

        recordFileCreateDate = repository.getRecordFileCreateDate(currentTimeMillis)
        Assert.assertNotNull(recordFileCreateDate)

        recordFileCreateDate = repository.getRecordFileCreateDate("-99999999999999999".toLong())
        Assert.assertNotNull(recordFileCreateDate)
    }

    @Test
    fun getSaveTxtToLocalDateTest() {
        var saveTxtToLocalDate = repository.getSaveTxtToLocalDate(null)
        Assert.assertEquals(saveTxtToLocalDate, "")

        val date = Date()
        saveTxtToLocalDate = repository.getSaveTxtToLocalDate(date)
        val format = SimpleDateFormat("yyyy-MM-dd HH-mm-ss", Locale.getDefault()).format(date)
        Assert.assertEquals(saveTxtToLocalDate, format)
    }

    @Test
    fun getRolesStringTest() {
        val convertContentItem = ConvertContentItem()
        val mutableListOf = arrayListOf<ConvertContentItem>()

        var rolesString = repository.getRolesString(mutableListOf)
        Assert.assertEquals(rolesString, "")

        mutableListOf.add(convertContentItem)
        rolesString = repository.getRolesString(mutableListOf)
        Assert.assertEquals(rolesString, "")


        convertContentItem.roleName = "role1"
        rolesString = repository.getRolesString(mutableListOf)
        val stringBuilder = StringBuilder("role1,  ")
        val toString = stringBuilder.deleteCharAt(stringBuilder.lastIndexOf(",")).toString()
        Assert.assertEquals(rolesString, toString)
    }

    @Test
    fun getItemsContentString() {
        val convertContentItem = ConvertContentItem()
        val mutableListOf = arrayListOf<ConvertContentItem>()

        val builder = StringBuilder()
        var itemsContentString = repository.getItemsContentString(mutableListOf)
        Assert.assertEquals(itemsContentString, builder.toString())

        builder.append(convertContentItem.textContent)
        mutableListOf.add(convertContentItem)
        itemsContentString = repository.getItemsContentString(mutableListOf)
        Assert.assertEquals(itemsContentString, builder.toString())
    }
}