/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.fragment.app.FragmentManager
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import org.junit.*
import java.util.*


@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowFeatureOption::class,
        ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
class ShareWithTxtActivityTest {
    private val mActivityController: ActivityController<ShareWithTxtActivity> =
        Robolectric.buildActivity(ShareWithTxtActivity::class.java)
    private var mActivity: ShareWithTxtActivity? = null

    @Before
    fun setUp() {
    }

    @After
    fun tearDown() {
        mActivity = null
    }

    @Test
    fun attachFragmentsTest() {
        mActivity = mActivityController.create().resume().get()
        var bundle: Bundle? = null
        Whitebox.invokeMethod<ShareWithTxtFragment?>(mActivity, "attachFragments", bundle)
        Assert.assertNotNull(Whitebox.getInternalState(mActivity, "mShareWithTxtFragment"))

        bundle = Bundle()
        bundle.putLong("mediaRecordId", 100)
        Whitebox.invokeMethod<ShareWithTxtFragment?>(mActivity, "attachFragments", bundle)
        Assert.assertNotNull(null, Whitebox.getInternalState(mActivity, "mShareWithTxtFragment"))
    }

    @Test
    fun getShareWithTxtFragmentTest() {
        mActivity = mActivityController.create().resume().get()
        Assert.assertNotNull(Whitebox.invokeMethod<ShareWithTxtFragment?>(mActivity, "getShareWithTxtFragment"))

        val spy = PowerMockito.spy(mActivity)
        val mock = Mockito.mock(FragmentManager::class.java)
        Mockito.`when`(spy?.supportFragmentManager).thenReturn(mock)
        val invokeMethod = Whitebox.invokeMethod<ShareWithTxtFragment?>(spy, "getShareWithTxtFragment")
        Assert.assertNull(invokeMethod)
    }

    @Test
    fun initBundleTest() {
        mActivity = mActivityController.create().get()
        val spy = PowerMockito.spy(mActivity)
        var intent: Intent? = null
        Mockito.`when`(spy?.intent).thenReturn(intent)

        Whitebox.invokeMethod<ShareWithTxtFragment?>(spy, "initBundle")

        intent = Intent()
        val bundle = Bundle()
        bundle.putLong("mediaRecordId", 1015231)
        bundle.putLong("createTime", Date().time)
        intent.putExtras(bundle)
        Mockito.`when`(spy?.intent).thenReturn(intent)
        Whitebox.invokeMethod<ShareWithTxtFragment?>(spy, "initBundle")
    }
}