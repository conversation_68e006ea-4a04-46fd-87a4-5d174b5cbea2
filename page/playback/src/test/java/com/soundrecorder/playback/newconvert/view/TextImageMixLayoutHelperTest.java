/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view;



import android.content.Context;
import android.os.Build;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.imageload.ImageLoadData;
import com.soundrecorder.common.databean.markdata.MarkDataBean;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;

import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import kotlin.Pair;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment;

@RunWith(AndroidJUnit4.class)
//@RunWith(PowerMockRunner.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
@PrepareForTest(TextImageMixLayoutHelper.class)
public class TextImageMixLayoutHelperTest {

    @Mock
    private Context mContext;
    private TextImageMixLayoutHelper mixLayoutHelper;
    private TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr drawAttr;


    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mixLayoutHelper = new TextImageMixLayoutHelper();
        drawAttr = new TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr(0,0,0,0,0,0f, com.soundrecorder.common.R.color.text_color, 0);
    }

    @Test
    public void should_return_null_checkAndAddBackgroundTextView_when_input_currentItem_null(){
        View parentView = new RecyclerView(mContext);
        ConvertContentItem convertContentItem = new ConvertContentItem();
        View view = mixLayoutHelper.checkAndAddBackgroundTextView(mContext, 0, null, convertContentItem, null, parentView, false, null, null, drawAttr);
        assert view == null;
    }


    @Test
    public void should_return_null_checkAndAddBackgroundTextView_when_input_convert_content_item_null(){
        View parentView = new RecyclerView(mContext);
        ConvertContentItem.TextItemMetaData textOrImageItem = new ConvertContentItem.TextItemMetaData();
        View view = mixLayoutHelper.checkAndAddBackgroundTextView(mContext, 0, textOrImageItem, null, null, parentView, false, null, null, drawAttr);
        assert view == null;
    }


    @Test
    public void should_return_backgroudTextView_checkAndAddBackgroundTextView_when_input_not_null() throws Exception {
        View parentView = PowerMockito.mock(RecyclerView.class);
        ConvertContentItem.TextItemMetaData textOrImageItem = new ConvertContentItem.TextItemMetaData();
        ConvertContentItem convertContentItem = new ConvertContentItem();
        /*TextImageMixLayoutHelper helper = Mockito.mock(TextImageMixLayoutHelper.class);
        Mockito.doNothing().when(helper).setItemContentSpannable(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyBoolean());
        Mockito.doNothing().when(helper).switchContentBackground(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt());
        helper.setItemContentSpannable(null, null, textOrImageItem, 0, false);*/

        TextImageMixLayoutHelper helper = PowerMockito.spy(mixLayoutHelper);
        //PowerMockito.doNothing().when(helper, "setItemContent", Mockito.any(), Mockito.any(), Mockito.anyInt());
        PowerMockito.doNothing().when(helper).setItemContent(Mockito.any(), Mockito.any(), Mockito.anyInt());
        PowerMockito.doNothing().when(helper).setItemContentSpannable(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyBoolean());
        PowerMockito.doNothing().when(helper).switchContentBackground(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyInt());

        View view = helper.checkAndAddBackgroundTextView(mContext, 0, textOrImageItem, convertContentItem, null, parentView, false, null, null, drawAttr);
        assert view instanceof BackgroundTextView;
    }


    @Test
    public void should_return_null_checkAndAddImageView_when_input_currentItem_null(){
        View parentView = new RecyclerView(mContext);
        ConvertContentItem convertContentItem = new ConvertContentItem();
        Pair<View, ImageLoadData> result = mixLayoutHelper.checkAndAddImageView(mContext, null, convertContentItem, null, parentView, false, null, drawAttr);
        assert result == null;
    }


    @Test
    public void should_return_null_checkAndAddImageView_when_input_convert_content_itemnull(){
        View parentView = new RecyclerView(mContext);
        ConvertContentItem.ImageMetaData textOrImageItem = new ConvertContentItem.ImageMetaData();
        Pair<View, ImageLoadData> result = mixLayoutHelper.checkAndAddImageView(mContext, textOrImageItem, null, null, parentView, false, null, drawAttr);
        assert result == null;
    }


    @Ignore
    @Test
    public void should_return_checkAndAddImageView_checkAndAddBackgroundTextView_when_input_not_null() throws Exception {
        View parentView = new RecyclerView(mContext);
        ConvertContentItem.ImageMetaData textOrImageItem = new ConvertContentItem.ImageMetaData(new MarkDataBean(0, 0));
        ConvertContentItem convertContentItem = new ConvertContentItem();
        TextImageMixLayoutHelper helper = PowerMockito.spy(mixLayoutHelper);
        PowerMockito.doNothing().when(helper).loadImageView(Mockito.any(), Mockito.any());
        //PowerMockito.doNothing().when(helper, "loadImageView", Mockito.any(), Mockito.any());
        /*PowerMockito.mockStatic(ImageWithHeightCaculateUtil.class);
        PowerMockito.when(ImageWithHeightCaculateUtil.INSTANCE.caculateImageViewWithAndHeight(Mockito.any(), Mockito.any())).thenAnswer(new Answer<ImageWithHeightCaculateUtil.ImageViewShowConfig>() {
            @Override
            public ImageWithHeightCaculateUtil.ImageViewShowConfig answer(InvocationOnMock invocation) {
                return new ImageWithHeightCaculateUtil.ImageViewShowConfig(720, 180, false);
            }
        });*/
        Pair<View, ImageLoadData> result = mixLayoutHelper.checkAndAddImageView(mContext, textOrImageItem, convertContentItem, null, parentView, false, null, drawAttr);
        assert result != null;
    }


}
