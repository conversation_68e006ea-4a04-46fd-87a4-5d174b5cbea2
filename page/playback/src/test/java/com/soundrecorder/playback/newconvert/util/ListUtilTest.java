/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.util;


import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowRecorderLogger;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowRecorderLogger.class, ShadowFeatureOption.class})
public class ListUtilTest {

    private ConvertContentItem convertContentItem;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        convertContentItem = new ConvertContentItem();
        convertContentItem.setRoleName("ITEM_ONE");
    }

    @Test
    public void should_equals_when_getDistinctRoleName() {
        List<String> resultList = new ArrayList<>();
        resultList = ListUtil.getDistinctRoleName(null);
        Assert.assertEquals(0, resultList.size());
        List<ConvertContentItem> convertContentItemList = new ArrayList<>();
        resultList = ListUtil.getDistinctRoleName(convertContentItemList);
        Assert.assertEquals(0, resultList.size());
        convertContentItemList.add(convertContentItem);
        resultList = ListUtil.getDistinctRoleName(convertContentItemList);
        Assert.assertEquals(convertContentItem.getRoleName() ,resultList.get(0));
        Assert.assertEquals(1, resultList.size());
        convertContentItemList.add(convertContentItem);
        resultList = ListUtil.getDistinctRoleName(convertContentItemList);
        Assert.assertEquals(1, resultList.size());
    }

    @Test
    public void should_equals_when_getTimeString() {
        BaseApplication.sIsRTLanguage = true;
        String result = ListUtil.getTimeString(1L, "标准录音1.mp3");
        Assert.assertNotEquals("", result);

        BaseApplication.sIsRTLanguage = false;
        result = ListUtil.getTimeString(1L, "标准录音1.mp3");
        Assert.assertNotEquals("", result);
    }

    @Test
    public void should_equals_when_getMetaDataList() {
        List list = new ArrayList<>();

        ConvertContentItem item1 = new ConvertContentItem();
        List textImageItem1 = new ArrayList<>();
        textImageItem1.add(new ConvertContentItem.TextItemMetaData());
        item1.setMTextOrImageItems(textImageItem1);
        list.add(item1);

        ConvertContentItem item2 = new ConvertContentItem();
        list.add(item2);
        Assert.assertEquals(1, ListUtil.getTextMetaDataList(list).size());
    }

    @Test
    public void should_equals_when_getTextMetaDataList() {
        List list = new ArrayList<>();

        ConvertContentItem item1 = new ConvertContentItem();
        List textImageItem1 = new ArrayList<>();
        textImageItem1.add(new ConvertContentItem.TextItemMetaData());
        item1.setMTextOrImageItems(textImageItem1);
        list.add(item1);

        ConvertContentItem item2 = new ConvertContentItem();
        list.add(item2);
        Assert.assertNotNull(ListUtil.getTextMetaDataList(list).get(0));
    }
}
