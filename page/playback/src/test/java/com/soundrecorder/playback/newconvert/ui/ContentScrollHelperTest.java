/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui;

import android.content.Context;
import android.os.Build;
import android.view.ViewGroup;

import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;

import kotlin.Pair;

import com.soundrecorder.playback.PlaybackActivityViewModel;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ContentScrollHelperTest {

    private Context mContext;
    private ContentScrollHelper mContentScrollHelper;
    private PlaybackActivityViewModel mModel;
    private PlaybackConvertViewModel mConvertViewModel;

    @Before
    public void setUp() {
        mContext = BaseApplication.getApplication();
        ViewGroup viewGroup = new ViewGroup(mContext) {
            @Override
            protected void onLayout(boolean changed, int l, int t, int r, int b) {

            }
        };
        mModel = new PlaybackActivityViewModel();
        mConvertViewModel = new PlaybackConvertViewModel(BaseApplication.getApplication());
        COUIRecyclerView recyclerView = Mockito.mock(COUIRecyclerView.class);
        mContentScrollHelper = new ContentScrollHelper(viewGroup, mModel, mConvertViewModel, recyclerView);
    }

    @Test
    public void should_returnExpectInt_when_prepareToScroll() throws Exception {
        List<Pair<Integer, ConvertContentItem.TextItemMetaData>> list = new ArrayList<>();
        ConvertContentItem.TextItemMetaData itemMetaData = new ConvertContentItem.TextItemMetaData();
        List<ConvertContentItem.SubSentence> subSentences = new ArrayList<>();
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        itemMetaData.setTextParagraph(subSentences);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair1 = new Pair<>(1, itemMetaData);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair2 = new Pair<>(2, itemMetaData);
        list.add(pair1);
        list.add(pair2);
        Whitebox.invokeMethod(mContentScrollHelper, "prepareToScroll", list, 1, 2, itemMetaData);
    }

    @Test
    public void should_returnExpectInt_when_findFocusPositionCursor() throws Exception {
        List<Pair<Integer, ConvertContentItem.TextItemMetaData>> list = new ArrayList<>();
        ConvertContentItem.TextItemMetaData itemMetaData = new ConvertContentItem.TextItemMetaData();
        List<ConvertContentItem.SubSentence> subSentences = new ArrayList<>();
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        itemMetaData.setTextParagraph(subSentences);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair1 = new Pair<>(1, itemMetaData);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair2 = new Pair<>(2, itemMetaData);
        Whitebox.invokeMethod(mContentScrollHelper, "findFocusPositionCursor", list, 0L, itemMetaData);
        list.add(pair1);
        list.add(pair2);
        Whitebox.invokeMethod(mContentScrollHelper, "findFocusPositionCursor", list, 0L, itemMetaData);
        Whitebox.invokeMethod(mContentScrollHelper, "findFocusPositionCursor", list, -1L, itemMetaData);
    }

    @Test
    public void should_returnExpectInt_when_getLastFocusItem() throws Exception {
        List<Pair<Integer, ConvertContentItem.TextItemMetaData>> list = new ArrayList<>();
        ConvertContentItem.TextItemMetaData itemMetaData = new ConvertContentItem.TextItemMetaData();
        List<ConvertContentItem.SubSentence> subSentences = new ArrayList<>();
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        itemMetaData.setTextParagraph(subSentences);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair1 = new Pair<>(1, itemMetaData);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair2 = new Pair<>(2, itemMetaData);
        list.add(pair1);
        list.add(pair2);
        Whitebox.invokeMethod(mContentScrollHelper, "getLastFocusItem", list);
        mModel.setMLastFocusPosition(1);
        Whitebox.invokeMethod(mContentScrollHelper, "getLastFocusItem", list);
    }

    @Test
    public void should_returnExpectInt_when_getListIndexForPosition() throws Exception {
        List<Pair<Integer, ConvertContentItem.TextItemMetaData>> list = new ArrayList<>();
        ConvertContentItem.TextItemMetaData itemMetaData = new ConvertContentItem.TextItemMetaData();
        List<ConvertContentItem.SubSentence> subSentences = new ArrayList<>();
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        itemMetaData.setTextParagraph(subSentences);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair1 = new Pair<>(1, itemMetaData);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair2 = new Pair<>(2, itemMetaData);
        list.add(pair1);
        list.add(pair2);
        Whitebox.invokeMethod(mContentScrollHelper, "getListIndexForPosition", list, 1);
    }

    @Test
    public void should_returnExpectInt_when_findSubFocusPositionCursor() throws Exception {
        List<Pair<Integer, ConvertContentItem.TextItemMetaData>> list = new ArrayList<>();
        ConvertContentItem.TextItemMetaData itemMetaData = new ConvertContentItem.TextItemMetaData();
        List<ConvertContentItem.SubSentence> subSentences = new ArrayList<>();
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        itemMetaData.setTextParagraph(subSentences);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair1 = new Pair<>(1, itemMetaData);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair2 = new Pair<>(2, itemMetaData);
        list.add(pair1);
        list.add(pair2);
        Whitebox.invokeMethod(mContentScrollHelper, "findSubFocusPositionCursor", list, 0L, 1);
    }

    @Test
    public void should_returnExpectInt_when_getScrollDirection() throws Exception {
        mContentScrollHelper.updateContentBackButton(true);
        mContentScrollHelper.updateContentBackButton(false);
    }

    @Test
    public void should_returnExpectInt_when_scrollToLastPositionByManualSentence() throws Exception {
        Whitebox.invokeMethod(mContentScrollHelper, "scrollToLastPositionByManualSentence");
        mModel.setMLastFocusPosition(1);
        Whitebox.invokeMethod(mContentScrollHelper, "scrollToLastPositionByManualSentence");
        mModel.setMLastSubFocusPosition(1);
        List<Pair<Integer, ConvertContentItem.TextItemMetaData>> list = new ArrayList<>();
        ConvertContentItem.TextItemMetaData itemMetaData = new ConvertContentItem.TextItemMetaData();
        List<ConvertContentItem.SubSentence> subSentences = new ArrayList<>();
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        subSentences.add(Mockito.mock(ConvertContentItem.SubSentence.class));
        itemMetaData.setTextParagraph(subSentences);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair1 = new Pair<>(1, itemMetaData);
        Pair<Integer, ConvertContentItem.TextItemMetaData> pair2 = new Pair<>(2, itemMetaData);
        list.add(pair1);
        list.add(pair2);
        mConvertViewModel.setMTextItems(list);
        Whitebox.invokeMethod(mContentScrollHelper, "scrollToLastPositionByManualSentence");
    }
}
