/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: EditViewAnimationControlTest
 * Description:
 * Version: 1.0
 * Date: 2023/7/29
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/7/29 1.0 create
 */

package com.soundrecorder.editrecord.ui.animation

import android.animation.AnimatorSet
import android.os.Build
import androidx.core.view.isVisible
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.splitwindow.FoldingWindowObserver
import com.soundrecorder.editrecord.EditRecordActivity
import com.soundrecorder.editrecord.databinding.EditRecordLayoutBinding
import com.soundrecorder.editrecord.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption
import com.soundrecorder.editrecord.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.editrecord.ui.EditRecordFragment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
class EditViewAnimationControlTest {
    private var controller: ActivityController<EditRecordActivity>? = null
    private var activity: EditRecordActivity? = null
    private var fragment: EditRecordFragment? = null
    private var editControl: EditViewAnimationControl? = null
    private var layoutBinding: EditRecordLayoutBinding? = null

    @Before
    fun setUp() {
        controller = Robolectric.buildActivity(EditRecordActivity::class.java)
        activity = controller?.create()?.resume()?.get()
        fragment =
            activity?.supportFragmentManager?.fragments?.getOrNull(0) as? EditRecordFragment
        editControl = Whitebox.getInternalState(fragment, "viewAnimationControl")
        layoutBinding = Whitebox.getInternalState(fragment, "dataBindUtil")
    }

    @After
    fun tearDown() {
        fragment = null
        controller?.stop()
        activity = null
        controller = null
    }

    @Test
    fun should_notNull_when_fragmentCreate() {
        Assert.assertNotNull(editControl)
        Assert.assertNotNull(layoutBinding)
    }

    @Test
    fun should_correct_when_getWavePercent() {
        editControl?.foldWindowType = FoldingWindowObserver.SCREEN_VERTICAL_EXPAND
        var percent = editControl?.getWavePercent(true)
        Assert.assertEquals(Whitebox.getInternalState(editControl, "wavePercentShowMark"), percent)

        percent = editControl?.getWavePercent(false)
        Assert.assertEquals(Whitebox.getInternalState(editControl, "wavePercentDefault"), percent)

        editControl?.foldWindowType = FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER
        percent = editControl?.getWavePercent(true)
        Assert.assertEquals(Whitebox.getInternalState(editControl, "wavePercentHover"), percent)
    }

    @Test
    fun should_correct_when_doAnimateTitleViewToTop() {
        val binding = layoutBinding ?: return
        binding.timerView.isVisible = true
        binding.layoutMarklist.isVisible = true
        editControl?.doAnimateTitleViewToTop(false, binding)
        Assert.assertTrue(binding.timerView.scaleX == 1F)
    }

    @Test
    fun should_correct_when_doAnimateTitleViewToCenter() {
        val binding = layoutBinding ?: return
        binding.timerView.isVisible = true
        binding.layoutMarklist.isVisible = true
        editControl?.doAnimateTitleViewToCenter(false, binding)
        Assert.assertTrue(binding.timerView.scaleX == 1F)
    }

    @Test
    fun should_correct_when_doAnimFoldStateChanged() {
        val binding = layoutBinding ?: return
        binding.layoutMarklist.isVisible = true
        editControl?.doAnimFoldStateChanged(binding, true)
        Assert.assertNotNull(
            Whitebox.getInternalState<AnimatorSet>(
                editControl,
                "showMarkAnimation"
            )
        )

        editControl?.doAnimFoldStateChanged(binding, false)
        Assert.assertFalse(binding.viewCenterDivider.isVisible)
        Assert.assertNotNull(
            Whitebox.getInternalState<AnimatorSet>(
                editControl,
                "hideMarkAnimation"
            )
        )
    }

    @Test
    fun should_null_when_release() {
        Whitebox.setInternalState(editControl, "showMarkAnimation", AnimatorSet())
        Whitebox.setInternalState(editControl, "hideMarkAnimation", AnimatorSet())

        editControl?.release()
        Assert.assertNull(Whitebox.getInternalState<AnimatorSet>(editControl, "showMarkAnimation"))
        Assert.assertNull(Whitebox.getInternalState<AnimatorSet>(editControl, "hideMarkAnimation"))
    }
}