/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderHeaderUtilsTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.utils;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.editrecord.shadows.ShadowFeatureOption;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class RecorderHeaderUtilsTest {


    @Test
    public void shouldNotNull_when_getADTSHeader() {
        byte[] aacHead = RecorderHeaderUtils.getADTSHeader(1000, 44100, 2, 1);
        Assert.assertNotNull(aacHead);
        Assert.assertEquals(7, aacHead.length);
    }

    @Test
    public void shouldNotNull_when_calWaveHeader() throws Exception{
        byte[] waveHead = RecorderHeaderUtils.calWaveHeader(1000, 44100, 2, 32);
        Assert.assertNotNull(waveHead);
    }
}
