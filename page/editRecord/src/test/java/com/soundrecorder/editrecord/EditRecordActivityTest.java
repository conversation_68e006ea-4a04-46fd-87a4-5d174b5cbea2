/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditRecordActivityTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord;

import android.content.Intent;
import android.os.Build;
import android.view.LayoutInflater;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowLog;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import androidx.fragment.app.Fragment;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.editrecord.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption;
import com.soundrecorder.editrecord.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.editrecord.ui.EditRecordFragment;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class EditRecordActivityTest {
    private ActivityController<EditRecordActivity> mController;
    private ShadowApplication mShadowApplication;

    @Before
    public void setUp() {
        mController = Robolectric.buildActivity(EditRecordActivity.class);
        mShadowApplication = ShadowApplication.getInstance();
    }

    @Test
    public void should_initView_when_onCreate() {
        EditRecordActivity activity = mController.get();
        List<Fragment> fragments = activity.getSupportFragmentManager().getFragments();
        assertEquals(0, fragments.size());
        mController.create().start().resume();
        fragments = activity.getSupportFragmentManager().getFragments();
        assertEquals(1, fragments.size());
        assertTrue(fragments.get(0) instanceof EditRecordFragment);
        EditRecordFragment editRecordFragment = (EditRecordFragment) fragments.get(0);
        editRecordFragment.onCreateView(LayoutInflater.from(activity), activity.findViewById(R.id.fl_content),null);
//        Whitebox.invokeMethod(editRecordFragment, "init", new Bundle());
        Intent intent = new Intent("on.file.delete.broadcast");
        Assert.assertFalse(mShadowApplication.hasReceiverForIntent(intent));
    }

    @After
    public void tearDown() {
        mController = null;
        mShadowApplication = null;
    }

}
