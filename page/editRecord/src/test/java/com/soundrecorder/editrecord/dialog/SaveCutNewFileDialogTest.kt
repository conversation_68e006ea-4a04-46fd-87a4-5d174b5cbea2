/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SaveCutNewFileDialogTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.dialog

import android.os.Build
import android.view.View
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.Record
import com.soundrecorder.editrecord.EditRecordActivity
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption
import com.soundrecorder.editrecord.views.dialog.SaveCutNewFileDialog
import org.junit.Assert.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SaveCutNewFileDialogTest {

    private var mActivity: EditRecordActivity? = null
    private var mSaveCutNewFileDialog: SaveCutNewFileDialog? = null
    private val content = "test"

    @Before
    fun setUp() {
        mActivity = Robolectric.buildActivity(EditRecordActivity::class.java).get()
        mSaveCutNewFileDialog = SaveCutNewFileDialog(mActivity!!, content, null)
    }

    @After
    fun tearDown() {
        mActivity = null
        mSaveCutNewFileDialog = null
    }

    @Test
    fun getMediaRecord() {
        val record = Record()
        mSaveCutNewFileDialog?.mediaRecord = record
        assertEquals(record, mSaveCutNewFileDialog?.mediaRecord)
    }

    @Test
    fun setMediaRecord() {
        val record = Record()
        mSaveCutNewFileDialog?.mediaRecord = record
        assertNotNull(mSaveCutNewFileDialog?.mediaRecord)
    }

    @Test
    fun onInitCustomView() {
        val dialog = Mockito.mock(SaveCutNewFileDialog::class.java)
        val view = View(mActivity)
        dialog?.onInitCustomView(view)
        Mockito.verify(dialog, Mockito.times(1)).onInitCustomView(view)
    }

    @Test
    fun getTitleText() {
        val titleText = mSaveCutNewFileDialog?.getTitleText()
        //assertEquals(R.string.saved_cut_file_title, titleText)
    }

    @Test
    fun onSave() {
        val dialog = Mockito.mock(SaveCutNewFileDialog::class.java)
        dialog?.onSave()
        Mockito.verify(dialog, Mockito.times(1)).onSave()
    }

    @Test
    fun onCancel() {
        val dialog = Mockito.mock(SaveCutNewFileDialog::class.java)
        dialog?.onCancel()
        Mockito.verify(dialog, Mockito.times(1)).onCancel()
    }

    @Test
    fun getOriginalContent() {
        val originalContent = mSaveCutNewFileDialog?.getOriginalContent()
        assertEquals(content, originalContent)
    }
}