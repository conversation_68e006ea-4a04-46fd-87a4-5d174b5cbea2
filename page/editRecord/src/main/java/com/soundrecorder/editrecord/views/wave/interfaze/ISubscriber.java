/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ISubscriber
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.wave.interfaze;

public interface ISubscriber {

    void notifyStartTime();

    void notifyEndTime();

    void notifyTimeChangeByX(float x);

    boolean isTouchHandler(float x);

    void notifyUp();
}
