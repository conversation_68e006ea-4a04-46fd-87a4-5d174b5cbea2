/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ClipTaskViewModel
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.ui

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class ClipTaskViewModel : ViewModel() {
    companion object {
        const val CLIP_INIT_DIALOG = 0
        const val CLIP_SHOW_DIALOG = 1
        const val CLIP_DISMISS_DIALOG = 2
        const val CLIP_RELEASE_DIALOG = 3
        const val CLIP_ERROR_DIALOG = 4
    }

    val dialogStatus = MutableLiveData(CLIP_INIT_DIALOG)
    val dialogProgress = MutableLiveData(0)
}